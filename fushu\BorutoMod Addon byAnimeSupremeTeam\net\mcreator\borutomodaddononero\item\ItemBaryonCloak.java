/*      */ package net.mcreator.borutomodaddononero.item;
/*      */ 
/*      */ import com.google.common.collect.Multimap;
/*      */ import java.util.List;
/*      */ import java.util.UUID;
/*      */ import javax.annotation.Nullable;
/*      */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*      */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*      */ import net.minecraft.client.model.ModelBase;
/*      */ import net.minecraft.client.model.ModelBiped;
/*      */ import net.minecraft.client.model.ModelBox;
/*      */ import net.minecraft.client.model.ModelRenderer;
/*      */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*      */ import net.minecraft.client.util.ITooltipFlag;
/*      */ import net.minecraft.entity.Entity;
/*      */ import net.minecraft.entity.EntityLivingBase;
/*      */ import net.minecraft.entity.SharedMonsterAttributes;
/*      */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*      */ import net.minecraft.entity.player.EntityPlayer;
/*      */ import net.minecraft.init.MobEffects;
/*      */ import net.minecraft.inventory.EntityEquipmentSlot;
/*      */ import net.minecraft.item.Item;
/*      */ import net.minecraft.item.ItemArmor;
/*      */ import net.minecraft.item.ItemStack;
/*      */ import net.minecraft.nbt.NBTBase;
/*      */ import net.minecraft.nbt.NBTTagCompound;
/*      */ import net.minecraft.potion.PotionEffect;
/*      */ import net.minecraft.util.math.MathHelper;
/*      */ import net.minecraft.util.math.RayTraceResult;
/*      */ import net.minecraft.util.text.translation.I18n;
/*      */ import net.minecraft.world.World;
/*      */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*      */ import net.minecraftforge.client.model.ModelLoader;
/*      */ import net.minecraftforge.common.util.EnumHelper;
/*      */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*      */ import net.minecraftforge.fml.relauncher.Side;
/*      */ import net.minecraftforge.fml.relauncher.SideOnly;
/*      */ import net.narutomod.Chakra;
/*      */ import net.narutomod.Particles;
/*      */ import net.narutomod.potion.PotionChakraEnhancedStrength;
/*      */ import net.narutomod.potion.PotionReach;
/*      */ import net.narutomod.procedure.ProcedureUtils;
/*      */ 
/*      */ @Tag
/*      */ public class ItemBaryonCloak
/*      */   extends ElementsBorutomodaddononeroMod.ModElement {
/*      */   @ObjectHolder("borutomodaddononero:baryon_cloakbody")
/*   48 */   public static final Item body = null;
/*      */   @ObjectHolder("borutomodaddononero:baryon_cloakhelmet")
/*   50 */   public static final Item helmet = null;
/*      */   
/*   52 */   private final AttributeModifier CLOAK_MODIFIER = new AttributeModifier(UUID.fromString("e884e4a0-7f08-422d-9aac-119972cd764d"), "bijucloak.maxhealth", 180.0D, 0);
/*      */   
/*      */   public ItemBaryonCloak(ElementsBorutomodaddononeroMod instance) {
/*   55 */     super(instance, 577);
/*      */   }
/*      */ 
/*      */   
/*      */   public void initElements() {
/*   60 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("BARYON_CLOAK", "narutomod:sasuke_", 1024, new int[] { 1024, 1024, 1024, 1024 }, 0, null, 5.0F);
/*      */ 
/*      */     
/*   63 */     this.elements.items.add(() -> ((Item)(new ItemArmor(enuma, 0, EntityEquipmentSlot.HEAD)
/*      */         {
/*      */           @SideOnly(Side.CLIENT)
/*      */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/*   67 */             ModelBiped armorModel = new ModelBiped();
/*   68 */             armorModel.field_78116_c = (new ItemBaryonCloak.ModelBaryonCloak()).Head;
/*   69 */             armorModel.field_78117_n = living.func_70093_af();
/*   70 */             armorModel.field_78093_q = living.func_184218_aH();
/*   71 */             armorModel.field_78091_s = living.func_70631_g_();
/*   72 */             return armorModel;
/*      */           }
/*      */ 
/*      */           
/*      */           public void onArmorTick(World world, EntityPlayer player, ItemStack itemstack) {
/*   77 */             if (!world.field_72995_K && 
/*   78 */               ItemBaryonCloak.this.isWearingFullSet(player)) {
/*   79 */               Chakra.PathwayPlayer pathwayPlayer = Chakra.pathway(player);
/*   80 */               if (pathwayPlayer.consume(1.0D)) {
/*   81 */                 ItemBaryonCloak.applyEffects((EntityLivingBase)player, 1);
/*      */               } else {
/*   83 */                 player.field_71071_by.field_70460_b.set(EntityEquipmentSlot.HEAD.func_188454_b(), ItemStack.field_190927_a);
/*      */               } 
/*      */             } 
/*      */           }
/*      */ 
/*      */ 
/*      */           
/*      */           public void func_77663_a(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
/*   91 */             super.func_77663_a(itemstack, world, entity, par4, par5);
/*   92 */             if (world.field_72995_K && entity instanceof EntityPlayer) {
/*   93 */               if (!itemstack.func_77942_o()) {
/*   94 */                 itemstack.func_77982_d(new NBTTagCompound());
/*      */               }
/*   96 */               NBTTagCompound compound = itemstack.func_77978_p();
/*   97 */               if (!compound.func_74767_n("particlesSpawned")) {
/*   98 */                 for (int i = 0; i < 100; i++) {
/*   99 */                   this;
/*  100 */                   this;
/*  101 */                   this; Particles.spawnParticle(world, Particles.Types.SMOKE, entity.field_70165_t, entity.field_70163_u + 0.8D, entity.field_70161_v, 1, 0.0D, 0.0D, 0.0D, (field_77697_d.nextDouble() - 0.5D) * 1.0D, (field_77697_d.nextDouble() - 0.5D) * 1.0D, (field_77697_d
/*  102 */                       .nextDouble() - 0.5D) * 1.0D, new int[] { 553582592, 30, 0, 240, entity
/*  103 */                         .func_145782_y() });
/*      */                 } 
/*  105 */                 compound.func_74757_a("particlesSpawned", true);
/*      */               } 
/*      */             } 
/*  108 */             if (!world.field_72995_K && entity instanceof EntityPlayer) {
/*  109 */               EntityPlayer player = (EntityPlayer)entity;
/*  110 */               for (int i = 0; i < player.field_71071_by.field_70462_a.size(); i++) {
/*  111 */                 ItemStack stackInSlot = player.field_71071_by.func_70301_a(i);
/*  112 */                 if (stackInSlot != null && stackInSlot.func_77973_b() == ItemBaryonCloak.helmet) {
/*  113 */                   player.field_71071_by.func_70299_a(i, ItemStack.field_190927_a);
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           }
/*      */ 
/*      */           
/*      */           public int func_77612_l() {
/*  121 */             return 0;
/*      */           }
/*      */ 
/*      */           
/*      */           public boolean func_77645_m() {
/*  126 */             return false;
/*      */           }
/*      */ 
/*      */           
/*      */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/*  131 */             return ItemBaryonCloak.getTexture(stack);
/*      */           }
/*      */         }).func_77655_b("baryon_cloakhelmet").setRegistryName("baryon_cloakhelmet")).func_77637_a(null));
/*      */     
/*  135 */     this.elements.items.add(() -> ((Item)(new ItemArmor(enuma, 0, EntityEquipmentSlot.CHEST)
/*      */         {
/*      */           @SideOnly(Side.CLIENT)
/*      */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/*  139 */             ModelBiped armorModel = new ModelBiped();
/*  140 */             armorModel.field_78115_e = (new ItemBaryonCloak.ModelBaryonCloak()).Body;
/*  141 */             armorModel.field_178723_h = (new ItemBaryonCloak.ModelBaryonCloak()).RightArm;
/*  142 */             armorModel.field_178724_i = (new ItemBaryonCloak.ModelBaryonCloak()).LeftArm;
/*  143 */             armorModel.field_78117_n = living.func_70093_af();
/*  144 */             armorModel.field_78093_q = living.func_184218_aH();
/*  145 */             armorModel.field_78091_s = living.func_70631_g_();
/*  146 */             return armorModel;
/*      */           }
/*      */ 
/*      */           
/*      */           public void onArmorTick(World world, EntityPlayer player, ItemStack itemstack) {
/*  151 */             super.onArmorTick(world, player, itemstack);
/*  152 */             if (!world.field_72995_K && 
/*  153 */               ItemBaryonCloak.this.isWearingFullSet(player)) {
/*  154 */               Chakra.PathwayPlayer pathwayPlayer = Chakra.pathway(player);
/*  155 */               if (!pathwayPlayer.consume(1.0D)) {
/*  156 */                 player.field_71071_by.field_70460_b.set(EntityEquipmentSlot.CHEST.func_188454_b(), ItemStack.field_190927_a);
/*      */               }
/*      */             } 
/*      */           }
/*      */ 
/*      */ 
/*      */           
/*      */           public void func_77663_a(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
/*  164 */             super.func_77663_a(itemstack, world, entity, par4, par5);
/*  165 */             if (world.field_72995_K && entity instanceof EntityPlayer) {
/*  166 */               if (!itemstack.func_77942_o()) {
/*  167 */                 itemstack.func_77982_d(new NBTTagCompound());
/*      */               }
/*  169 */               NBTTagCompound compound = itemstack.func_77978_p();
/*  170 */               if (!compound.func_74767_n("particlesSpawned")) {
/*  171 */                 for (int i = 0; i < 100; i++) {
/*  172 */                   this;
/*  173 */                   this;
/*  174 */                   this; Particles.spawnParticle(world, Particles.Types.SMOKE, entity.field_70165_t, entity.field_70163_u + 0.8D, entity.field_70161_v, 1, 0.0D, 0.0D, 0.0D, (field_77697_d.nextDouble() - 0.5D) * 1.0D, (field_77697_d.nextDouble() - 0.5D) * 1.0D, (field_77697_d
/*  175 */                       .nextDouble() - 0.5D) * 1.0D, new int[] { 553582592, 30, 0, 240, entity
/*  176 */                         .func_145782_y() });
/*      */                 } 
/*  178 */                 compound.func_74757_a("particlesSpawned", true);
/*      */               } 
/*      */             } 
/*  181 */             if (!world.field_72995_K && entity instanceof EntityPlayer) {
/*  182 */               EntityPlayer player = (EntityPlayer)entity;
/*  183 */               for (int i = 0; i < player.field_71071_by.field_70462_a.size(); i++) {
/*  184 */                 ItemStack stackInSlot = player.field_71071_by.func_70301_a(i);
/*  185 */                 if (stackInSlot != null && stackInSlot.func_77973_b() == ItemBaryonCloak.body) {
/*  186 */                   player.field_71071_by.func_70299_a(i, ItemStack.field_190927_a);
/*      */                 }
/*      */               } 
/*      */             } 
/*      */           }
/*      */ 
/*      */           
/*      */           public Multimap<String, AttributeModifier> getAttributeModifiers(EntityEquipmentSlot slot, ItemStack stack) {
/*  194 */             Multimap<String, AttributeModifier> multimap = super.getAttributeModifiers(slot, stack);
/*  195 */             if (slot == EntityEquipmentSlot.CHEST) {
/*  196 */               multimap.put(SharedMonsterAttributes.field_111267_a.func_111108_a(), ItemBaryonCloak.this.CLOAK_MODIFIER);
/*      */             }
/*  198 */             return multimap;
/*      */           }
/*      */ 
/*      */           
/*      */           @SideOnly(Side.CLIENT)
/*      */           public void func_77624_a(ItemStack stack, @Nullable World worldIn, List<String> tooltip, ITooltipFlag flagIn) {
/*  204 */             super.func_77624_a(stack, worldIn, tooltip, flagIn);
/*  205 */             tooltip.add(I18n.func_74838_a("key.mcreator.specialjutsu1") + ": " + I18n.func_74838_a("entity.fistofpush.name"));
/*      */           }
/*      */ 
/*      */           
/*      */           public int func_77612_l() {
/*  210 */             return 0;
/*      */           }
/*      */ 
/*      */           
/*      */           public boolean func_77645_m() {
/*  215 */             return false;
/*      */           }
/*      */ 
/*      */           
/*      */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/*  220 */             return ItemBaryonCloak.getTexture(stack);
/*      */           }
/*      */         }).func_77655_b("baryon_cloakbody").setRegistryName("baryon_cloakbody")).func_77637_a(null));
/*      */   }
/*      */   
/*      */   private static String getTexture(ItemStack stack) {
/*  226 */     return "borutomodaddononero:textures/baryon_mode.png";
/*      */   }
/*      */   
/*      */   public static void applyEffects(EntityLivingBase entity, int level) {
/*  230 */     applyEffects(entity, level, true);
/*      */   }
/*      */   
/*      */   public static void applyEffects(EntityLivingBase entity, int level, boolean smoke) {
/*  234 */     if (!entity.field_70170_p.field_72995_K && entity.field_70173_aa % 10 == 4) {
/*      */       
/*  236 */       entity.func_70690_d(new PotionEffect(PotionChakraEnhancedStrength.potion, 12, level * 32, false, false));
/*  237 */       entity.func_70690_d(new PotionEffect(MobEffects.field_76424_c, 12, level * 24, false, false));
/*  238 */       entity.func_70690_d(new PotionEffect(MobEffects.field_76430_j, 12, 5, false, false));
/*  239 */       entity.func_70690_d(new PotionEffect(PotionReach.potion, 12, level - 1, false, false));
/*  240 */       if (entity.func_110143_aJ() < entity.func_110138_aP() && entity.func_110143_aJ() > 0.0F) {
/*  241 */         entity.func_70691_i(level);
/*      */       }
/*  243 */       if (level == 2) {
/*  244 */         entity.func_70690_d(new PotionEffect(MobEffects.field_76429_m, 12, 2, false, false));
/*      */       }
/*      */     } 
/*  247 */     if (!entity.field_70170_p.field_72995_K && entity instanceof EntityPlayer) {
/*  248 */       NBTTagCompound compound = entity.getEntityData().func_74764_b("lungeAttackData") ? entity.getEntityData().func_74775_l("lungeAttackData") : new NBTTagCompound();
/*  249 */       int attackTime = compound.func_74762_e("attackTime");
/*  250 */       Entity target = compound.func_74764_b("targetId") ? entity.field_70170_p.func_73045_a(compound.func_74762_e("targetId")) : null;
/*  251 */       if (entity.field_110158_av == 1) {
/*  252 */         RayTraceResult res = ProcedureUtils.objectEntityLookingAt((Entity)entity, 15.0D, 3.0D);
/*  253 */         if (res != null && res.field_72308_g instanceof EntityLivingBase && res.field_72308_g.func_70089_S()) {
/*  254 */           target = res.field_72308_g;
/*  255 */           compound.func_74768_a("targetId", target.func_145782_y());
/*  256 */           attackTime = 0;
/*  257 */           entity.field_70177_z = ProcedureUtils.getYawFromVec(target.func_174791_d()
/*  258 */               .func_178788_d(entity.func_174791_d()));
/*  259 */           double d0 = target.field_70165_t - entity.field_70165_t;
/*  260 */           double d1 = target.field_70163_u - entity.field_70163_u;
/*  261 */           double d2 = target.field_70161_v - entity.field_70161_v;
/*  262 */           double d3 = MathHelper.func_76133_a(d0 * d0 + d2 * d2);
/*  263 */           ProcedureUtils.setVelocity((Entity)entity, d0 * 0.5D, d1 * 0.5D + d3 * 0.025D, d2 * 0.5D);
/*      */         } 
/*      */       } 
/*  266 */       if (attackTime < 12 && target != null && target.func_70068_e((Entity)entity) < 25.0D) {
/*  267 */         ((EntityPlayer)entity).func_71059_n(target);
/*  268 */         compound.func_82580_o("targetId");
/*      */       } 
/*  270 */       compound.func_74768_a("attackTime", ++attackTime);
/*  271 */       entity.getEntityData().func_74782_a("lungeAttackData", (NBTBase)compound);
/*      */     } 
/*      */   }
/*      */   
/*      */   private boolean isWearingFullSet(EntityPlayer player) {
/*  276 */     ItemStack headArmor = (ItemStack)player.field_71071_by.field_70460_b.get(EntityEquipmentSlot.HEAD.func_188454_b());
/*  277 */     ItemStack bodyArmor = (ItemStack)player.field_71071_by.field_70460_b.get(EntityEquipmentSlot.CHEST.func_188454_b());
/*      */     
/*  279 */     return (!headArmor.func_190926_b() && headArmor.func_77973_b() == helmet && 
/*  280 */       !bodyArmor.func_190926_b() && bodyArmor.func_77973_b() == body);
/*      */   }
/*      */ 
/*      */   
/*      */   @SideOnly(Side.CLIENT)
/*      */   public void registerModels(ModelRegistryEvent event) {
/*  286 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:baryon_cloakbody", "inventory"));
/*  287 */     ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("borutomodaddononero:baryon_cloakbody", "inventory"));
/*      */   }
/*      */ 
/*      */   
/*      */   @SideOnly(Side.CLIENT)
/*      */   public class ModelBaryonCloak
/*      */     extends ModelBiped
/*      */   {
/*      */     private final ModelRenderer Head;
/*      */     private final ModelRenderer Body;
/*      */     private final ModelRenderer cube_r1;
/*      */     private final ModelRenderer cube_r2;
/*      */     private final ModelRenderer cube_r3;
/*      */     private final ModelRenderer cube_r4;
/*      */     private final ModelRenderer cube_r5;
/*      */     private final ModelRenderer cube_r6;
/*      */     private final ModelRenderer cube_r7;
/*      */     private final ModelRenderer cube_r8;
/*      */     private final ModelRenderer cube_r9;
/*      */     private final ModelRenderer cube_r10;
/*      */     private final ModelRenderer cube_r11;
/*      */     private final ModelRenderer cube_r12;
/*      */     private final ModelRenderer cube_r13;
/*      */     private final ModelRenderer cube_r14;
/*      */     private final ModelRenderer cube_r15;
/*      */     private final ModelRenderer cube_r16;
/*      */     private final ModelRenderer cube_r17;
/*      */     private final ModelRenderer cube_r18;
/*      */     private final ModelRenderer cube_r19;
/*      */     private final ModelRenderer cube_r20;
/*      */     private final ModelRenderer cube_r21;
/*      */     private final ModelRenderer cube_r22;
/*      */     private final ModelRenderer cube_r23;
/*      */     private final ModelRenderer cube_r24;
/*      */     private final ModelRenderer cube_r25;
/*      */     private final ModelRenderer cube_r26;
/*      */     private final ModelRenderer cube_r27;
/*      */     private final ModelRenderer cube_r28;
/*      */     private final ModelRenderer cube_r29;
/*      */     private final ModelRenderer cube_r30;
/*      */     private final ModelRenderer cube_r31;
/*      */     private final ModelRenderer cube_r32;
/*      */     private final ModelRenderer cube_r33;
/*      */     private final ModelRenderer cube_r34;
/*      */     private final ModelRenderer cube_r35;
/*      */     private final ModelRenderer cube_r36;
/*      */     private final ModelRenderer cube_r37;
/*      */     private final ModelRenderer cube_r38;
/*      */     private final ModelRenderer cube_r39;
/*      */     private final ModelRenderer cube_r40;
/*      */     private final ModelRenderer cube_r41;
/*      */     private final ModelRenderer cube_r42;
/*      */     private final ModelRenderer cube_r43;
/*      */     private final ModelRenderer cube_r44;
/*      */     private final ModelRenderer cube_r45;
/*      */     private final ModelRenderer cube_r46;
/*      */     private final ModelRenderer cube_r47;
/*      */     private final ModelRenderer cube_r48;
/*      */     private final ModelRenderer cube_r49;
/*      */     private final ModelRenderer cube_r50;
/*      */     private final ModelRenderer cube_r51;
/*      */     private final ModelRenderer cube_r52;
/*      */     private final ModelRenderer cube_r53;
/*      */     private final ModelRenderer cube_r54;
/*      */     private final ModelRenderer cube_r55;
/*      */     private final ModelRenderer cube_r56;
/*      */     private final ModelRenderer cube_r57;
/*      */     private final ModelRenderer cube_r58;
/*      */     private final ModelRenderer cube_r59;
/*      */     private final ModelRenderer cube_r60;
/*      */     private final ModelRenderer cube_r61;
/*      */     private final ModelRenderer cube_r62;
/*      */     private final ModelRenderer cube_r63;
/*      */     private final ModelRenderer cube_r64;
/*      */     private final ModelRenderer cube_r65;
/*      */     private final ModelRenderer cube_r66;
/*      */     private final ModelRenderer cube_r67;
/*      */     private final ModelRenderer cube_r68;
/*      */     private final ModelRenderer cube_r69;
/*      */     private final ModelRenderer cube_r70;
/*      */     private final ModelRenderer cube_r71;
/*      */     private final ModelRenderer cube_r72;
/*      */     private final ModelRenderer cube_r73;
/*      */     private final ModelRenderer cube_r74;
/*      */     private final ModelRenderer cube_r75;
/*      */     private final ModelRenderer cube_r76;
/*      */     private final ModelRenderer cube_r77;
/*      */     private final ModelRenderer cube_r78;
/*      */     private final ModelRenderer cube_r79;
/*      */     private final ModelRenderer cube_r80;
/*      */     private final ModelRenderer cube_r81;
/*      */     private final ModelRenderer cube_r82;
/*      */     private final ModelRenderer cube_r83;
/*      */     private final ModelRenderer cube_r84;
/*      */     private final ModelRenderer cube_r85;
/*      */     private final ModelRenderer cube_r86;
/*      */     private final ModelRenderer cube_r87;
/*      */     private final ModelRenderer cube_r88;
/*      */     private final ModelRenderer cube_r89;
/*      */     private final ModelRenderer cube_r90;
/*      */     private final ModelRenderer cube_r91;
/*      */     private final ModelRenderer cube_r92;
/*      */     private final ModelRenderer cube_r93;
/*      */     private final ModelRenderer cube_r94;
/*      */     private final ModelRenderer cube_r95;
/*      */     private final ModelRenderer cube_r96;
/*      */     private final ModelRenderer cube_r97;
/*      */     private final ModelRenderer cube_r98;
/*      */     private final ModelRenderer cube_r99;
/*      */     private final ModelRenderer cube_r100;
/*      */     private final ModelRenderer cube_r101;
/*      */     private final ModelRenderer cube_r102;
/*      */     private final ModelRenderer cube_r103;
/*      */     private final ModelRenderer cube_r104;
/*      */     private final ModelRenderer cube_r105;
/*      */     private final ModelRenderer cube_r106;
/*      */     private final ModelRenderer cube_r107;
/*      */     private final ModelRenderer cube_r108;
/*      */     private final ModelRenderer cube_r109;
/*      */     private final ModelRenderer cube_r110;
/*      */     private final ModelRenderer cube_r111;
/*      */     private final ModelRenderer cube_r112;
/*      */     private final ModelRenderer cube_r113;
/*      */     private final ModelRenderer cube_r114;
/*      */     private final ModelRenderer cube_r115;
/*      */     private final ModelRenderer cube_r116;
/*      */     private final ModelRenderer cube_r117;
/*      */     private final ModelRenderer cube_r118;
/*      */     private final ModelRenderer cube_r119;
/*      */     private final ModelRenderer cube_r120;
/*      */     private final ModelRenderer cube_r121;
/*      */     private final ModelRenderer cube_r122;
/*      */     private final ModelRenderer cube_r123;
/*      */     private final ModelRenderer cube_r124;
/*      */     private final ModelRenderer cube_r125;
/*      */     private final ModelRenderer cube_r126;
/*      */     private final ModelRenderer cube_r127;
/*      */     private final ModelRenderer cube_r128;
/*      */     private final ModelRenderer cube_r129;
/*      */     private final ModelRenderer cube_r130;
/*      */     private final ModelRenderer cube_r131;
/*      */     private final ModelRenderer cube_r132;
/*      */     private final ModelRenderer cube_r133;
/*      */     private final ModelRenderer cube_r134;
/*      */     private final ModelRenderer cube_r135;
/*      */     private final ModelRenderer cube_r136;
/*      */     private final ModelRenderer cube_r137;
/*      */     private final ModelRenderer cube_r138;
/*      */     private final ModelRenderer cube_r139;
/*      */     private final ModelRenderer cube_r140;
/*      */     private final ModelRenderer cube_r141;
/*      */     private final ModelRenderer cube_r142;
/*      */     private final ModelRenderer cube_r143;
/*      */     private final ModelRenderer cube_r144;
/*      */     private final ModelRenderer cube_r145;
/*      */     private final ModelRenderer cube_r146;
/*      */     private final ModelRenderer cube_r147;
/*      */     private final ModelRenderer cube_r148;
/*      */     private final ModelRenderer cube_r149;
/*      */     private final ModelRenderer cube_r150;
/*      */     private final ModelRenderer cube_r151;
/*      */     private final ModelRenderer cube_r152;
/*      */     private final ModelRenderer cube_r153;
/*      */     private final ModelRenderer cube_r154;
/*      */     private final ModelRenderer cube_r155;
/*      */     private final ModelRenderer cube_r156;
/*      */     private final ModelRenderer cube_r157;
/*      */     private final ModelRenderer cube_r158;
/*      */     private final ModelRenderer cube_r159;
/*      */     private final ModelRenderer cube_r160;
/*      */     private final ModelRenderer cube_r161;
/*      */     private final ModelRenderer cube_r162;
/*      */     private final ModelRenderer cube_r163;
/*      */     private final ModelRenderer cube_r164;
/*      */     private final ModelRenderer cube_r165;
/*      */     private final ModelRenderer cube_r166;
/*      */     private final ModelRenderer cube_r167;
/*      */     private final ModelRenderer cube_r168;
/*      */     private final ModelRenderer cube_r169;
/*      */     private final ModelRenderer cube_r170;
/*      */     private final ModelRenderer cube_r171;
/*      */     private final ModelRenderer cube_r172;
/*      */     private final ModelRenderer cube_r173;
/*      */     private final ModelRenderer cube_r174;
/*      */     private final ModelRenderer LeftArm;
/*      */     private final ModelRenderer cube_r175;
/*      */     private final ModelRenderer cube_r176;
/*      */     private final ModelRenderer cube_r177;
/*      */     private final ModelRenderer cube_r178;
/*      */     private final ModelRenderer cube_r179;
/*      */     private final ModelRenderer cube_r180;
/*      */     private final ModelRenderer cube_r181;
/*      */     private final ModelRenderer cube_r182;
/*      */     private final ModelRenderer cube_r183;
/*      */     private final ModelRenderer cube_r184;
/*      */     private final ModelRenderer cube_r185;
/*      */     private final ModelRenderer cube_r186;
/*      */     private final ModelRenderer cube_r187;
/*      */     private final ModelRenderer cube_r188;
/*      */     private final ModelRenderer cube_r189;
/*      */     private final ModelRenderer cube_r190;
/*      */     private final ModelRenderer cube_r191;
/*      */     private final ModelRenderer cube_r192;
/*      */     private final ModelRenderer cube_r193;
/*      */     private final ModelRenderer cube_r194;
/*      */     private final ModelRenderer cube_r195;
/*      */     private final ModelRenderer cube_r196;
/*      */     private final ModelRenderer cube_r197;
/*      */     private final ModelRenderer cube_r198;
/*      */     private final ModelRenderer cube_r199;
/*      */     private final ModelRenderer cube_r200;
/*      */     private final ModelRenderer cube_r201;
/*      */     private final ModelRenderer cube_r202;
/*      */     private final ModelRenderer cube_r203;
/*      */     private final ModelRenderer cube_r204;
/*      */     private final ModelRenderer cube_r205;
/*      */     private final ModelRenderer cube_r206;
/*      */     private final ModelRenderer cube_r207;
/*      */     private final ModelRenderer cube_r208;
/*      */     private final ModelRenderer cube_r209;
/*      */     private final ModelRenderer cube_r210;
/*      */     private final ModelRenderer cube_r211;
/*      */     private final ModelRenderer cube_r212;
/*      */     private final ModelRenderer cube_r213;
/*      */     private final ModelRenderer cube_r214;
/*      */     private final ModelRenderer cube_r215;
/*      */     private final ModelRenderer cube_r216;
/*      */     private final ModelRenderer cube_r217;
/*      */     private final ModelRenderer cube_r218;
/*      */     private final ModelRenderer cube_r219;
/*      */     private final ModelRenderer cube_r220;
/*      */     private final ModelRenderer cube_r221;
/*      */     private final ModelRenderer cube_r222;
/*      */     private final ModelRenderer cube_r223;
/*      */     private final ModelRenderer cube_r224;
/*      */     private final ModelRenderer cube_r225;
/*      */     private final ModelRenderer cube_r226;
/*      */     private final ModelRenderer cube_r227;
/*      */     private final ModelRenderer cube_r228;
/*      */     private final ModelRenderer cube_r229;
/*      */     private final ModelRenderer cube_r230;
/*      */     private final ModelRenderer cube_r231;
/*      */     private final ModelRenderer cube_r232;
/*      */     private final ModelRenderer cube_r233;
/*      */     private final ModelRenderer RightArm;
/*      */     private final ModelRenderer cube_r234;
/*      */     private final ModelRenderer cube_r235;
/*      */     private final ModelRenderer cube_r236;
/*      */     private final ModelRenderer cube_r237;
/*      */     private final ModelRenderer cube_r238;
/*      */     private final ModelRenderer cube_r239;
/*      */     private final ModelRenderer cube_r240;
/*      */     private final ModelRenderer cube_r241;
/*      */     private final ModelRenderer cube_r242;
/*      */     private final ModelRenderer cube_r243;
/*      */     private final ModelRenderer cube_r244;
/*      */     private final ModelRenderer cube_r245;
/*      */     private final ModelRenderer cube_r246;
/*      */     private final ModelRenderer cube_r247;
/*      */     private final ModelRenderer cube_r248;
/*      */     private final ModelRenderer cube_r249;
/*      */     private final ModelRenderer cube_r250;
/*      */     private final ModelRenderer cube_r251;
/*      */     private final ModelRenderer cube_r252;
/*      */     private final ModelRenderer cube_r253;
/*      */     private final ModelRenderer cube_r254;
/*      */     private final ModelRenderer cube_r255;
/*      */     private final ModelRenderer cube_r256;
/*      */     private final ModelRenderer cube_r257;
/*      */     private final ModelRenderer cube_r258;
/*      */     private final ModelRenderer cube_r259;
/*      */     private final ModelRenderer cube_r260;
/*      */     private final ModelRenderer cube_r261;
/*      */     private final ModelRenderer cube_r262;
/*      */     private final ModelRenderer cube_r263;
/*      */     private final ModelRenderer cube_r264;
/*      */     private final ModelRenderer cube_r265;
/*      */     private final ModelRenderer cube_r266;
/*      */     private final ModelRenderer cube_r267;
/*      */     private final ModelRenderer cube_r268;
/*      */     private final ModelRenderer cube_r269;
/*      */     private final ModelRenderer cube_r270;
/*      */     private final ModelRenderer cube_r271;
/*      */     private final ModelRenderer cube_r272;
/*      */     private final ModelRenderer cube_r273;
/*      */     private final ModelRenderer cube_r274;
/*      */     private final ModelRenderer cube_r275;
/*      */     private final ModelRenderer cube_r276;
/*      */     private final ModelRenderer cube_r277;
/*      */     private final ModelRenderer cube_r278;
/*      */     private final ModelRenderer cube_r279;
/*      */     private final ModelRenderer cube_r280;
/*      */     private final ModelRenderer cube_r281;
/*      */     private final ModelRenderer cube_r282;
/*      */     private final ModelRenderer cube_r283;
/*      */     private final ModelRenderer cube_r284;
/*      */     private final ModelRenderer cube_r285;
/*      */     private final ModelRenderer cube_r286;
/*      */     private final ModelRenderer cube_r287;
/*      */     private final ModelRenderer cube_r288;
/*      */     private final ModelRenderer cube_r289;
/*      */     private final ModelRenderer cube_r290;
/*      */     private final ModelRenderer cube_r291;
/*      */     private final ModelRenderer cube_r292;
/*      */     
/*      */     public ModelBaryonCloak() {
/*  593 */       this.field_78090_t = 64;
/*  594 */       this.field_78089_u = 32;
/*      */       
/*  596 */       this.Head = new ModelRenderer((ModelBase)this);
/*  597 */       this.Head.func_78793_a(0.0F, 0.0F, 0.0F);
/*      */       
/*  599 */       this.Body = new ModelRenderer((ModelBase)this);
/*  600 */       this.Body.func_78793_a(0.0F, 0.0F, 0.0F);
/*  601 */       this.Body.field_78804_l.add(new ModelBox(this.Body, 7, 14, -4.4009F, -3.1357F, 2.475F, 9, 1, 3, -0.45F, true));
/*      */       
/*  603 */       this.cube_r1 = new ModelRenderer((ModelBase)this);
/*  604 */       this.cube_r1.func_78793_a(2.3845F, 4.5597F, -3.3817F);
/*  605 */       this.Body.func_78792_a(this.cube_r1);
/*  606 */       setRotationAngle(this.cube_r1, -0.0152F, 0.0859F, -0.1752F);
/*  607 */       this.cube_r1.field_78804_l.add(new ModelBox(this.cube_r1, 3, 29, -2.5F, 0.5F, 0.5F, 4, 2, 1, 0.0F, false));
/*      */       
/*  609 */       this.cube_r2 = new ModelRenderer((ModelBase)this);
/*  610 */       this.cube_r2.func_78793_a(-0.6293F, -1.8168F, 0.0443F);
/*  611 */       this.Body.func_78792_a(this.cube_r2);
/*  612 */       setRotationAngle(this.cube_r2, 0.0F, 0.0F, 0.1309F);
/*  613 */       this.cube_r2.field_78804_l.add(new ModelBox(this.cube_r2, 13, 8, 4.5203F, -2.8971F, -3.0373F, 1, 2, 6, 0.0F, false));
/*      */       
/*  615 */       this.cube_r3 = new ModelRenderer((ModelBase)this);
/*  616 */       this.cube_r3.func_78793_a(5.1249F, -3.0915F, -0.993F);
/*  617 */       this.Body.func_78792_a(this.cube_r3);
/*  618 */       setRotationAngle(this.cube_r3, 0.0F, 0.0F, 0.6109F);
/*  619 */       this.cube_r3.field_78804_l.add(new ModelBox(this.cube_r3, 18, 12, -0.5F, 2.0F, -2.0F, 1, 1, 1, 0.0F, false));
/*  620 */       this.cube_r3.field_78804_l.add(new ModelBox(this.cube_r3, 13, 7, -0.5F, 1.0F, -2.0F, 1, 1, 6, 0.0F, false));
/*      */       
/*  622 */       this.cube_r4 = new ModelRenderer((ModelBase)this);
/*  623 */       this.cube_r4.func_78793_a(3.1173F, -0.2245F, 0.507F);
/*  624 */       this.Body.func_78792_a(this.cube_r4);
/*  625 */       setRotationAngle(this.cube_r4, 0.0F, 0.0F, 0.3491F);
/*  626 */       this.cube_r4.field_78804_l.add(new ModelBox(this.cube_r4, 19, 11, -0.15F, -1.35F, -3.5F, 1, 2, 1, 0.0F, false));
/*      */       
/*  628 */       this.cube_r5 = new ModelRenderer((ModelBase)this);
/*  629 */       this.cube_r5.func_78793_a(-5.2381F, 1.7613F, -1.7491F);
/*  630 */       this.Body.func_78792_a(this.cube_r5);
/*  631 */       setRotationAngle(this.cube_r5, 0.0F, 0.0873F, 0.0F);
/*  632 */       this.cube_r5.field_78804_l.add(new ModelBox(this.cube_r5, 18, 26, 4.5F, -0.5F, -0.5F, 5, 5, 1, 0.0F, false));
/*  633 */       this.cube_r5.field_78804_l.add(new ModelBox(this.cube_r5, 21, 0, 5.5F, -1.5F, -0.5F, 4, 1, 1, 0.0F, false));
/*      */       
/*  635 */       this.cube_r6 = new ModelRenderer((ModelBase)this);
/*  636 */       this.cube_r6.func_78793_a(1.2854F, 0.2897F, 0.673F);
/*  637 */       this.Body.func_78792_a(this.cube_r6);
/*  638 */       setRotationAngle(this.cube_r6, 0.0873F, 0.0F, 1.1781F);
/*  639 */       this.cube_r6.field_78804_l.add(new ModelBox(this.cube_r6, 19, 11, -0.45F, -2.525F, -3.475F, 1, 5, 1, 0.0F, false));
/*      */       
/*  641 */       this.cube_r7 = new ModelRenderer((ModelBase)this);
/*  642 */       this.cube_r7.func_78793_a(0.2535F, 3.9396F, -2.0853F);
/*  643 */       this.Body.func_78792_a(this.cube_r7);
/*  644 */       setRotationAngle(this.cube_r7, -0.0352F, 0.007F, 0.0031F);
/*  645 */       this.cube_r7.field_78804_l.add(new ModelBox(this.cube_r7, 18, 9, -0.75F, -2.5F, -0.5F, 1, 6, 1, 0.0F, false));
/*      */       
/*  647 */       this.cube_r8 = new ModelRenderer((ModelBase)this);
/*  648 */       this.cube_r8.func_78793_a(-0.2633F, 3.9428F, -2.0923F);
/*  649 */       this.Body.func_78792_a(this.cube_r8);
/*  650 */       setRotationAngle(this.cube_r8, -0.0352F, -0.007F, -0.0031F);
/*  651 */       this.cube_r8.field_78804_l.add(new ModelBox(this.cube_r8, 18, 9, -0.25F, -2.475F, -0.5F, 1, 6, 1, 0.0F, true));
/*      */       
/*  653 */       this.cube_r9 = new ModelRenderer((ModelBase)this);
/*  654 */       this.cube_r9.func_78793_a(-5.1347F, -3.0884F, -2.0F);
/*  655 */       this.Body.func_78792_a(this.cube_r9);
/*  656 */       setRotationAngle(this.cube_r9, 0.0F, 0.0F, -0.6109F);
/*  657 */       this.cube_r9.field_78804_l.add(new ModelBox(this.cube_r9, 13, 7, -0.5F, 1.0F, -1.0F, 1, 1, 6, 0.0F, true));
/*  658 */       this.cube_r9.field_78804_l.add(new ModelBox(this.cube_r9, 18, 12, -0.5F, 2.0F, -1.0F, 1, 1, 1, 0.0F, true));
/*      */       
/*  660 */       this.cube_r10 = new ModelRenderer((ModelBase)this);
/*  661 */       this.cube_r10.func_78793_a(5.2283F, 2.5644F, -1.7561F);
/*  662 */       this.Body.func_78792_a(this.cube_r10);
/*  663 */       setRotationAngle(this.cube_r10, 0.0F, -0.0873F, 0.0F);
/*  664 */       this.cube_r10.field_78804_l.add(new ModelBox(this.cube_r10, 21, 2, -9.5F, -2.3F, -0.5F, 4, 1, 1, 0.0F, true));
/*  665 */       this.cube_r10.field_78804_l.add(new ModelBox(this.cube_r10, 13, 26, -9.5F, -1.3F, -0.5F, 5, 5, 1, 0.0F, false));
/*      */       
/*  667 */       this.cube_r11 = new ModelRenderer((ModelBase)this);
/*  668 */       this.cube_r11.func_78793_a(-1.2953F, 0.2928F, 0.666F);
/*  669 */       this.Body.func_78792_a(this.cube_r11);
/*  670 */       setRotationAngle(this.cube_r11, 0.0873F, 0.0F, -1.1781F);
/*  671 */       this.cube_r11.field_78804_l.add(new ModelBox(this.cube_r11, 19, 11, -0.55F, -2.525F, -3.475F, 1, 5, 1, 0.0F, true));
/*      */       
/*  673 */       this.cube_r12 = new ModelRenderer((ModelBase)this);
/*  674 */       this.cube_r12.func_78793_a(-2.3944F, 4.5628F, -3.3886F);
/*  675 */       this.Body.func_78792_a(this.cube_r12);
/*  676 */       setRotationAngle(this.cube_r12, -0.0152F, -0.0859F, 0.1752F);
/*  677 */       this.cube_r12.field_78804_l.add(new ModelBox(this.cube_r12, 0, 29, -1.5F, 0.5F, 0.5F, 4, 2, 1, 0.0F, false));
/*      */       
/*  679 */       this.cube_r13 = new ModelRenderer((ModelBase)this);
/*  680 */       this.cube_r13.func_78793_a(-3.1272F, -0.2214F, 0.5F);
/*  681 */       this.Body.func_78792_a(this.cube_r13);
/*  682 */       setRotationAngle(this.cube_r13, 0.0F, 0.0F, -0.3491F);
/*  683 */       this.cube_r13.field_78804_l.add(new ModelBox(this.cube_r13, 19, 11, -0.85F, -1.35F, -3.5F, 1, 2, 1, 0.0F, true));
/*      */       
/*  685 */       this.cube_r14 = new ModelRenderer((ModelBase)this);
/*  686 */       this.cube_r14.func_78793_a(0.6195F, 2.402F, 3.7971F);
/*  687 */       this.Body.func_78792_a(this.cube_r14);
/*  688 */       setRotationAngle(this.cube_r14, 1.5708F, 0.0F, 0.0F);
/*  689 */       this.cube_r14.field_78804_l.add(new ModelBox(this.cube_r14, 5, 13, -5.0203F, -1.3221F, 1.4377F, 9, 1, 4, -0.45F, true));
/*      */       
/*  691 */       this.cube_r15 = new ModelRenderer((ModelBase)this);
/*  692 */       this.cube_r15.func_78793_a(0.6195F, -1.8136F, 0.0373F);
/*  693 */       this.Body.func_78792_a(this.cube_r15);
/*  694 */       setRotationAngle(this.cube_r15, 0.0F, 0.0F, -0.1309F);
/*  695 */       this.cube_r15.field_78804_l.add(new ModelBox(this.cube_r15, 13, 8, -5.5203F, -2.8971F, -3.0373F, 1, 2, 6, 0.0F, true));
/*      */       
/*  697 */       this.cube_r16 = new ModelRenderer((ModelBase)this);
/*  698 */       this.cube_r16.func_78793_a(-4.5841F, -4.0506F, 3.2706F);
/*  699 */       this.Body.func_78792_a(this.cube_r16);
/*  700 */       setRotationAngle(this.cube_r16, 0.0F, 0.3927F, -0.1309F);
/*  701 */       this.cube_r16.field_78804_l.add(new ModelBox(this.cube_r16, 16, 12, -0.5F, 0.0F, -0.5F, 1, 2, 2, 0.0F, true));
/*      */       
/*  703 */       this.cube_r17 = new ModelRenderer((ModelBase)this);
/*  704 */       this.cube_r17.func_78793_a(-4.473F, -4.0652F, 4.1407F);
/*  705 */       this.Body.func_78792_a(this.cube_r17);
/*  706 */       setRotationAngle(this.cube_r17, 0.0F, 0.7854F, -0.1309F);
/*  707 */       this.cube_r17.field_78804_l.add(new ModelBox(this.cube_r17, 18, 13, -0.5F, 0.0F, 0.5F, 1, 2, 1, 0.0F, true));
/*      */       
/*  709 */       this.cube_r18 = new ModelRenderer((ModelBase)this);
/*  710 */       this.cube_r18.func_78793_a(-6.3526F, -4.1255F, 5.4375F);
/*  711 */       this.Body.func_78792_a(this.cube_r18);
/*  712 */       setRotationAngle(this.cube_r18, 0.0F, 1.5708F, -0.0436F);
/*  713 */       this.cube_r18.field_78804_l.add(new ModelBox(this.cube_r18, 15, 11, -0.5F, 0.0F, 3.5F, 1, 2, 3, 0.0F, true));
/*      */       
/*  715 */       this.cube_r19 = new ModelRenderer((ModelBase)this);
/*  716 */       this.cube_r19.func_78793_a(-5.8721F, -3.881F, 4.1362F);
/*  717 */       this.Body.func_78792_a(this.cube_r19);
/*  718 */       setRotationAngle(this.cube_r19, 0.0F, 1.1781F, -0.1309F);
/*  719 */       this.cube_r19.field_78804_l.add(new ModelBox(this.cube_r19, 17, 13, -0.5F, 0.0F, 2.5F, 1, 2, 1, 0.0F, true));
/*      */       
/*  721 */       this.cube_r20 = new ModelRenderer((ModelBase)this);
/*  722 */       this.cube_r20.func_78793_a(4.5742F, -4.0538F, 3.2776F);
/*  723 */       this.Body.func_78792_a(this.cube_r20);
/*  724 */       setRotationAngle(this.cube_r20, 0.0F, -0.3927F, 0.1309F);
/*  725 */       this.cube_r20.field_78804_l.add(new ModelBox(this.cube_r20, 16, 12, -0.5F, 0.0F, -0.5F, 1, 2, 2, 0.0F, false));
/*      */       
/*  727 */       this.cube_r21 = new ModelRenderer((ModelBase)this);
/*  728 */       this.cube_r21.func_78793_a(4.4631F, -4.0684F, 4.1476F);
/*  729 */       this.Body.func_78792_a(this.cube_r21);
/*  730 */       setRotationAngle(this.cube_r21, 0.0F, -0.7854F, 0.1309F);
/*  731 */       this.cube_r21.field_78804_l.add(new ModelBox(this.cube_r21, 18, 13, -0.5F, 0.0F, 0.5F, 1, 2, 1, 0.0F, false));
/*      */       
/*  733 */       this.cube_r22 = new ModelRenderer((ModelBase)this);
/*  734 */       this.cube_r22.func_78793_a(5.8623F, -3.8842F, 4.1432F);
/*  735 */       this.Body.func_78792_a(this.cube_r22);
/*  736 */       setRotationAngle(this.cube_r22, 0.0F, -1.1781F, 0.1309F);
/*  737 */       this.cube_r22.field_78804_l.add(new ModelBox(this.cube_r22, 17, 13, -0.5F, 0.0F, 2.5F, 1, 2, 1, 0.0F, false));
/*      */       
/*  739 */       this.cube_r23 = new ModelRenderer((ModelBase)this);
/*  740 */       this.cube_r23.func_78793_a(6.3427F, -4.1286F, 5.4445F);
/*  741 */       this.Body.func_78792_a(this.cube_r23);
/*  742 */       setRotationAngle(this.cube_r23, 0.0F, -1.5708F, 0.0436F);
/*  743 */       this.cube_r23.field_78804_l.add(new ModelBox(this.cube_r23, 15, 11, -0.5F, 0.0F, 3.5F, 1, 2, 3, 0.0F, false));
/*      */       
/*  745 */       this.cube_r24 = new ModelRenderer((ModelBase)this);
/*  746 */       this.cube_r24.func_78793_a(6.2203F, 1.5184F, -0.3553F);
/*  747 */       this.Body.func_78792_a(this.cube_r24);
/*  748 */       setRotationAngle(this.cube_r24, 2.8457F, -0.7119F, -2.9367F);
/*  749 */       this.cube_r24.field_78804_l.add(new ModelBox(this.cube_r24, 0, 0, 1.0F, -2.6F, -1.7F, 2, 11, 1, 0.0F, false));
/*      */       
/*  751 */       this.cube_r25 = new ModelRenderer((ModelBase)this);
/*  752 */       this.cube_r25.func_78793_a(4.4448F, 3.1702F, 3.153F);
/*  753 */       this.Body.func_78792_a(this.cube_r25);
/*  754 */       setRotationAngle(this.cube_r25, 2.8927F, -0.4596F, -3.021F);
/*  755 */       this.cube_r25.field_78804_l.add(new ModelBox(this.cube_r25, 2, 0, -0.5F, -5.0F, -0.5F, 1, 11, 1, 0.0F, false));
/*      */       
/*  757 */       this.cube_r26 = new ModelRenderer((ModelBase)this);
/*  758 */       this.cube_r26.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  759 */       this.Body.func_78792_a(this.cube_r26);
/*  760 */       setRotationAngle(this.cube_r26, -2.3026F, 0.7434F, -2.897F);
/*  761 */       this.cube_r26.field_78804_l.add(new ModelBox(this.cube_r26, 6, 18, 23.1216F, -7.7868F, -1.6314F, 2, 5, 1, 0.0F, true));
/*      */       
/*  763 */       this.cube_r27 = new ModelRenderer((ModelBase)this);
/*  764 */       this.cube_r27.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  765 */       this.Body.func_78792_a(this.cube_r27);
/*  766 */       setRotationAngle(this.cube_r27, -1.9136F, 0.9502F, -2.3854F);
/*  767 */       this.cube_r27.field_78804_l.add(new ModelBox(this.cube_r27, 17, 11, 22.585F, -7.5368F, -11.1209F, 2, 5, 1, 0.0F, false));
/*      */       
/*  769 */       this.cube_r28 = new ModelRenderer((ModelBase)this);
/*  770 */       this.cube_r28.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  771 */       this.Body.func_78792_a(this.cube_r28);
/*  772 */       setRotationAngle(this.cube_r28, -2.4869F, 0.4523F, 3.0641F);
/*  773 */       this.cube_r28.field_78804_l.add(new ModelBox(this.cube_r28, 6, 12, 19.9859F, -7.5118F, 7.341F, 2, 5, 1, 0.0F, false));
/*      */       
/*  775 */       this.cube_r29 = new ModelRenderer((ModelBase)this);
/*  776 */       this.cube_r29.func_78793_a(18.641F, 6.6781F, 17.988F);
/*  777 */       this.Body.func_78792_a(this.cube_r29);
/*  778 */       setRotationAngle(this.cube_r29, -2.4869F, 0.4523F, 3.0641F);
/*  779 */       this.cube_r29.field_78804_l.add(new ModelBox(this.cube_r29, 16, 24, 25.9859F, -4.7118F, 7.341F, 1, 1, 1, 0.0F, true));
/*      */       
/*  781 */       this.cube_r30 = new ModelRenderer((ModelBase)this);
/*  782 */       this.cube_r30.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  783 */       this.Body.func_78792_a(this.cube_r30);
/*  784 */       setRotationAngle(this.cube_r30, -2.0408F, 0.7434F, -2.897F);
/*  785 */       this.cube_r30.field_78804_l.add(new ModelBox(this.cube_r30, 6, 24, 23.1216F, -11.6849F, 0.4055F, 2, 4, 1, 0.0F, true));
/*      */       
/*  787 */       this.cube_r31 = new ModelRenderer((ModelBase)this);
/*  788 */       this.cube_r31.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  789 */       this.Body.func_78792_a(this.cube_r31);
/*  790 */       setRotationAngle(this.cube_r31, -1.6948F, 0.8481F, -2.4178F);
/*  791 */       this.cube_r31.field_78804_l.add(new ModelBox(this.cube_r31, 17, 11, 23.7453F, -11.2599F, -8.2111F, 2, 4, 1, 0.0F, false));
/*  792 */       this.cube_r31.field_78804_l.add(new ModelBox(this.cube_r31, 17, 11, 23.7453F, -11.6849F, -8.2111F, 2, 4, 1, 0.0F, false));
/*      */       
/*  794 */       this.cube_r32 = new ModelRenderer((ModelBase)this);
/*  795 */       this.cube_r32.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  796 */       this.Body.func_78792_a(this.cube_r32);
/*  797 */       setRotationAngle(this.cube_r32, -2.2636F, 0.5492F, 3.0206F);
/*  798 */       this.cube_r32.field_78804_l.add(new ModelBox(this.cube_r32, 17, 11, 19.5885F, -11.2349F, 8.2891F, 2, 4, 1, 0.0F, false));
/*  799 */       this.cube_r32.field_78804_l.add(new ModelBox(this.cube_r32, 17, 11, 19.5885F, -11.6849F, 8.2891F, 2, 4, 1, 0.0F, false));
/*      */       
/*  801 */       this.cube_r33 = new ModelRenderer((ModelBase)this);
/*  802 */       this.cube_r33.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  803 */       this.Body.func_78792_a(this.cube_r33);
/*  804 */       setRotationAngle(this.cube_r33, -2.4335F, 0.7434F, -2.897F);
/*  805 */       this.cube_r33.field_78804_l.add(new ModelBox(this.cube_r33, 21, 4, 23.1216F, -13.9506F, -4.097F, 2, 3, 1, 0.0F, true));
/*      */       
/*  807 */       this.cube_r34 = new ModelRenderer((ModelBase)this);
/*  808 */       this.cube_r34.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  809 */       this.Body.func_78792_a(this.cube_r34);
/*  810 */       setRotationAngle(this.cube_r34, -2.1214F, 0.9742F, -2.4899F);
/*  811 */       this.cube_r34.field_78804_l.add(new ModelBox(this.cube_r34, 14, 13, 22.2053F, -13.9506F, -12.442F, 2, 3, 1, 0.0F, false));
/*      */       
/*  813 */       this.cube_r35 = new ModelRenderer((ModelBase)this);
/*  814 */       this.cube_r35.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  815 */       this.Body.func_78792_a(this.cube_r35);
/*  816 */       setRotationAngle(this.cube_r35, -2.5776F, 0.4609F, 3.1352F);
/*  817 */       this.cube_r35.field_78804_l.add(new ModelBox(this.cube_r35, 14, 13, 21.1285F, -13.9506F, 4.0581F, 2, 3, 1, 0.0F, false));
/*      */       
/*  819 */       this.cube_r36 = new ModelRenderer((ModelBase)this);
/*  820 */       this.cube_r36.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  821 */       this.Body.func_78792_a(this.cube_r36);
/*  822 */       setRotationAngle(this.cube_r36, -2.608F, 0.7434F, -2.897F);
/*  823 */       this.cube_r36.field_78804_l.add(new ModelBox(this.cube_r36, 24, 22, 23.1216F, -16.0272F, -6.4573F, 2, 3, 1, 0.0F, true));
/*      */       
/*  825 */       this.cube_r37 = new ModelRenderer((ModelBase)this);
/*  826 */       this.cube_r37.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  827 */       this.Body.func_78792_a(this.cube_r37);
/*  828 */       setRotationAngle(this.cube_r37, -2.3422F, 1.0213F, -2.5574F);
/*  829 */       this.cube_r37.field_78804_l.add(new ModelBox(this.cube_r37, 14, 13, 21.3981F, -16.0272F, -14.6599F, 2, 3, 1, 0.0F, false));
/*      */       
/*  831 */       this.cube_r38 = new ModelRenderer((ModelBase)this);
/*  832 */       this.cube_r38.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  833 */       this.Body.func_78792_a(this.cube_r38);
/*  834 */       setRotationAngle(this.cube_r38, -2.0986F, 0.6791F, -2.2503F);
/*  835 */       this.cube_r38.field_78804_l.add(new ModelBox(this.cube_r38, 14, 13, 26.2957F, -7.8599F, -13.4029F, 2, 2, 1, -0.1F, false));
/*      */       
/*  837 */       this.cube_r39 = new ModelRenderer((ModelBase)this);
/*  838 */       this.cube_r39.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  839 */       this.Body.func_78792_a(this.cube_r39);
/*  840 */       setRotationAngle(this.cube_r39, -2.7165F, 0.4326F, -3.0898F);
/*  841 */       this.cube_r39.field_78804_l.add(new ModelBox(this.cube_r39, 14, 13, 21.9357F, -16.0272F, 1.8402F, 2, 3, 1, 0.0F, false));
/*      */       
/*  843 */       this.cube_r40 = new ModelRenderer((ModelBase)this);
/*  844 */       this.cube_r40.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  845 */       this.Body.func_78792_a(this.cube_r40);
/*  846 */       setRotationAngle(this.cube_r40, -2.9074F, 0.6443F, 2.7934F);
/*  847 */       this.cube_r40.field_78804_l.add(new ModelBox(this.cube_r40, 14, 13, 14.2682F, -25.0959F, 0.5401F, 2, 2, 1, -0.1F, false));
/*      */       
/*  849 */       this.cube_r41 = new ModelRenderer((ModelBase)this);
/*  850 */       this.cube_r41.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  851 */       this.Body.func_78792_a(this.cube_r41);
/*  852 */       setRotationAngle(this.cube_r41, 2.7525F, 0.7456F, 2.3238F);
/*  853 */       this.cube_r41.field_78804_l.add(new ModelBox(this.cube_r41, 14, 13, 4.4484F, -29.97F, -9.0631F, 3, 3, 1, -0.1F, false));
/*      */       
/*  855 */       this.cube_r42 = new ModelRenderer((ModelBase)this);
/*  856 */       this.cube_r42.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  857 */       this.Body.func_78792_a(this.cube_r42);
/*  858 */       setRotationAngle(this.cube_r42, -2.9134F, 0.7434F, -2.897F);
/*  859 */       this.cube_r42.field_78804_l.add(new ModelBox(this.cube_r42, 14, 13, 23.1216F, -15.2437F, -11.0779F, 2, 2, 1, -0.1F, false));
/*      */       
/*  861 */       this.cube_r43 = new ModelRenderer((ModelBase)this);
/*  862 */       this.cube_r43.func_78793_a(-19.091F, 6.678F, 17.988F);
/*  863 */       this.Body.func_78792_a(this.cube_r43);
/*  864 */       setRotationAngle(this.cube_r43, -2.4869F, -0.4523F, -3.0641F);
/*  865 */       this.cube_r43.field_78804_l.add(new ModelBox(this.cube_r43, 16, 24, -26.9859F, -4.7118F, 7.341F, 1, 1, 1, 0.0F, false));
/*      */       
/*  867 */       this.cube_r44 = new ModelRenderer((ModelBase)this);
/*  868 */       this.cube_r44.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  869 */       this.Body.func_78792_a(this.cube_r44);
/*  870 */       setRotationAngle(this.cube_r44, -2.4869F, -0.4523F, -3.0641F);
/*  871 */       this.cube_r44.field_78804_l.add(new ModelBox(this.cube_r44, 6, 12, -21.9859F, -7.5118F, 7.341F, 2, 5, 1, 0.0F, true));
/*      */       
/*  873 */       this.cube_r45 = new ModelRenderer((ModelBase)this);
/*  874 */       this.cube_r45.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  875 */       this.Body.func_78792_a(this.cube_r45);
/*  876 */       setRotationAngle(this.cube_r45, -2.3026F, -0.7434F, 2.897F);
/*  877 */       this.cube_r45.field_78804_l.add(new ModelBox(this.cube_r45, 6, 18, -25.1216F, -7.7868F, -1.6314F, 2, 5, 1, 0.0F, false));
/*      */       
/*  879 */       this.cube_r46 = new ModelRenderer((ModelBase)this);
/*  880 */       this.cube_r46.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  881 */       this.Body.func_78792_a(this.cube_r46);
/*  882 */       setRotationAngle(this.cube_r46, -1.9136F, -0.9502F, 2.3854F);
/*  883 */       this.cube_r46.field_78804_l.add(new ModelBox(this.cube_r46, 17, 11, -24.585F, -7.5368F, -11.1209F, 2, 5, 1, 0.0F, true));
/*      */       
/*  885 */       this.cube_r47 = new ModelRenderer((ModelBase)this);
/*  886 */       this.cube_r47.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  887 */       this.Body.func_78792_a(this.cube_r47);
/*  888 */       setRotationAngle(this.cube_r47, -2.0408F, -0.7434F, 2.897F);
/*  889 */       this.cube_r47.field_78804_l.add(new ModelBox(this.cube_r47, 6, 24, -25.1216F, -11.6849F, 0.4055F, 2, 4, 1, 0.0F, false));
/*      */       
/*  891 */       this.cube_r48 = new ModelRenderer((ModelBase)this);
/*  892 */       this.cube_r48.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  893 */       this.Body.func_78792_a(this.cube_r48);
/*  894 */       setRotationAngle(this.cube_r48, -1.6948F, -0.8481F, 2.4178F);
/*  895 */       this.cube_r48.field_78804_l.add(new ModelBox(this.cube_r48, 17, 11, -25.7453F, -11.2599F, -8.2111F, 2, 4, 1, 0.0F, true));
/*  896 */       this.cube_r48.field_78804_l.add(new ModelBox(this.cube_r48, 17, 11, -25.7453F, -11.6849F, -8.2111F, 2, 4, 1, 0.0F, true));
/*      */       
/*  898 */       this.cube_r49 = new ModelRenderer((ModelBase)this);
/*  899 */       this.cube_r49.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  900 */       this.Body.func_78792_a(this.cube_r49);
/*  901 */       setRotationAngle(this.cube_r49, -2.5776F, -0.4609F, -3.1352F);
/*  902 */       this.cube_r49.field_78804_l.add(new ModelBox(this.cube_r49, 14, 13, -23.1285F, -13.9506F, 4.0581F, 2, 3, 1, 0.0F, true));
/*      */       
/*  904 */       this.cube_r50 = new ModelRenderer((ModelBase)this);
/*  905 */       this.cube_r50.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  906 */       this.Body.func_78792_a(this.cube_r50);
/*  907 */       setRotationAngle(this.cube_r50, -2.4335F, -0.7434F, 2.897F);
/*  908 */       this.cube_r50.field_78804_l.add(new ModelBox(this.cube_r50, 21, 4, -25.1216F, -13.9506F, -4.097F, 2, 3, 1, 0.0F, false));
/*      */       
/*  910 */       this.cube_r51 = new ModelRenderer((ModelBase)this);
/*  911 */       this.cube_r51.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  912 */       this.Body.func_78792_a(this.cube_r51);
/*  913 */       setRotationAngle(this.cube_r51, -2.1214F, -0.9742F, 2.4899F);
/*  914 */       this.cube_r51.field_78804_l.add(new ModelBox(this.cube_r51, 14, 13, -24.2053F, -13.9506F, -12.442F, 2, 3, 1, 0.0F, true));
/*      */       
/*  916 */       this.cube_r52 = new ModelRenderer((ModelBase)this);
/*  917 */       this.cube_r52.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  918 */       this.Body.func_78792_a(this.cube_r52);
/*  919 */       setRotationAngle(this.cube_r52, -2.7165F, -0.4326F, 3.0898F);
/*  920 */       this.cube_r52.field_78804_l.add(new ModelBox(this.cube_r52, 14, 13, -23.9357F, -16.0272F, 1.8402F, 2, 3, 1, 0.0F, true));
/*      */       
/*  922 */       this.cube_r53 = new ModelRenderer((ModelBase)this);
/*  923 */       this.cube_r53.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  924 */       this.Body.func_78792_a(this.cube_r53);
/*  925 */       setRotationAngle(this.cube_r53, -2.608F, -0.7434F, 2.897F);
/*  926 */       this.cube_r53.field_78804_l.add(new ModelBox(this.cube_r53, 24, 22, -25.1216F, -16.0272F, -6.4573F, 2, 3, 1, 0.0F, false));
/*      */       
/*  928 */       this.cube_r54 = new ModelRenderer((ModelBase)this);
/*  929 */       this.cube_r54.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  930 */       this.Body.func_78792_a(this.cube_r54);
/*  931 */       setRotationAngle(this.cube_r54, -2.3422F, -1.0213F, 2.5574F);
/*  932 */       this.cube_r54.field_78804_l.add(new ModelBox(this.cube_r54, 14, 13, -23.3981F, -16.0272F, -14.6599F, 2, 3, 1, 0.0F, true));
/*      */       
/*  934 */       this.cube_r55 = new ModelRenderer((ModelBase)this);
/*  935 */       this.cube_r55.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  936 */       this.Body.func_78792_a(this.cube_r55);
/*  937 */       setRotationAngle(this.cube_r55, -2.9134F, -0.7434F, 2.897F);
/*  938 */       this.cube_r55.field_78804_l.add(new ModelBox(this.cube_r55, 14, 13, -25.1216F, -15.2437F, -11.0779F, 2, 2, 1, -0.1F, true));
/*      */       
/*  940 */       this.cube_r56 = new ModelRenderer((ModelBase)this);
/*  941 */       this.cube_r56.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  942 */       this.Body.func_78792_a(this.cube_r56);
/*  943 */       setRotationAngle(this.cube_r56, -2.9074F, -0.6443F, -2.7934F);
/*  944 */       this.cube_r56.field_78804_l.add(new ModelBox(this.cube_r56, 14, 13, -16.2682F, -25.0959F, 0.5401F, 2, 2, 1, -0.1F, true));
/*      */       
/*  946 */       this.cube_r57 = new ModelRenderer((ModelBase)this);
/*  947 */       this.cube_r57.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  948 */       this.Body.func_78792_a(this.cube_r57);
/*  949 */       setRotationAngle(this.cube_r57, 2.7525F, -0.7456F, -2.3238F);
/*  950 */       this.cube_r57.field_78804_l.add(new ModelBox(this.cube_r57, 14, 13, -7.4484F, -29.97F, -9.0631F, 3, 3, 1, -0.1F, true));
/*      */       
/*  952 */       this.cube_r58 = new ModelRenderer((ModelBase)this);
/*  953 */       this.cube_r58.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  954 */       this.Body.func_78792_a(this.cube_r58);
/*  955 */       setRotationAngle(this.cube_r58, -2.0986F, -0.6791F, 2.2503F);
/*  956 */       this.cube_r58.field_78804_l.add(new ModelBox(this.cube_r58, 14, 13, -28.2957F, -7.8599F, -13.4029F, 2, 2, 1, -0.1F, true));
/*      */       
/*  958 */       this.cube_r59 = new ModelRenderer((ModelBase)this);
/*  959 */       this.cube_r59.func_78793_a(-13.1405F, 7.9356F, 14.3809F);
/*  960 */       this.Body.func_78792_a(this.cube_r59);
/*  961 */       setRotationAngle(this.cube_r59, -2.2636F, -0.5492F, -3.0206F);
/*  962 */       this.cube_r59.field_78804_l.add(new ModelBox(this.cube_r59, 17, 11, -21.5885F, -11.2349F, 8.2891F, 2, 4, 1, 0.0F, true));
/*  963 */       this.cube_r59.field_78804_l.add(new ModelBox(this.cube_r59, 17, 11, -21.5885F, -11.6849F, 8.2891F, 2, 4, 1, 0.0F, true));
/*      */       
/*  965 */       this.cube_r60 = new ModelRenderer((ModelBase)this);
/*  966 */       this.cube_r60.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  967 */       this.Body.func_78792_a(this.cube_r60);
/*  968 */       setRotationAngle(this.cube_r60, -2.3884F, -0.3993F, -3.1383F);
/*  969 */       this.cube_r60.field_78804_l.add(new ModelBox(this.cube_r60, 12, 18, 5.0898F, 1.3692F, 14.3553F, 2, 5, 1, 0.0F, true));
/*      */       
/*  971 */       this.cube_r61 = new ModelRenderer((ModelBase)this);
/*  972 */       this.cube_r61.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  973 */       this.Body.func_78792_a(this.cube_r61);
/*  974 */       setRotationAngle(this.cube_r61, -2.2138F, -0.6641F, 2.806F);
/*  975 */       this.cube_r61.field_78804_l.add(new ModelBox(this.cube_r61, 17, 11, -2.7912F, 1.6192F, 15.2104F, 2, 5, 1, 0.0F, true));
/*      */       
/*  977 */       this.cube_r62 = new ModelRenderer((ModelBase)this);
/*  978 */       this.cube_r62.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  979 */       this.Body.func_78792_a(this.cube_r62);
/*  980 */       setRotationAngle(this.cube_r62, -2.4555F, -0.1022F, -2.872F);
/*  981 */       this.cube_r62.field_78804_l.add(new ModelBox(this.cube_r62, 17, 11, 12.0437F, 1.6442F, 10.5494F, 2, 5, 1, 0.0F, true));
/*      */       
/*  983 */       this.cube_r63 = new ModelRenderer((ModelBase)this);
/*  984 */       this.cube_r63.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  985 */       this.Body.func_78792_a(this.cube_r63);
/*  986 */       setRotationAngle(this.cube_r63, -2.1266F, -0.3993F, -3.1383F);
/*  987 */       this.cube_r63.field_78804_l.add(new ModelBox(this.cube_r63, 12, 18, 5.0898F, 1.2968F, 13.4777F, 2, 4, 1, 0.0F, true));
/*      */       
/*  989 */       this.cube_r64 = new ModelRenderer((ModelBase)this);
/*  990 */       this.cube_r64.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  991 */       this.Body.func_78792_a(this.cube_r64);
/*  992 */       setRotationAngle(this.cube_r64, -1.9631F, -0.5605F, 2.7948F);
/*  993 */       this.cube_r64.field_78804_l.add(new ModelBox(this.cube_r64, 17, 11, -1.8268F, 1.7218F, 14.4057F, 2, 4, 1, 0.0F, true));
/*  994 */       this.cube_r64.field_78804_l.add(new ModelBox(this.cube_r64, 17, 11, -1.8268F, 1.2968F, 14.4057F, 2, 4, 1, 0.0F, true));
/*      */       
/*  996 */       this.cube_r65 = new ModelRenderer((ModelBase)this);
/*  997 */       this.cube_r65.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/*  998 */       this.Body.func_78792_a(this.cube_r65);
/*  999 */       setRotationAngle(this.cube_r65, -2.2165F, -0.2004F, -2.8373F);
/* 1000 */       this.cube_r65.field_78804_l.add(new ModelBox(this.cube_r65, 17, 11, 11.2719F, 1.7468F, 10.2401F, 2, 4, 1, 0.0F, true));
/* 1001 */       this.cube_r65.field_78804_l.add(new ModelBox(this.cube_r65, 17, 11, 11.2719F, 1.2968F, 10.2401F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1003 */       this.cube_r66 = new ModelRenderer((ModelBase)this);
/* 1004 */       this.cube_r66.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1005 */       this.Body.func_78792_a(this.cube_r66);
/* 1006 */       setRotationAngle(this.cube_r66, -2.5193F, -0.3993F, -3.1383F);
/* 1007 */       this.cube_r66.field_78804_l.add(new ModelBox(this.cube_r66, 12, 18, 5.0898F, -6.9596F, 12.948F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1009 */       this.cube_r67 = new ModelRenderer((ModelBase)this);
/* 1010 */       this.cube_r67.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1011 */       this.Body.func_78792_a(this.cube_r67);
/* 1012 */       setRotationAngle(this.cube_r67, -2.3864F, -0.6705F, 2.8876F);
/* 1013 */       this.cube_r67.field_78804_l.add(new ModelBox(this.cube_r67, 14, 13, -1.6456F, -6.9596F, 13.908F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1015 */       this.cube_r68 = new ModelRenderer((ModelBase)this);
/* 1016 */       this.cube_r68.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1017 */       this.Body.func_78792_a(this.cube_r68);
/* 1018 */       setRotationAngle(this.cube_r68, -2.5708F, -0.1095F, -2.9363F);
/* 1019 */       this.cube_r68.field_78804_l.add(new ModelBox(this.cube_r68, 14, 13, 11.0907F, -6.9596F, 9.7423F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1021 */       this.cube_r69 = new ModelRenderer((ModelBase)this);
/* 1022 */       this.cube_r69.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1023 */       this.Body.func_78792_a(this.cube_r69);
/* 1024 */       setRotationAngle(this.cube_r69, -2.6938F, -0.3993F, -3.1383F);
/* 1025 */       this.cube_r69.field_78804_l.add(new ModelBox(this.cube_r69, 24, 18, 5.0898F, -12.1023F, 11.5428F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1027 */       this.cube_r70 = new ModelRenderer((ModelBase)this);
/* 1028 */       this.cube_r70.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1029 */       this.Body.func_78792_a(this.cube_r70);
/* 1030 */       setRotationAngle(this.cube_r70, -2.5894F, -0.7068F, 2.9489F);
/* 1031 */       this.cube_r70.field_78804_l.add(new ModelBox(this.cube_r70, 14, 13, -1.165F, -12.1023F, 12.5875F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1033 */       this.cube_r71 = new ModelRenderer((ModelBase)this);
/* 1034 */       this.cube_r71.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1035 */       this.Body.func_78792_a(this.cube_r71);
/* 1036 */       setRotationAngle(this.cube_r71, -2.3946F, -0.4319F, 2.6092F);
/* 1037 */       this.cube_r71.field_78804_l.add(new ModelBox(this.cube_r71, 14, 13, -5.0855F, -12.7421F, 12.8571F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1039 */       this.cube_r72 = new ModelRenderer((ModelBase)this);
/* 1040 */       this.cube_r72.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1041 */       this.Body.func_78792_a(this.cube_r72);
/* 1042 */       setRotationAngle(this.cube_r72, -2.7298F, -0.0814F, -2.9892F);
/* 1043 */       this.cube_r72.field_78804_l.add(new ModelBox(this.cube_r72, 14, 13, 10.6101F, -12.1023F, 8.4218F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1045 */       this.cube_r73 = new ModelRenderer((ModelBase)this);
/* 1046 */       this.cube_r73.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1047 */       this.Body.func_78792_a(this.cube_r73);
/* 1048 */       setRotationAngle(this.cube_r73, -2.7792F, -0.3115F, -2.6525F);
/* 1049 */       this.cube_r73.field_78804_l.add(new ModelBox(this.cube_r73, 14, 13, 13.4567F, -8.2497F, 9.7474F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1051 */       this.cube_r74 = new ModelRenderer((ModelBase)this);
/* 1052 */       this.cube_r74.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1053 */       this.Body.func_78792_a(this.cube_r74);
/* 1054 */       setRotationAngle(this.cube_r74, 3.0441F, -0.4853F, -2.2936F);
/* 1055 */       this.cube_r74.field_78804_l.add(new ModelBox(this.cube_r74, 14, 13, 13.582F, -8.4853F, 9.5987F, 3, 3, 1, -0.1F, true));
/*      */       
/* 1057 */       this.cube_r75 = new ModelRenderer((ModelBase)this);
/* 1058 */       this.cube_r75.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1059 */       this.Body.func_78792_a(this.cube_r75);
/* 1060 */       setRotationAngle(this.cube_r75, -2.9992F, -0.3993F, -3.1383F);
/* 1061 */       this.cube_r75.field_78804_l.add(new ModelBox(this.cube_r75, 14, 13, 5.0898F, -16.9132F, 7.2693F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1063 */       this.cube_r76 = new ModelRenderer((ModelBase)this);
/* 1064 */       this.cube_r76.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1065 */       this.Body.func_78792_a(this.cube_r76);
/* 1066 */       setRotationAngle(this.cube_r76, -2.3884F, 0.3993F, 3.1383F);
/* 1067 */       this.cube_r76.field_78804_l.add(new ModelBox(this.cube_r76, 12, 18, 16.7091F, -5.5619F, 7.0879F, 2, 5, 1, 0.0F, false));
/*      */       
/* 1069 */       this.cube_r77 = new ModelRenderer((ModelBase)this);
/* 1070 */       this.cube_r77.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1071 */       this.Body.func_78792_a(this.cube_r77);
/* 1072 */       setRotationAngle(this.cube_r77, -2.2138F, 0.6641F, -2.806F);
/* 1073 */       this.cube_r77.field_78804_l.add(new ModelBox(this.cube_r77, 17, 11, 19.9973F, -5.3119F, -0.6113F, 2, 5, 1, 0.0F, false));
/*      */       
/* 1075 */       this.cube_r78 = new ModelRenderer((ModelBase)this);
/* 1076 */       this.cube_r78.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1077 */       this.Body.func_78792_a(this.cube_r78);
/* 1078 */       setRotationAngle(this.cube_r78, -2.4555F, 0.1022F, 2.872F);
/* 1079 */       this.cube_r78.field_78804_l.add(new ModelBox(this.cube_r78, 17, 11, 10.7247F, -5.2869F, 12.9427F, 2, 5, 1, 0.0F, false));
/*      */       
/* 1081 */       this.cube_r79 = new ModelRenderer((ModelBase)this);
/* 1082 */       this.cube_r79.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1083 */       this.Body.func_78792_a(this.cube_r79);
/* 1084 */       setRotationAngle(this.cube_r79, -2.1266F, 0.3993F, 3.1383F);
/* 1085 */       this.cube_r79.field_78804_l.add(new ModelBox(this.cube_r79, 12, 18, 16.7091F, -7.2791F, 8.2519F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1087 */       this.cube_r80 = new ModelRenderer((ModelBase)this);
/* 1088 */       this.cube_r80.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1089 */       this.Body.func_78792_a(this.cube_r80);
/* 1090 */       setRotationAngle(this.cube_r80, -1.9631F, 0.5605F, -2.7948F);
/* 1091 */       this.cube_r80.field_78804_l.add(new ModelBox(this.cube_r80, 17, 11, 20.4031F, -6.8541F, 1.3553F, 2, 4, 1, 0.0F, false));
/* 1092 */       this.cube_r80.field_78804_l.add(new ModelBox(this.cube_r80, 17, 11, 20.4031F, -7.2791F, 1.3553F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1094 */       this.cube_r81 = new ModelRenderer((ModelBase)this);
/* 1095 */       this.cube_r81.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1096 */       this.Body.func_78792_a(this.cube_r81);
/* 1097 */       setRotationAngle(this.cube_r81, -2.2165F, 0.2004F, 2.8373F);
/* 1098 */       this.cube_r81.field_78804_l.add(new ModelBox(this.cube_r81, 17, 11, 10.8791F, -6.8291F, 13.469F, 2, 4, 1, 0.0F, false));
/* 1099 */       this.cube_r81.field_78804_l.add(new ModelBox(this.cube_r81, 17, 11, 10.8791F, -7.2791F, 13.469F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1101 */       this.cube_r82 = new ModelRenderer((ModelBase)this);
/* 1102 */       this.cube_r82.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1103 */       this.Body.func_78792_a(this.cube_r82);
/* 1104 */       setRotationAngle(this.cube_r82, -2.5193F, 0.3993F, 3.1383F);
/* 1105 */       this.cube_r82.field_78804_l.add(new ModelBox(this.cube_r82, 12, 18, 16.7091F, -12.8829F, 4.8381F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1107 */       this.cube_r83 = new ModelRenderer((ModelBase)this);
/* 1108 */       this.cube_r83.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1109 */       this.Body.func_78792_a(this.cube_r83);
/* 1110 */       setRotationAngle(this.cube_r83, -2.3864F, 0.6705F, -2.8876F);
/* 1111 */       this.cube_r83.field_78804_l.add(new ModelBox(this.cube_r83, 14, 13, 19.2355F, -12.8829F, -1.8525F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1113 */       this.cube_r84 = new ModelRenderer((ModelBase)this);
/* 1114 */       this.cube_r84.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1115 */       this.Body.func_78792_a(this.cube_r84);
/* 1116 */       setRotationAngle(this.cube_r84, -2.5708F, 0.1095F, 2.9363F);
/* 1117 */       this.cube_r84.field_78804_l.add(new ModelBox(this.cube_r84, 14, 13, 12.0466F, -12.8829F, 10.2612F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1119 */       this.cube_r85 = new ModelRenderer((ModelBase)this);
/* 1120 */       this.cube_r85.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1121 */       this.Body.func_78792_a(this.cube_r85);
/* 1122 */       setRotationAngle(this.cube_r85, -2.6938F, 0.3993F, 3.1383F);
/* 1123 */       this.cube_r85.field_78804_l.add(new ModelBox(this.cube_r85, 24, 18, 16.7091F, -16.5273F, 2.5275F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1125 */       this.cube_r86 = new ModelRenderer((ModelBase)this);
/* 1126 */       this.cube_r86.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1127 */       this.Body.func_78792_a(this.cube_r86);
/* 1128 */       setRotationAngle(this.cube_r86, -2.5894F, 0.7068F, -2.9489F);
/* 1129 */       this.cube_r86.field_78804_l.add(new ModelBox(this.cube_r86, 14, 13, 18.4452F, -16.5273F, -4.0238F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1131 */       this.cube_r87 = new ModelRenderer((ModelBase)this);
/* 1132 */       this.cube_r87.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1133 */       this.Body.func_78792_a(this.cube_r87);
/* 1134 */       setRotationAngle(this.cube_r87, -2.3946F, 0.4319F, -2.6092F);
/* 1135 */       this.cube_r87.field_78804_l.add(new ModelBox(this.cube_r87, 14, 13, 23.2975F, -9.452F, -2.8875F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1137 */       this.cube_r88 = new ModelRenderer((ModelBase)this);
/* 1138 */       this.cube_r88.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1139 */       this.Body.func_78792_a(this.cube_r88);
/* 1140 */       setRotationAngle(this.cube_r88, -2.7298F, 0.0814F, 2.9892F);
/* 1141 */       this.cube_r88.field_78804_l.add(new ModelBox(this.cube_r88, 14, 13, 12.8369F, -16.5273F, 8.09F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1143 */       this.cube_r89 = new ModelRenderer((ModelBase)this);
/* 1144 */       this.cube_r89.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1145 */       this.Body.func_78792_a(this.cube_r89);
/* 1146 */       setRotationAngle(this.cube_r89, -2.7792F, 0.3115F, 2.6525F);
/* 1147 */       this.cube_r89.field_78804_l.add(new ModelBox(this.cube_r89, 14, 13, 6.248F, -22.076F, 7.5154F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1149 */       this.cube_r90 = new ModelRenderer((ModelBase)this);
/* 1150 */       this.cube_r90.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1151 */       this.Body.func_78792_a(this.cube_r90);
/* 1152 */       setRotationAngle(this.cube_r90, 3.0441F, 0.4853F, 2.2936F);
/* 1153 */       this.cube_r90.field_78804_l.add(new ModelBox(this.cube_r90, 14, 13, -1.4678F, -26.9896F, -0.2193F, 3, 3, 1, -0.1F, false));
/*      */       
/* 1155 */       this.cube_r91 = new ModelRenderer((ModelBase)this);
/* 1156 */       this.cube_r91.func_78793_a(12.6905F, 7.9356F, 14.3809F);
/* 1157 */       this.Body.func_78792_a(this.cube_r91);
/* 1158 */       setRotationAngle(this.cube_r91, -2.9992F, 0.3993F, 3.1383F);
/* 1159 */       this.cube_r91.field_78804_l.add(new ModelBox(this.cube_r91, 14, 13, 16.7091F, -18.4224F, -2.6593F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1161 */       this.cube_r92 = new ModelRenderer((ModelBase)this);
/* 1162 */       this.cube_r92.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1163 */       this.Body.func_78792_a(this.cube_r92);
/* 1164 */       setRotationAngle(this.cube_r92, 2.012F, -0.3751F, -3.0013F);
/* 1165 */       this.cube_r92.field_78804_l.add(new ModelBox(this.cube_r92, 12, 19, -1.1818F, -6.2503F, -1.4628F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1167 */       this.cube_r93 = new ModelRenderer((ModelBase)this);
/* 1168 */       this.cube_r93.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1169 */       this.Body.func_78792_a(this.cube_r93);
/* 1170 */       setRotationAngle(this.cube_r93, 1.8342F, -0.5127F, -2.5929F);
/* 1171 */       this.cube_r93.field_78804_l.add(new ModelBox(this.cube_r93, 14, 13, -2.532F, -5.5003F, -1.8037F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1173 */       this.cube_r94 = new ModelRenderer((ModelBase)this);
/* 1174 */       this.cube_r94.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1175 */       this.Body.func_78792_a(this.cube_r94);
/* 1176 */       setRotationAngle(this.cube_r94, 2.1133F, -0.1875F, 2.9219F);
/* 1177 */       this.cube_r94.field_78804_l.add(new ModelBox(this.cube_r94, 17, 11, 0.1962F, -5.5253F, -1.6646F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1179 */       this.cube_r95 = new ModelRenderer((ModelBase)this);
/* 1180 */       this.cube_r95.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1181 */       this.Body.func_78792_a(this.cube_r95);
/* 1182 */       setRotationAngle(this.cube_r95, 1.6043F, -0.4127F, -2.6251F);
/* 1183 */       this.cube_r95.field_78804_l.add(new ModelBox(this.cube_r95, 17, 11, -2.4164F, -2.4788F, -2.3113F, 2, 4, 1, 0.0F, true));
/* 1184 */       this.cube_r95.field_78804_l.add(new ModelBox(this.cube_r95, 17, 11, -2.4164F, -2.0538F, -2.3113F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1186 */       this.cube_r96 = new ModelRenderer((ModelBase)this);
/* 1187 */       this.cube_r96.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1188 */       this.Body.func_78792_a(this.cube_r96);
/* 1189 */       setRotationAngle(this.cube_r96, 1.8689F, -0.2916F, 2.9229F);
/* 1190 */       this.cube_r96.field_78804_l.add(new ModelBox(this.cube_r96, 17, 11, 0.0748F, -2.5038F, -2.1869F, 2, 4, 1, 0.0F, true));
/* 1191 */       this.cube_r96.field_78804_l.add(new ModelBox(this.cube_r96, 17, 11, 0.0748F, -2.0538F, -2.1869F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1193 */       this.cube_r97 = new ModelRenderer((ModelBase)this);
/* 1194 */       this.cube_r97.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1195 */       this.Body.func_78792_a(this.cube_r97);
/* 1196 */       setRotationAngle(this.cube_r97, 2.1429F, -0.3751F, -3.0013F);
/* 1197 */       this.cube_r97.field_78804_l.add(new ModelBox(this.cube_r97, 12, 19, -1.1818F, 1.0214F, -2.6198F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1199 */       this.cube_r98 = new ModelRenderer((ModelBase)this);
/* 1200 */       this.cube_r98.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1201 */       this.Body.func_78792_a(this.cube_r98);
/* 1202 */       setRotationAngle(this.cube_r98, 1.9894F, -0.5428F, -2.6588F);
/* 1203 */       this.cube_r98.field_78804_l.add(new ModelBox(this.cube_r98, 14, 13, -2.2145F, 1.0214F, -2.8659F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1205 */       this.cube_r99 = new ModelRenderer((ModelBase)this);
/* 1206 */       this.cube_r99.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1207 */       this.Body.func_78792_a(this.cube_r99);
/* 1208 */       setRotationAngle(this.cube_r99, 2.224F, -0.1728F, 2.9857F);
/* 1209 */       this.cube_r99.field_78804_l.add(new ModelBox(this.cube_r99, 14, 13, -0.1271F, 1.0214F, -2.7416F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1211 */       this.cube_r100 = new ModelRenderer((ModelBase)this);
/* 1212 */       this.cube_r100.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1213 */       this.Body.func_78792_a(this.cube_r100);
/* 1214 */       setRotationAngle(this.cube_r100, 2.3174F, -0.3751F, -3.0013F);
/* 1215 */       this.cube_r100.field_78804_l.add(new ModelBox(this.cube_r100, 18, 18, -1.1818F, 3.5054F, -3.2783F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1217 */       this.cube_r101 = new ModelRenderer((ModelBase)this);
/* 1218 */       this.cube_r101.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1219 */       this.Body.func_78792_a(this.cube_r101);
/* 1220 */       setRotationAngle(this.cube_r101, 2.1721F, -0.5949F, -2.6933F);
/* 1221 */       this.cube_r101.field_78804_l.add(new ModelBox(this.cube_r101, 14, 13, -1.9893F, 3.5054F, -3.4847F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1223 */       this.cube_r102 = new ModelRenderer((ModelBase)this);
/* 1224 */       this.cube_r102.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1225 */       this.Body.func_78792_a(this.cube_r102);
/* 1226 */       setRotationAngle(this.cube_r102, 2.0865F, -0.2379F, -2.5067F);
/* 1227 */       this.cube_r102.field_78804_l.add(new ModelBox(this.cube_r102, 14, 13, -4.4075F, 5.2557F, -3.26F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1229 */       this.cube_r103 = new ModelRenderer((ModelBase)this);
/* 1230 */       this.cube_r103.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1231 */       this.Body.func_78792_a(this.cube_r103);
/* 1232 */       setRotationAngle(this.cube_r103, 2.382F, -0.1285F, 3.026F);
/* 1233 */       this.cube_r103.field_78804_l.add(new ModelBox(this.cube_r103, 14, 13, -0.3523F, 3.5054F, -3.3604F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1235 */       this.cube_r104 = new ModelRenderer((ModelBase)this);
/* 1236 */       this.cube_r104.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1237 */       this.Body.func_78792_a(this.cube_r104);
/* 1238 */       setRotationAngle(this.cube_r104, 2.4361F, -0.4567F, 2.7825F);
/* 1239 */       this.cube_r104.field_78804_l.add(new ModelBox(this.cube_r104, 14, 13, 2.2604F, 5.2739F, -3.0594F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1241 */       this.cube_r105 = new ModelRenderer((ModelBase)this);
/* 1242 */       this.cube_r105.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1243 */       this.Body.func_78792_a(this.cube_r105);
/* 1244 */       setRotationAngle(this.cube_r105, 2.9312F, -0.7278F, 2.4713F);
/* 1245 */       this.cube_r105.field_78804_l.add(new ModelBox(this.cube_r105, 14, 13, 3.6726F, 4.0262F, -4.1852F, 3, 3, 1, -0.1F, true));
/*      */       
/* 1247 */       this.cube_r106 = new ModelRenderer((ModelBase)this);
/* 1248 */       this.cube_r106.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1249 */       this.Body.func_78792_a(this.cube_r106);
/* 1250 */       setRotationAngle(this.cube_r106, 2.6228F, -0.3751F, -3.0013F);
/* 1251 */       this.cube_r106.field_78804_l.add(new ModelBox(this.cube_r106, 14, 13, -1.1818F, 5.1185F, -5.1827F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1253 */       this.cube_r107 = new ModelRenderer((ModelBase)this);
/* 1254 */       this.cube_r107.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1255 */       this.Body.func_78792_a(this.cube_r107);
/* 1256 */       setRotationAngle(this.cube_r107, 2.012F, 0.3751F, 3.0013F);
/* 1257 */       this.cube_r107.field_78804_l.add(new ModelBox(this.cube_r107, 12, 19, -0.8182F, -6.2503F, -1.4628F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1259 */       this.cube_r108 = new ModelRenderer((ModelBase)this);
/* 1260 */       this.cube_r108.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1261 */       this.Body.func_78792_a(this.cube_r108);
/* 1262 */       setRotationAngle(this.cube_r108, 1.8342F, 0.5127F, 2.5929F);
/* 1263 */       this.cube_r108.field_78804_l.add(new ModelBox(this.cube_r108, 14, 13, 0.532F, -5.5003F, -1.8037F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1265 */       this.cube_r109 = new ModelRenderer((ModelBase)this);
/* 1266 */       this.cube_r109.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1267 */       this.Body.func_78792_a(this.cube_r109);
/* 1268 */       setRotationAngle(this.cube_r109, 2.1133F, 0.1875F, -2.9219F);
/* 1269 */       this.cube_r109.field_78804_l.add(new ModelBox(this.cube_r109, 17, 11, -2.1962F, -5.5253F, -1.6646F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1271 */       this.cube_r110 = new ModelRenderer((ModelBase)this);
/* 1272 */       this.cube_r110.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1273 */       this.Body.func_78792_a(this.cube_r110);
/* 1274 */       setRotationAngle(this.cube_r110, 1.7502F, 0.3751F, 3.0013F);
/* 1275 */       this.cube_r110.field_78804_l.add(new ModelBox(this.cube_r110, 12, 19, -0.8182F, -2.0538F, -2.0295F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1277 */       this.cube_r111 = new ModelRenderer((ModelBase)this);
/* 1278 */       this.cube_r111.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1279 */       this.Body.func_78792_a(this.cube_r111);
/* 1280 */       setRotationAngle(this.cube_r111, 1.6043F, 0.4127F, 2.6251F);
/* 1281 */       this.cube_r111.field_78804_l.add(new ModelBox(this.cube_r111, 17, 11, 0.4164F, -2.4788F, -2.3113F, 2, 4, 1, 0.0F, false));
/* 1282 */       this.cube_r111.field_78804_l.add(new ModelBox(this.cube_r111, 17, 11, 0.4164F, -2.0538F, -2.3113F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1284 */       this.cube_r112 = new ModelRenderer((ModelBase)this);
/* 1285 */       this.cube_r112.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1286 */       this.Body.func_78792_a(this.cube_r112);
/* 1287 */       setRotationAngle(this.cube_r112, 1.8689F, 0.2916F, -2.9229F);
/* 1288 */       this.cube_r112.field_78804_l.add(new ModelBox(this.cube_r112, 17, 11, -2.0748F, -2.5038F, -2.1869F, 2, 4, 1, 0.0F, false));
/* 1289 */       this.cube_r112.field_78804_l.add(new ModelBox(this.cube_r112, 17, 11, -2.0748F, -2.0538F, -2.1869F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1291 */       this.cube_r113 = new ModelRenderer((ModelBase)this);
/* 1292 */       this.cube_r113.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1293 */       this.Body.func_78792_a(this.cube_r113);
/* 1294 */       setRotationAngle(this.cube_r113, 2.1429F, 0.3751F, 3.0013F);
/* 1295 */       this.cube_r113.field_78804_l.add(new ModelBox(this.cube_r113, 12, 19, -0.8182F, 1.0214F, -2.6198F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1297 */       this.cube_r114 = new ModelRenderer((ModelBase)this);
/* 1298 */       this.cube_r114.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1299 */       this.Body.func_78792_a(this.cube_r114);
/* 1300 */       setRotationAngle(this.cube_r114, 1.9894F, 0.5428F, 2.6588F);
/* 1301 */       this.cube_r114.field_78804_l.add(new ModelBox(this.cube_r114, 14, 13, 0.2145F, 1.0214F, -2.8659F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1303 */       this.cube_r115 = new ModelRenderer((ModelBase)this);
/* 1304 */       this.cube_r115.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1305 */       this.Body.func_78792_a(this.cube_r115);
/* 1306 */       setRotationAngle(this.cube_r115, 2.224F, 0.1728F, -2.9857F);
/* 1307 */       this.cube_r115.field_78804_l.add(new ModelBox(this.cube_r115, 14, 13, -1.8729F, 1.0214F, -2.7416F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1309 */       this.cube_r116 = new ModelRenderer((ModelBase)this);
/* 1310 */       this.cube_r116.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1311 */       this.Body.func_78792_a(this.cube_r116);
/* 1312 */       setRotationAngle(this.cube_r116, 2.3174F, 0.3751F, 3.0013F);
/* 1313 */       this.cube_r116.field_78804_l.add(new ModelBox(this.cube_r116, 18, 18, -0.8182F, 3.5054F, -3.2783F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1315 */       this.cube_r117 = new ModelRenderer((ModelBase)this);
/* 1316 */       this.cube_r117.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1317 */       this.Body.func_78792_a(this.cube_r117);
/* 1318 */       setRotationAngle(this.cube_r117, 2.1721F, 0.5949F, 2.6933F);
/* 1319 */       this.cube_r117.field_78804_l.add(new ModelBox(this.cube_r117, 14, 13, -0.0107F, 3.5054F, -3.4847F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1321 */       this.cube_r118 = new ModelRenderer((ModelBase)this);
/* 1322 */       this.cube_r118.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1323 */       this.Body.func_78792_a(this.cube_r118);
/* 1324 */       setRotationAngle(this.cube_r118, 2.0865F, 0.2379F, 2.5067F);
/* 1325 */       this.cube_r118.field_78804_l.add(new ModelBox(this.cube_r118, 14, 13, 2.4075F, 5.2557F, -3.26F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1327 */       this.cube_r119 = new ModelRenderer((ModelBase)this);
/* 1328 */       this.cube_r119.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1329 */       this.Body.func_78792_a(this.cube_r119);
/* 1330 */       setRotationAngle(this.cube_r119, 2.382F, 0.1285F, -3.026F);
/* 1331 */       this.cube_r119.field_78804_l.add(new ModelBox(this.cube_r119, 14, 13, -1.6477F, 3.5054F, -3.3604F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1333 */       this.cube_r120 = new ModelRenderer((ModelBase)this);
/* 1334 */       this.cube_r120.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1335 */       this.Body.func_78792_a(this.cube_r120);
/* 1336 */       setRotationAngle(this.cube_r120, 2.4361F, 0.4567F, -2.7825F);
/* 1337 */       this.cube_r120.field_78804_l.add(new ModelBox(this.cube_r120, 14, 13, -4.2604F, 5.2739F, -3.0594F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1339 */       this.cube_r121 = new ModelRenderer((ModelBase)this);
/* 1340 */       this.cube_r121.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1341 */       this.Body.func_78792_a(this.cube_r121);
/* 1342 */       setRotationAngle(this.cube_r121, 2.9312F, 0.7278F, -2.4713F);
/* 1343 */       this.cube_r121.field_78804_l.add(new ModelBox(this.cube_r121, 14, 13, -6.6726F, 4.0262F, -4.1852F, 3, 3, 1, -0.1F, false));
/*      */       
/* 1345 */       this.cube_r122 = new ModelRenderer((ModelBase)this);
/* 1346 */       this.cube_r122.func_78793_a(-3.0984F, 11.6084F, 9.2877F);
/* 1347 */       this.Body.func_78792_a(this.cube_r122);
/* 1348 */       setRotationAngle(this.cube_r122, 2.6228F, 0.3751F, 3.0013F);
/* 1349 */       this.cube_r122.field_78804_l.add(new ModelBox(this.cube_r122, 14, 13, -0.8182F, 5.1185F, -5.1827F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1351 */       this.cube_r123 = new ModelRenderer((ModelBase)this);
/* 1352 */       this.cube_r123.func_78793_a(-0.1964F, 8.5514F, 6.496F);
/* 1353 */       this.Body.func_78792_a(this.cube_r123);
/* 1354 */       setRotationAngle(this.cube_r123, 1.939F, -0.0179F, 3.1272F);
/* 1355 */       this.cube_r123.field_78804_l.add(new ModelBox(this.cube_r123, 12, 19, -1.0F, -2.0F, -0.5F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1357 */       this.cube_r124 = new ModelRenderer((ModelBase)this);
/* 1358 */       this.cube_r124.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1359 */       this.Body.func_78792_a(this.cube_r124);
/* 1360 */       setRotationAngle(this.cube_r124, 1.8656F, -0.139F, -2.781F);
/* 1361 */       this.cube_r124.field_78804_l.add(new ModelBox(this.cube_r124, 17, 11, -3.7072F, -10.7927F, -0.6416F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1363 */       this.cube_r125 = new ModelRenderer((ModelBase)this);
/* 1364 */       this.cube_r125.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1365 */       this.Body.func_78792_a(this.cube_r125);
/* 1366 */       setRotationAngle(this.cube_r125, 1.8788F, 0.1057F, 2.7538F);
/* 1367 */       this.cube_r125.field_78804_l.add(new ModelBox(this.cube_r125, 17, 11, 0.1869F, -10.8177F, -0.0119F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1369 */       this.cube_r126 = new ModelRenderer((ModelBase)this);
/* 1370 */       this.cube_r126.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1371 */       this.Body.func_78792_a(this.cube_r126);
/* 1372 */       setRotationAngle(this.cube_r126, 1.6336F, -0.0179F, 3.1272F);
/* 1373 */       this.cube_r126.field_78804_l.add(new ModelBox(this.cube_r126, 12, 19, -1.8228F, -6.5942F, -1.6689F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1375 */       this.cube_r127 = new ModelRenderer((ModelBase)this);
/* 1376 */       this.cube_r127.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1377 */       this.Body.func_78792_a(this.cube_r127);
/* 1378 */       setRotationAngle(this.cube_r127, 1.6237F, -0.0383F, -2.8074F);
/* 1379 */       this.cube_r127.field_78804_l.add(new ModelBox(this.cube_r127, 17, 11, -3.142F, -7.0192F, -2.1917F, 2, 4, 1, 0.0F, true));
/* 1380 */       this.cube_r127.field_78804_l.add(new ModelBox(this.cube_r127, 17, 11, -3.142F, -6.5942F, -2.1917F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1382 */       this.cube_r128 = new ModelRenderer((ModelBase)this);
/* 1383 */       this.cube_r128.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1384 */       this.Body.func_78792_a(this.cube_r128);
/* 1385 */       setRotationAngle(this.cube_r128, 1.6359F, 0.0046F, 2.7788F);
/* 1386 */       this.cube_r128.field_78804_l.add(new ModelBox(this.cube_r128, 17, 11, -0.4043F, -7.0442F, -1.6289F, 2, 4, 1, 0.0F, true));
/* 1387 */       this.cube_r128.field_78804_l.add(new ModelBox(this.cube_r128, 17, 11, -0.4043F, -6.5942F, -1.6289F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1389 */       this.cube_r129 = new ModelRenderer((ModelBase)this);
/* 1390 */       this.cube_r129.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1391 */       this.Body.func_78792_a(this.cube_r129);
/* 1392 */       setRotationAngle(this.cube_r129, 2.0263F, -0.0179F, 3.1272F);
/* 1393 */       this.cube_r129.field_78804_l.add(new ModelBox(this.cube_r129, 12, 19, -1.8228F, -3.0354F, -0.5491F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1395 */       this.cube_r130 = new ModelRenderer((ModelBase)this);
/* 1396 */       this.cube_r130.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1397 */       this.Body.func_78792_a(this.cube_r130);
/* 1398 */       setRotationAngle(this.cube_r130, 1.9965F, -0.1681F, -2.8392F);
/* 1399 */       this.cube_r130.field_78804_l.add(new ModelBox(this.cube_r130, 14, 13, -3.525F, -3.0354F, -1.1394F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1401 */       this.cube_r131 = new ModelRenderer((ModelBase)this);
/* 1402 */       this.cube_r131.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1403 */       this.Body.func_78792_a(this.cube_r131);
/* 1404 */       setRotationAngle(this.cube_r131, 2.0078F, 0.134F, 2.8121F);
/* 1405 */       this.cube_r131.field_78804_l.add(new ModelBox(this.cube_r131, 14, 13, -0.0213F, -3.0354F, -0.5766F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1407 */       this.cube_r132 = new ModelRenderer((ModelBase)this);
/* 1408 */       this.cube_r132.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1409 */       this.Body.func_78792_a(this.cube_r132);
/* 1410 */       setRotationAngle(this.cube_r132, 2.2008F, -0.0179F, 3.1272F);
/* 1411 */       this.cube_r132.field_78804_l.add(new ModelBox(this.cube_r132, 18, 18, -1.8228F, -0.1303F, -0.5346F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1413 */       this.cube_r133 = new ModelRenderer((ModelBase)this);
/* 1414 */       this.cube_r133.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1415 */       this.Body.func_78792_a(this.cube_r133);
/* 1416 */       setRotationAngle(this.cube_r133, 2.1663F, -0.2201F, -2.8689F);
/* 1417 */       this.cube_r133.field_78804_l.add(new ModelBox(this.cube_r133, 14, 13, -3.53F, -0.1303F, -1.1258F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1419 */       this.cube_r134 = new ModelRenderer((ModelBase)this);
/* 1420 */       this.cube_r134.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1421 */       this.Body.func_78792_a(this.cube_r134);
/* 1422 */       setRotationAngle(this.cube_r134, 2.1495F, 0.1316F, -2.688F);
/* 1423 */       this.cube_r134.field_78804_l.add(new ModelBox(this.cube_r134, 14, 13, -4.3368F, 1.3072F, -0.9019F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1425 */       this.cube_r135 = new ModelRenderer((ModelBase)this);
/* 1426 */       this.cube_r135.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1427 */       this.Body.func_78792_a(this.cube_r135);
/* 1428 */       setRotationAngle(this.cube_r135, 2.1766F, 0.1857F, 2.8421F);
/* 1429 */       this.cube_r135.field_78804_l.add(new ModelBox(this.cube_r135, 14, 13, -0.0163F, -0.1303F, -0.563F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1431 */       this.cube_r136 = new ModelRenderer((ModelBase)this);
/* 1432 */       this.cube_r136.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1433 */       this.Body.func_78792_a(this.cube_r136);
/* 1434 */       setRotationAngle(this.cube_r136, 2.1594F, -0.188F, 2.6935F);
/* 1435 */       this.cube_r136.field_78804_l.add(new ModelBox(this.cube_r136, 14, 13, 0.9399F, 1.7865F, -0.3668F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1437 */       this.cube_r137 = new ModelRenderer((ModelBase)this);
/* 1438 */       this.cube_r137.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1439 */       this.Body.func_78792_a(this.cube_r137);
/* 1440 */       setRotationAngle(this.cube_r137, 2.5372F, -0.5275F, 2.528F);
/* 1441 */       this.cube_r137.field_78804_l.add(new ModelBox(this.cube_r137, 14, 13, 1.0124F, 2.2154F, -0.8988F, 3, 3, 1, -0.1F, true));
/*      */       
/* 1443 */       this.cube_r138 = new ModelRenderer((ModelBase)this);
/* 1444 */       this.cube_r138.func_78793_a(-0.8388F, 10.8285F, 14.7347F);
/* 1445 */       this.Body.func_78792_a(this.cube_r138);
/* 1446 */       setRotationAngle(this.cube_r138, 2.5062F, -0.0179F, 3.1272F);
/* 1447 */       this.cube_r138.field_78804_l.add(new ModelBox(this.cube_r138, 14, 13, -1.8228F, 2.4762F, -1.4728F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1449 */       this.cube_r139 = new ModelRenderer((ModelBase)this);
/* 1450 */       this.cube_r139.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1451 */       this.Body.func_78792_a(this.cube_r139);
/* 1452 */       setRotationAngle(this.cube_r139, 2.712F, -0.6666F, -3.0502F);
/* 1453 */       this.cube_r139.field_78804_l.add(new ModelBox(this.cube_r139, 14, 13, -0.9672F, 5.8537F, -4.819F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1455 */       this.cube_r140 = new ModelRenderer((ModelBase)this);
/* 1456 */       this.cube_r140.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1457 */       this.Body.func_78792_a(this.cube_r140);
/* 1458 */       setRotationAngle(this.cube_r140, -2.9554F, -0.8417F, 2.1848F);
/* 1459 */       this.cube_r140.field_78804_l.add(new ModelBox(this.cube_r140, 14, 13, 4.2916F, 4.3687F, -3.7178F, 3, 3, 1, -0.1F, true));
/*      */       
/* 1461 */       this.cube_r141 = new ModelRenderer((ModelBase)this);
/* 1462 */       this.cube_r141.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1463 */       this.Body.func_78792_a(this.cube_r141);
/* 1464 */       setRotationAngle(this.cube_r141, 2.6929F, -0.6666F, 2.6404F);
/* 1465 */       this.cube_r141.field_78804_l.add(new ModelBox(this.cube_r141, 14, 13, 2.8103F, 5.6692F, -2.5491F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1467 */       this.cube_r142 = new ModelRenderer((ModelBase)this);
/* 1468 */       this.cube_r142.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1469 */       this.Body.func_78792_a(this.cube_r142);
/* 1470 */       setRotationAngle(this.cube_r142, 2.5348F, -0.3915F, 2.9823F);
/* 1471 */       this.cube_r142.field_78804_l.add(new ModelBox(this.cube_r142, 14, 13, 0.0436F, 4.0972F, -2.9F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1473 */       this.cube_r143 = new ModelRenderer((ModelBase)this);
/* 1474 */       this.cube_r143.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1475 */       this.Body.func_78792_a(this.cube_r143);
/* 1476 */       setRotationAngle(this.cube_r143, 1.9991F, -0.5284F, -2.4777F);
/* 1477 */       this.cube_r143.field_78804_l.add(new ModelBox(this.cube_r143, 14, 13, -4.6005F, 5.8053F, -2.6439F, 2, 2, 1, -0.1F, true));
/*      */       
/* 1479 */       this.cube_r144 = new ModelRenderer((ModelBase)this);
/* 1480 */       this.cube_r144.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1481 */       this.Body.func_78792_a(this.cube_r144);
/* 1482 */       setRotationAngle(this.cube_r144, 2.1389F, -0.8953F, -2.6746F);
/* 1483 */       this.cube_r144.field_78804_l.add(new ModelBox(this.cube_r144, 14, 13, -1.9819F, 4.0972F, -2.8776F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1485 */       this.cube_r145 = new ModelRenderer((ModelBase)this);
/* 1486 */       this.cube_r145.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1487 */       this.Body.func_78792_a(this.cube_r145);
/* 1488 */       setRotationAngle(this.cube_r145, 2.4066F, -0.6666F, -3.0502F);
/* 1489 */       this.cube_r145.field_78804_l.add(new ModelBox(this.cube_r145, 18, 18, -0.9672F, 4.0972F, -2.7102F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1491 */       this.cube_r146 = new ModelRenderer((ModelBase)this);
/* 1492 */       this.cube_r146.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1493 */       this.Body.func_78792_a(this.cube_r146);
/* 1494 */       setRotationAngle(this.cube_r146, 2.391F, -0.429F, 2.9317F);
/* 1495 */       this.cube_r146.field_78804_l.add(new ModelBox(this.cube_r146, 14, 13, 0.301F, 1.5056F, -2.1928F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1497 */       this.cube_r147 = new ModelRenderer((ModelBase)this);
/* 1498 */       this.cube_r147.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1499 */       this.Body.func_78792_a(this.cube_r147);
/* 1500 */       setRotationAngle(this.cube_r147, 1.9429F, -0.8422F, -2.6327F);
/* 1501 */       this.cube_r147.field_78804_l.add(new ModelBox(this.cube_r147, 14, 13, -2.2393F, 1.5056F, -2.1703F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1503 */       this.cube_r148 = new ModelRenderer((ModelBase)this);
/* 1504 */       this.cube_r148.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1505 */       this.Body.func_78792_a(this.cube_r148);
/* 1506 */       setRotationAngle(this.cube_r148, 2.232F, -0.6666F, -3.0502F);
/* 1507 */       this.cube_r148.field_78804_l.add(new ModelBox(this.cube_r148, 12, 19, -0.9672F, 1.5056F, -1.9576F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1509 */       this.cube_r149 = new ModelRenderer((ModelBase)this);
/* 1510 */       this.cube_r149.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1511 */       this.Body.func_78792_a(this.cube_r149);
/* 1512 */       setRotationAngle(this.cube_r149, 2.0641F, -0.5348F, 2.8397F);
/* 1513 */       this.cube_r149.field_78804_l.add(new ModelBox(this.cube_r149, 17, 11, 0.549F, -1.8599F, -1.5113F, 2, 4, 1, 0.0F, true));
/* 1514 */       this.cube_r149.field_78804_l.add(new ModelBox(this.cube_r149, 17, 11, 0.549F, -2.3099F, -1.5113F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1516 */       this.cube_r150 = new ModelRenderer((ModelBase)this);
/* 1517 */       this.cube_r150.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1518 */       this.Body.func_78792_a(this.cube_r150);
/* 1519 */       setRotationAngle(this.cube_r150, 1.5503F, -0.7107F, -2.6F);
/* 1520 */       this.cube_r150.field_78804_l.add(new ModelBox(this.cube_r150, 17, 11, -2.4874F, -1.8599F, -1.4889F, 2, 4, 1, 0.0F, true));
/* 1521 */       this.cube_r150.field_78804_l.add(new ModelBox(this.cube_r150, 17, 11, -2.4874F, -2.2849F, -1.4889F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1523 */       this.cube_r151 = new ModelRenderer((ModelBase)this);
/* 1524 */       this.cube_r151.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1525 */       this.Body.func_78792_a(this.cube_r151);
/* 1526 */       setRotationAngle(this.cube_r151, 1.8393F, -0.6666F, -3.0502F);
/* 1527 */       this.cube_r151.field_78804_l.add(new ModelBox(this.cube_r151, 12, 19, -0.9672F, -1.8599F, -1.2324F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1529 */       this.cube_r152 = new ModelRenderer((ModelBase)this);
/* 1530 */       this.cube_r152.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1531 */       this.Body.func_78792_a(this.cube_r152);
/* 1532 */       setRotationAngle(this.cube_r152, 2.2985F, -0.4325F, 2.8609F);
/* 1533 */       this.cube_r152.field_78804_l.add(new ModelBox(this.cube_r152, 14, 13, 0.6698F, -5.1317F, -1.0818F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1535 */       this.cube_r153 = new ModelRenderer((ModelBase)this);
/* 1536 */       this.cube_r153.func_78793_a(6.2165F, 9.5539F, 3.5406F);
/* 1537 */       this.Body.func_78792_a(this.cube_r153);
/* 1538 */       setRotationAngle(this.cube_r153, 1.8664F, -1.1057F, -2.6789F);
/* 1539 */       this.cube_r153.field_78804_l.add(new ModelBox(this.cube_r153, 14, 13, -1.0F, -1.0F, -0.5F, 2, 2, 1, 0.0F, true));
/*      */       
/* 1541 */       this.cube_r154 = new ModelRenderer((ModelBase)this);
/* 1542 */       this.cube_r154.func_78793_a(6.3622F, 10.9448F, 6.3721F);
/* 1543 */       this.Body.func_78792_a(this.cube_r154);
/* 1544 */       setRotationAngle(this.cube_r154, 2.1011F, -0.6666F, -3.0502F);
/* 1545 */       this.cube_r154.field_78804_l.add(new ModelBox(this.cube_r154, 12, 19, -0.9672F, -3.8567F, -0.7431F, 2, 2, 1, 0.0F, true));
/*      */       
/* 1547 */       this.cube_r155 = new ModelRenderer((ModelBase)this);
/* 1548 */       this.cube_r155.func_78793_a(-4.9841F, 3.5952F, 3.1916F);
/* 1549 */       this.Body.func_78792_a(this.cube_r155);
/* 1550 */       setRotationAngle(this.cube_r155, 2.8908F, 0.4747F, 3.0334F);
/* 1551 */       this.cube_r155.field_78804_l.add(new ModelBox(this.cube_r155, 14, 0, -0.5F, -5.5F, -0.5F, 1, 11, 1, 0.0F, false));
/*      */       
/* 1553 */       this.cube_r156 = new ModelRenderer((ModelBase)this);
/* 1554 */       this.cube_r156.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1555 */       this.Body.func_78792_a(this.cube_r156);
/* 1556 */       setRotationAngle(this.cube_r156, 2.2985F, 0.4325F, -2.8609F);
/* 1557 */       this.cube_r156.field_78804_l.add(new ModelBox(this.cube_r156, 14, 13, -2.6698F, -5.1317F, -1.0818F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1559 */       this.cube_r157 = new ModelRenderer((ModelBase)this);
/* 1560 */       this.cube_r157.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1561 */       this.Body.func_78792_a(this.cube_r157);
/* 1562 */       setRotationAngle(this.cube_r157, 2.1011F, 0.6666F, 3.0502F);
/* 1563 */       this.cube_r157.field_78804_l.add(new ModelBox(this.cube_r157, 12, 19, -1.0328F, -4.8567F, -0.7431F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1565 */       this.cube_r158 = new ModelRenderer((ModelBase)this);
/* 1566 */       this.cube_r158.func_78793_a(-6.2777F, 9.5739F, 3.5978F);
/* 1567 */       this.Body.func_78792_a(this.cube_r158);
/* 1568 */       setRotationAngle(this.cube_r158, 1.8943F, 1.1472F, 2.7098F);
/* 1569 */       this.cube_r158.field_78804_l.add(new ModelBox(this.cube_r158, 14, 13, -1.0F, -1.0F, -0.5F, 2, 2, 1, 0.0F, false));
/*      */       
/* 1571 */       this.cube_r159 = new ModelRenderer((ModelBase)this);
/* 1572 */       this.cube_r159.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1573 */       this.Body.func_78792_a(this.cube_r159);
/* 1574 */       setRotationAngle(this.cube_r159, 1.8393F, 0.6666F, 3.0502F);
/* 1575 */       this.cube_r159.field_78804_l.add(new ModelBox(this.cube_r159, 12, 19, -1.0328F, -1.8599F, -1.2324F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1577 */       this.cube_r160 = new ModelRenderer((ModelBase)this);
/* 1578 */       this.cube_r160.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1579 */       this.Body.func_78792_a(this.cube_r160);
/* 1580 */       setRotationAngle(this.cube_r160, 1.5503F, 0.7107F, 2.6F);
/* 1581 */       this.cube_r160.field_78804_l.add(new ModelBox(this.cube_r160, 17, 11, 0.4874F, -2.2849F, -1.4889F, 2, 4, 1, 0.0F, false));
/* 1582 */       this.cube_r160.field_78804_l.add(new ModelBox(this.cube_r160, 17, 11, 0.4874F, -1.8599F, -1.4889F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1584 */       this.cube_r161 = new ModelRenderer((ModelBase)this);
/* 1585 */       this.cube_r161.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1586 */       this.Body.func_78792_a(this.cube_r161);
/* 1587 */       setRotationAngle(this.cube_r161, 2.0641F, 0.5348F, -2.8397F);
/* 1588 */       this.cube_r161.field_78804_l.add(new ModelBox(this.cube_r161, 17, 11, -2.549F, -2.3099F, -1.5113F, 2, 4, 1, 0.0F, false));
/* 1589 */       this.cube_r161.field_78804_l.add(new ModelBox(this.cube_r161, 17, 11, -2.549F, -1.8599F, -1.5113F, 2, 4, 1, 0.0F, false));
/*      */       
/* 1591 */       this.cube_r162 = new ModelRenderer((ModelBase)this);
/* 1592 */       this.cube_r162.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1593 */       this.Body.func_78792_a(this.cube_r162);
/* 1594 */       setRotationAngle(this.cube_r162, 2.232F, 0.6666F, 3.0502F);
/* 1595 */       this.cube_r162.field_78804_l.add(new ModelBox(this.cube_r162, 12, 19, -1.0328F, 1.5056F, -1.9576F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1597 */       this.cube_r163 = new ModelRenderer((ModelBase)this);
/* 1598 */       this.cube_r163.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1599 */       this.Body.func_78792_a(this.cube_r163);
/* 1600 */       setRotationAngle(this.cube_r163, 1.9429F, 0.8422F, 2.6327F);
/* 1601 */       this.cube_r163.field_78804_l.add(new ModelBox(this.cube_r163, 14, 13, 0.2393F, 1.5056F, -2.1703F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1603 */       this.cube_r164 = new ModelRenderer((ModelBase)this);
/* 1604 */       this.cube_r164.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1605 */       this.Body.func_78792_a(this.cube_r164);
/* 1606 */       setRotationAngle(this.cube_r164, 2.391F, 0.429F, -2.9317F);
/* 1607 */       this.cube_r164.field_78804_l.add(new ModelBox(this.cube_r164, 14, 13, -2.301F, 1.5056F, -2.1928F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1609 */       this.cube_r165 = new ModelRenderer((ModelBase)this);
/* 1610 */       this.cube_r165.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1611 */       this.Body.func_78792_a(this.cube_r165);
/* 1612 */       setRotationAngle(this.cube_r165, 2.4066F, 0.6666F, 3.0502F);
/* 1613 */       this.cube_r165.field_78804_l.add(new ModelBox(this.cube_r165, 18, 18, -1.0328F, 4.0972F, -2.7102F, 2, 3, 1, 0.0F, true));
/*      */       
/* 1615 */       this.cube_r166 = new ModelRenderer((ModelBase)this);
/* 1616 */       this.cube_r166.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1617 */       this.Body.func_78792_a(this.cube_r166);
/* 1618 */       setRotationAngle(this.cube_r166, 2.1389F, 0.8953F, 2.6746F);
/* 1619 */       this.cube_r166.field_78804_l.add(new ModelBox(this.cube_r166, 14, 13, -0.0181F, 4.0972F, -2.8776F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1621 */       this.cube_r167 = new ModelRenderer((ModelBase)this);
/* 1622 */       this.cube_r167.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1623 */       this.Body.func_78792_a(this.cube_r167);
/* 1624 */       setRotationAngle(this.cube_r167, 1.9991F, 0.5284F, 2.4777F);
/* 1625 */       this.cube_r167.field_78804_l.add(new ModelBox(this.cube_r167, 14, 13, 2.6005F, 5.8053F, -2.6439F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1627 */       this.cube_r168 = new ModelRenderer((ModelBase)this);
/* 1628 */       this.cube_r168.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1629 */       this.Body.func_78792_a(this.cube_r168);
/* 1630 */       setRotationAngle(this.cube_r168, 2.5348F, 0.3915F, -2.9823F);
/* 1631 */       this.cube_r168.field_78804_l.add(new ModelBox(this.cube_r168, 14, 13, -2.0436F, 4.0972F, -2.9F, 2, 3, 1, 0.0F, false));
/*      */       
/* 1633 */       this.cube_r169 = new ModelRenderer((ModelBase)this);
/* 1634 */       this.cube_r169.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1635 */       this.Body.func_78792_a(this.cube_r169);
/* 1636 */       setRotationAngle(this.cube_r169, 2.6929F, 0.6666F, -2.6404F);
/* 1637 */       this.cube_r169.field_78804_l.add(new ModelBox(this.cube_r169, 14, 13, -4.8103F, 5.6692F, -2.5491F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1639 */       this.cube_r170 = new ModelRenderer((ModelBase)this);
/* 1640 */       this.cube_r170.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1641 */       this.Body.func_78792_a(this.cube_r170);
/* 1642 */       setRotationAngle(this.cube_r170, -2.9554F, 0.8417F, -2.1848F);
/* 1643 */       this.cube_r170.field_78804_l.add(new ModelBox(this.cube_r170, 14, 13, -7.2916F, 4.3687F, -3.7178F, 3, 3, 1, -0.1F, false));
/*      */       
/* 1645 */       this.cube_r171 = new ModelRenderer((ModelBase)this);
/* 1646 */       this.cube_r171.func_78793_a(-6.4122F, 10.9448F, 6.3721F);
/* 1647 */       this.Body.func_78792_a(this.cube_r171);
/* 1648 */       setRotationAngle(this.cube_r171, 2.712F, 0.6666F, 3.0502F);
/* 1649 */       this.cube_r171.field_78804_l.add(new ModelBox(this.cube_r171, 14, 13, -1.0328F, 5.8537F, -4.819F, 2, 2, 1, -0.1F, false));
/*      */       
/* 1651 */       this.cube_r172 = new ModelRenderer((ModelBase)this);
/* 1652 */       this.cube_r172.func_78793_a(6.8588F, 1.4411F, 13.1466F);
/* 1653 */       this.Body.func_78792_a(this.cube_r172);
/* 1654 */       setRotationAngle(this.cube_r172, 2.8525F, 0.6851F, 2.964F);
/* 1655 */       this.cube_r172.field_78804_l.add(new ModelBox(this.cube_r172, 15, 0, 16.0F, -5.5F, -0.5F, 2, 11, 1, 0.0F, false));
/*      */       
/* 1657 */       this.cube_r173 = new ModelRenderer((ModelBase)this);
/* 1658 */       this.cube_r173.func_78793_a(-0.2757F, 3.4776F, 3.8852F);
/* 1659 */       this.Body.func_78792_a(this.cube_r173);
/* 1660 */       setRotationAngle(this.cube_r173, 2.919F, 0.0076F, -3.1349F);
/* 1661 */       this.cube_r173.field_78804_l.add(new ModelBox(this.cube_r173, 5, 1, -4.5F, -3.5F, 0.0F, 9, 9, 1, 0.0F, false));
/*      */       
/* 1663 */       this.cube_r174 = new ModelRenderer((ModelBase)this);
/* 1664 */       this.cube_r174.func_78793_a(2.7484F, 11.6084F, 9.2877F);
/* 1665 */       this.Body.func_78792_a(this.cube_r174);
/* 1666 */       setRotationAngle(this.cube_r174, 1.7502F, -0.3751F, -3.0013F);
/* 1667 */       this.cube_r174.field_78804_l.add(new ModelBox(this.cube_r174, 12, 19, -1.1818F, -2.0538F, -2.0295F, 2, 4, 1, 0.0F, true));
/*      */       
/* 1669 */       this.LeftArm = new ModelRenderer((ModelBase)this);
/* 1670 */       this.LeftArm.func_78793_a(5.0F, 2.0F, 0.0F);
/* 1671 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 17, 8, -1.0F, -2.0F, -2.0F, 1, 6, 4, 0.05F, false));
/*      */       
/* 1673 */       this.cube_r175 = new ModelRenderer((ModelBase)this);
/* 1674 */       this.cube_r175.func_78793_a(1.4064F, -1.2231F, 2.3293F);
/* 1675 */       this.LeftArm.func_78792_a(this.cube_r175);
/* 1676 */       setRotationAngle(this.cube_r175, -0.5672F, -1.5272F, -0.3491F);
/* 1677 */       this.cube_r175.field_78804_l.add(new ModelBox(this.cube_r175, 17, 12, 0.075F, -0.5F, -0.5F, 1, 1, 2, -0.35F, true));
/*      */       
/* 1679 */       this.cube_r176 = new ModelRenderer((ModelBase)this);
/* 1680 */       this.cube_r176.func_78793_a(1.5254F, -1.0941F, 2.3357F);
/* 1681 */       this.LeftArm.func_78792_a(this.cube_r176);
/* 1682 */       setRotationAngle(this.cube_r176, -0.3491F, -1.5272F, -0.3491F);
/* 1683 */       this.cube_r176.field_78804_l.add(new ModelBox(this.cube_r176, 17, 12, 0.075F, -0.5F, -0.5F, 1, 1, 2, -0.35F, true));
/*      */       
/* 1685 */       this.cube_r177 = new ModelRenderer((ModelBase)this);
/* 1686 */       this.cube_r177.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1687 */       this.LeftArm.func_78792_a(this.cube_r177);
/* 1688 */       setRotationAngle(this.cube_r177, 1.0036F, 0.0F, 0.5236F);
/* 1689 */       this.cube_r177.field_78804_l.add(new ModelBox(this.cube_r177, 17, 12, 0.8945F, -2.5661F, -1.5923F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1691 */       this.cube_r178 = new ModelRenderer((ModelBase)this);
/* 1692 */       this.cube_r178.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1693 */       this.LeftArm.func_78792_a(this.cube_r178);
/* 1694 */       setRotationAngle(this.cube_r178, 0.3054F, 0.0F, 0.5236F);
/* 1695 */       this.cube_r178.field_78804_l.add(new ModelBox(this.cube_r178, 18, 13, 0.7442F, -2.1894F, -1.3492F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1697 */       this.cube_r179 = new ModelRenderer((ModelBase)this);
/* 1698 */       this.cube_r179.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1699 */       this.LeftArm.func_78792_a(this.cube_r179);
/* 1700 */       setRotationAngle(this.cube_r179, 0.1309F, 0.0F, 0.5236F);
/* 1701 */       this.cube_r179.field_78804_l.add(new ModelBox(this.cube_r179, 18, 13, 0.6789F, -2.0781F, -0.7264F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1703 */       this.cube_r180 = new ModelRenderer((ModelBase)this);
/* 1704 */       this.cube_r180.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1705 */       this.LeftArm.func_78792_a(this.cube_r180);
/* 1706 */       setRotationAngle(this.cube_r180, -0.1309F, 0.0F, 0.5236F);
/* 1707 */       this.cube_r180.field_78804_l.add(new ModelBox(this.cube_r180, 18, 13, 0.6789F, -2.0781F, -0.2736F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1709 */       this.cube_r181 = new ModelRenderer((ModelBase)this);
/* 1710 */       this.cube_r181.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1711 */       this.LeftArm.func_78792_a(this.cube_r181);
/* 1712 */       setRotationAngle(this.cube_r181, -0.3054F, 0.0F, 0.5236F);
/* 1713 */       this.cube_r181.field_78804_l.add(new ModelBox(this.cube_r181, 18, 13, 0.7442F, -2.1894F, 0.3492F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1715 */       this.cube_r182 = new ModelRenderer((ModelBase)this);
/* 1716 */       this.cube_r182.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1717 */       this.LeftArm.func_78792_a(this.cube_r182);
/* 1718 */       setRotationAngle(this.cube_r182, -1.0036F, 0.0F, 0.5236F);
/* 1719 */       this.cube_r182.field_78804_l.add(new ModelBox(this.cube_r182, 17, 12, 0.8945F, -2.5661F, -0.4077F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1721 */       this.cube_r183 = new ModelRenderer((ModelBase)this);
/* 1722 */       this.cube_r183.func_78793_a(-0.1171F, -1.0265F, 0.0755F);
/* 1723 */       this.LeftArm.func_78792_a(this.cube_r183);
/* 1724 */       setRotationAngle(this.cube_r183, -1.5272F, 0.0F, 0.1745F);
/* 1725 */       this.cube_r183.field_78804_l.add(new ModelBox(this.cube_r183, 16, 11, -1.2622F, -3.0093F, -0.6937F, 2, 1, 3, 0.0F, true));
/*      */       
/* 1727 */       this.cube_r184 = new ModelRenderer((ModelBase)this);
/* 1728 */       this.cube_r184.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1729 */       this.LeftArm.func_78792_a(this.cube_r184);
/* 1730 */       setRotationAngle(this.cube_r184, -1.0036F, 0.0F, 0.1745F);
/* 1731 */       this.cube_r184.field_78804_l.add(new ModelBox(this.cube_r184, 17, 12, -1.5551F, -2.4392F, -0.2085F, 2, 1, 2, 0.0F, true));
/*      */       
/* 1733 */       this.cube_r185 = new ModelRenderer((ModelBase)this);
/* 1734 */       this.cube_r185.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1735 */       this.LeftArm.func_78792_a(this.cube_r185);
/* 1736 */       setRotationAngle(this.cube_r185, -0.3054F, 0.0F, 0.1745F);
/* 1737 */       this.cube_r185.field_78804_l.add(new ModelBox(this.cube_r185, 18, 13, -1.6073F, -1.9982F, 0.4095F, 2, 1, 1, 0.0F, true));
/*      */       
/* 1739 */       this.cube_r186 = new ModelRenderer((ModelBase)this);
/* 1740 */       this.cube_r186.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1741 */       this.LeftArm.func_78792_a(this.cube_r186);
/* 1742 */       setRotationAngle(this.cube_r186, -0.1309F, 0.0F, 0.1745F);
/* 1743 */       this.cube_r186.field_78804_l.add(new ModelBox(this.cube_r186, 18, 13, -1.6299F, -1.8948F, -0.2495F, 2, 1, 1, 0.0F, true));
/*      */       
/* 1745 */       this.cube_r187 = new ModelRenderer((ModelBase)this);
/* 1746 */       this.cube_r187.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1747 */       this.LeftArm.func_78792_a(this.cube_r187);
/* 1748 */       setRotationAngle(this.cube_r187, 0.1309F, 0.0F, 0.1745F);
/* 1749 */       this.cube_r187.field_78804_l.add(new ModelBox(this.cube_r187, 18, 13, -1.6299F, -1.8948F, -0.7505F, 2, 1, 1, 0.0F, true));
/*      */       
/* 1751 */       this.cube_r188 = new ModelRenderer((ModelBase)this);
/* 1752 */       this.cube_r188.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1753 */       this.LeftArm.func_78792_a(this.cube_r188);
/* 1754 */       setRotationAngle(this.cube_r188, 0.3054F, 0.0F, 0.1745F);
/* 1755 */       this.cube_r188.field_78804_l.add(new ModelBox(this.cube_r188, 18, 13, -1.6073F, -1.9982F, -1.4095F, 2, 1, 1, 0.0F, true));
/*      */       
/* 1757 */       this.cube_r189 = new ModelRenderer((ModelBase)this);
/* 1758 */       this.cube_r189.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1759 */       this.LeftArm.func_78792_a(this.cube_r189);
/* 1760 */       setRotationAngle(this.cube_r189, 1.0036F, 0.0F, 0.1745F);
/* 1761 */       this.cube_r189.field_78804_l.add(new ModelBox(this.cube_r189, 17, 12, -1.5551F, -2.4392F, -1.7915F, 2, 1, 2, 0.0F, true));
/*      */       
/* 1763 */       this.cube_r190 = new ModelRenderer((ModelBase)this);
/* 1764 */       this.cube_r190.func_78793_a(-0.1171F, -1.0265F, -0.0118F);
/* 1765 */       this.LeftArm.func_78792_a(this.cube_r190);
/* 1766 */       setRotationAngle(this.cube_r190, 1.5272F, 0.0F, 0.1745F);
/* 1767 */       this.cube_r190.field_78804_l.add(new ModelBox(this.cube_r190, 16, 11, -1.2622F, -3.0093F, -2.3063F, 2, 1, 3, 0.0F, true));
/*      */       
/* 1769 */       this.cube_r191 = new ModelRenderer((ModelBase)this);
/* 1770 */       this.cube_r191.func_78793_a(-0.5693F, -1.1476F, 0.0755F);
/* 1771 */       this.LeftArm.func_78792_a(this.cube_r191);
/* 1772 */       setRotationAngle(this.cube_r191, -1.5272F, 0.0F, 0.3491F);
/* 1773 */       this.cube_r191.field_78804_l.add(new ModelBox(this.cube_r191, 16, 11, 0.757F, -3.015F, -0.8244F, 1, 1, 3, 0.0F, true));
/*      */       
/* 1775 */       this.cube_r192 = new ModelRenderer((ModelBase)this);
/* 1776 */       this.cube_r192.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1777 */       this.LeftArm.func_78792_a(this.cube_r192);
/* 1778 */       setRotationAngle(this.cube_r192, -1.0036F, 0.0F, 0.3491F);
/* 1779 */       this.cube_r192.field_78804_l.add(new ModelBox(this.cube_r192, 17, 12, 0.1801F, -2.4686F, -0.2546F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1781 */       this.cube_r193 = new ModelRenderer((ModelBase)this);
/* 1782 */       this.cube_r193.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1783 */       this.LeftArm.func_78792_a(this.cube_r193);
/* 1784 */       setRotationAngle(this.cube_r193, -0.3054F, 0.0F, 0.3491F);
/* 1785 */       this.cube_r193.field_78804_l.add(new ModelBox(this.cube_r193, 18, 13, 0.0772F, -2.0374F, 0.3972F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1787 */       this.cube_r194 = new ModelRenderer((ModelBase)this);
/* 1788 */       this.cube_r194.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1789 */       this.LeftArm.func_78792_a(this.cube_r194);
/* 1790 */       setRotationAngle(this.cube_r194, -0.1309F, 0.0F, 0.3491F);
/* 1791 */       this.cube_r194.field_78804_l.add(new ModelBox(this.cube_r194, 18, 13, 0.0326F, -1.9297F, -0.254F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1793 */       this.cube_r195 = new ModelRenderer((ModelBase)this);
/* 1794 */       this.cube_r195.func_78793_a(0.7806F, -1.5535F, 0.0318F);
/* 1795 */       this.LeftArm.func_78792_a(this.cube_r195);
/* 1796 */       setRotationAngle(this.cube_r195, 0.1309F, 0.0F, 0.3491F);
/* 1797 */       this.cube_r195.field_78804_l.add(new ModelBox(this.cube_r195, 18, 13, 0.0326F, -1.9297F, -0.746F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1799 */       this.cube_r196 = new ModelRenderer((ModelBase)this);
/* 1800 */       this.cube_r196.func_78793_a(0.65F, -1.684F, 0.0318F);
/* 1801 */       this.LeftArm.func_78792_a(this.cube_r196);
/* 1802 */       setRotationAngle(this.cube_r196, 0.3054F, 0.0F, 0.3491F);
/* 1803 */       this.cube_r196.field_78804_l.add(new ModelBox(this.cube_r196, 18, 13, 0.0772F, -2.0374F, -1.3972F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1805 */       this.cube_r197 = new ModelRenderer((ModelBase)this);
/* 1806 */       this.cube_r197.func_78793_a(0.3493F, -1.9847F, 0.0318F);
/* 1807 */       this.LeftArm.func_78792_a(this.cube_r197);
/* 1808 */       setRotationAngle(this.cube_r197, 1.0036F, 0.0F, 0.3491F);
/* 1809 */       this.cube_r197.field_78804_l.add(new ModelBox(this.cube_r197, 17, 12, 0.1801F, -2.4686F, -1.7454F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1811 */       this.cube_r198 = new ModelRenderer((ModelBase)this);
/* 1812 */       this.cube_r198.func_78793_a(-0.5693F, -1.1476F, -0.0118F);
/* 1813 */       this.LeftArm.func_78792_a(this.cube_r198);
/* 1814 */       setRotationAngle(this.cube_r198, 1.5272F, 0.0F, 0.3491F);
/* 1815 */       this.cube_r198.field_78804_l.add(new ModelBox(this.cube_r198, 16, 11, 0.757F, -3.015F, -2.1756F, 1, 1, 3, 0.0F, true));
/*      */       
/* 1817 */       this.cube_r199 = new ModelRenderer((ModelBase)this);
/* 1818 */       this.cube_r199.func_78793_a(1.8009F, -1.5764F, 0.0318F);
/* 1819 */       this.LeftArm.func_78792_a(this.cube_r199);
/* 1820 */       setRotationAngle(this.cube_r199, 0.0F, 1.0036F, -0.5236F);
/* 1821 */       this.cube_r199.field_78804_l.add(new ModelBox(this.cube_r199, 17, 12, 1.3853F, -0.2999F, -1.8761F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1823 */       this.cube_r200 = new ModelRenderer((ModelBase)this);
/* 1824 */       this.cube_r200.func_78793_a(1.8054F, -1.5242F, 0.0318F);
/* 1825 */       this.LeftArm.func_78792_a(this.cube_r200);
/* 1826 */       setRotationAngle(this.cube_r200, 0.0F, 0.3054F, -0.5236F);
/* 1827 */       this.cube_r200.field_78804_l.add(new ModelBox(this.cube_r200, 18, 13, 0.8896F, -0.1971F, -1.4438F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1829 */       this.cube_r201 = new ModelRenderer((ModelBase)this);
/* 1830 */       this.cube_r201.func_78793_a(1.8074F, -1.5016F, 0.0318F);
/* 1831 */       this.LeftArm.func_78792_a(this.cube_r201);
/* 1832 */       setRotationAngle(this.cube_r201, 0.0F, 0.1309F, -0.5236F);
/* 1833 */       this.cube_r201.field_78804_l.add(new ModelBox(this.cube_r201, 18, 13, 0.7761F, -0.1525F, -0.7662F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1835 */       this.cube_r202 = new ModelRenderer((ModelBase)this);
/* 1836 */       this.cube_r202.func_78793_a(1.8074F, -1.5016F, 0.0318F);
/* 1837 */       this.LeftArm.func_78792_a(this.cube_r202);
/* 1838 */       setRotationAngle(this.cube_r202, 0.0F, -0.1309F, -0.5236F);
/* 1839 */       this.cube_r202.field_78804_l.add(new ModelBox(this.cube_r202, 18, 13, 0.7761F, -0.1525F, -0.2338F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1841 */       this.cube_r203 = new ModelRenderer((ModelBase)this);
/* 1842 */       this.cube_r203.func_78793_a(1.8054F, -1.5242F, 0.0318F);
/* 1843 */       this.LeftArm.func_78792_a(this.cube_r203);
/* 1844 */       setRotationAngle(this.cube_r203, 0.0F, -0.3054F, -0.5236F);
/* 1845 */       this.cube_r203.field_78804_l.add(new ModelBox(this.cube_r203, 18, 13, 0.8896F, -0.1971F, 0.4438F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1847 */       this.cube_r204 = new ModelRenderer((ModelBase)this);
/* 1848 */       this.cube_r204.func_78793_a(1.8009F, -1.5764F, 0.0318F);
/* 1849 */       this.LeftArm.func_78792_a(this.cube_r204);
/* 1850 */       setRotationAngle(this.cube_r204, 0.0F, -1.0036F, -0.5236F);
/* 1851 */       this.cube_r204.field_78804_l.add(new ModelBox(this.cube_r204, 17, 12, 1.3853F, -0.2999F, -0.1239F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1853 */       this.cube_r205 = new ModelRenderer((ModelBase)this);
/* 1854 */       this.cube_r205.func_78793_a(4.1772F, 1.7613F, 0.4005F);
/* 1855 */       this.LeftArm.func_78792_a(this.cube_r205);
/* 1856 */       setRotationAngle(this.cube_r205, 0.0F, 0.3054F, 0.0F);
/* 1857 */       this.cube_r205.field_78804_l.add(new ModelBox(this.cube_r205, 18, 13, -0.6F, -1.5F, -2.3F, 1, 3, 1, 0.0F, true));
/*      */       
/* 1859 */       this.cube_r206 = new ModelRenderer((ModelBase)this);
/* 1860 */       this.cube_r206.func_78793_a(2.2367F, 3.192F, -2.1725F);
/* 1861 */       this.LeftArm.func_78792_a(this.cube_r206);
/* 1862 */       setRotationAngle(this.cube_r206, 0.0883F, 1.0847F, -0.5005F);
/* 1863 */       this.cube_r206.field_78804_l.add(new ModelBox(this.cube_r206, 17, 12, -0.5F, -0.5F, -0.725F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1865 */       this.cube_r207 = new ModelRenderer((ModelBase)this);
/* 1866 */       this.cube_r207.func_78793_a(2.755F, 1.7613F, -2.0289F);
/* 1867 */       this.LeftArm.func_78792_a(this.cube_r207);
/* 1868 */       setRotationAngle(this.cube_r207, 0.0F, 1.0036F, 0.0F);
/* 1869 */       this.cube_r207.field_78804_l.add(new ModelBox(this.cube_r207, 17, 12, -0.5F, -1.5F, -1.0F, 1, 3, 2, 0.0F, true));
/*      */       
/* 1871 */       this.cube_r208 = new ModelRenderer((ModelBase)this);
/* 1872 */       this.cube_r208.func_78793_a(-0.4122F, 3.6981F, -2.4986F);
/* 1873 */       this.LeftArm.func_78792_a(this.cube_r208);
/* 1874 */       setRotationAngle(this.cube_r208, -0.7413F, 1.5208F, -0.9604F);
/* 1875 */       this.cube_r208.field_78804_l.add(new ModelBox(this.cube_r208, 18, 13, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.0F, true));
/*      */       
/* 1877 */       this.cube_r209 = new ModelRenderer((ModelBase)this);
/* 1878 */       this.cube_r209.func_78793_a(1.0605F, 3.7538F, -2.4586F);
/* 1879 */       this.LeftArm.func_78792_a(this.cube_r209);
/* 1880 */       setRotationAngle(this.cube_r209, -0.6104F, 1.5208F, -0.9604F);
/* 1881 */       this.cube_r209.field_78804_l.add(new ModelBox(this.cube_r209, 17, 12, -0.5F, -1.5F, -1.0F, 1, 2, 2, 0.0F, true));
/*      */       
/* 1883 */       this.cube_r210 = new ModelRenderer((ModelBase)this);
/* 1884 */       this.cube_r210.func_78793_a(1.1201F, 1.7613F, -1.6329F);
/* 1885 */       this.LeftArm.func_78792_a(this.cube_r210);
/* 1886 */       setRotationAngle(this.cube_r210, 0.0F, 1.5272F, 0.0F);
/* 1887 */       this.cube_r210.field_78804_l.add(new ModelBox(this.cube_r210, 16, 11, 0.4F, -1.5F, -2.0F, 1, 3, 3, 0.0F, true));
/*      */       
/* 1889 */       this.cube_r211 = new ModelRenderer((ModelBase)this);
/* 1890 */       this.cube_r211.func_78793_a(4.1772F, 1.7613F, -0.3368F);
/* 1891 */       this.LeftArm.func_78792_a(this.cube_r211);
/* 1892 */       setRotationAngle(this.cube_r211, 0.0F, -0.3054F, 0.0F);
/* 1893 */       this.cube_r211.field_78804_l.add(new ModelBox(this.cube_r211, 18, 13, -0.6F, -1.5F, 1.3F, 1, 3, 1, 0.0F, true));
/*      */       
/* 1895 */       this.cube_r212 = new ModelRenderer((ModelBase)this);
/* 1896 */       this.cube_r212.func_78793_a(2.755F, 1.7613F, 2.0926F);
/* 1897 */       this.LeftArm.func_78792_a(this.cube_r212);
/* 1898 */       setRotationAngle(this.cube_r212, 0.0F, -1.0036F, 0.0F);
/* 1899 */       this.cube_r212.field_78804_l.add(new ModelBox(this.cube_r212, 17, 12, -0.5F, -1.5F, -1.0F, 1, 3, 2, 0.0F, true));
/*      */       
/* 1901 */       this.cube_r213 = new ModelRenderer((ModelBase)this);
/* 1902 */       this.cube_r213.func_78793_a(1.1201F, 1.7613F, 1.6965F);
/* 1903 */       this.LeftArm.func_78792_a(this.cube_r213);
/* 1904 */       setRotationAngle(this.cube_r213, 0.0F, -1.5272F, 0.0F);
/* 1905 */       this.cube_r213.field_78804_l.add(new ModelBox(this.cube_r213, 16, 11, 0.4F, -1.5F, -1.0F, 1, 3, 3, 0.0F, true));
/*      */       
/* 1907 */       this.cube_r214 = new ModelRenderer((ModelBase)this);
/* 1908 */       this.cube_r214.func_78793_a(3.9225F, 1.7613F, -0.5239F);
/* 1909 */       this.LeftArm.func_78792_a(this.cube_r214);
/* 1910 */       setRotationAngle(this.cube_r214, 0.0F, 0.1309F, 0.0F);
/* 1911 */       this.cube_r214.field_78804_l.add(new ModelBox(this.cube_r214, 18, 13, -0.7F, -1.5F, -0.4F, 1, 3, 1, 0.0F, true));
/*      */       
/* 1913 */       this.cube_r215 = new ModelRenderer((ModelBase)this);
/* 1914 */       this.cube_r215.func_78793_a(3.9225F, 1.7613F, 0.5875F);
/* 1915 */       this.LeftArm.func_78792_a(this.cube_r215);
/* 1916 */       setRotationAngle(this.cube_r215, 0.0F, -0.1309F, 0.0F);
/* 1917 */       this.cube_r215.field_78804_l.add(new ModelBox(this.cube_r215, 18, 13, -0.7F, -1.5F, -0.6F, 1, 3, 1, 0.0F, true));
/*      */       
/* 1919 */       this.cube_r216 = new ModelRenderer((ModelBase)this);
/* 1920 */       this.cube_r216.func_78793_a(1.4153F, 0.3805F, 0.0755F);
/* 1921 */       this.LeftArm.func_78792_a(this.cube_r216);
/* 1922 */       setRotationAngle(this.cube_r216, 0.0F, -1.5272F, -0.1745F);
/* 1923 */       this.cube_r216.field_78804_l.add(new ModelBox(this.cube_r216, 16, 11, 2.006F, -0.6917F, -0.6179F, 1, 1, 3, 0.0F, true));
/*      */       
/* 1925 */       this.cube_r217 = new ModelRenderer((ModelBase)this);
/* 1926 */       this.cube_r217.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1927 */       this.LeftArm.func_78792_a(this.cube_r217);
/* 1928 */       setRotationAngle(this.cube_r217, 0.0F, -1.0036F, -0.1745F);
/* 1929 */       this.cube_r217.field_78804_l.add(new ModelBox(this.cube_r217, 17, 12, 1.3984F, -0.3988F, -0.1445F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1931 */       this.cube_r218 = new ModelRenderer((ModelBase)this);
/* 1932 */       this.cube_r218.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1933 */       this.LeftArm.func_78792_a(this.cube_r218);
/* 1934 */       setRotationAngle(this.cube_r218, 0.0F, -0.3054F, -0.1745F);
/* 1935 */       this.cube_r218.field_78804_l.add(new ModelBox(this.cube_r218, 18, 13, 0.9258F, -0.3466F, 0.4324F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1937 */       this.cube_r219 = new ModelRenderer((ModelBase)this);
/* 1938 */       this.cube_r219.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1939 */       this.LeftArm.func_78792_a(this.cube_r219);
/* 1940 */       setRotationAngle(this.cube_r219, 0.0F, -0.1309F, -0.1745F);
/* 1941 */       this.cube_r219.field_78804_l.add(new ModelBox(this.cube_r219, 18, 13, 0.8195F, -0.3239F, -0.2395F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1943 */       this.cube_r220 = new ModelRenderer((ModelBase)this);
/* 1944 */       this.cube_r220.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1945 */       this.LeftArm.func_78792_a(this.cube_r220);
/* 1946 */       setRotationAngle(this.cube_r220, 0.0F, 0.1309F, -0.1745F);
/* 1947 */       this.cube_r220.field_78804_l.add(new ModelBox(this.cube_r220, 18, 13, 0.8195F, -0.3239F, -0.7605F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1949 */       this.cube_r221 = new ModelRenderer((ModelBase)this);
/* 1950 */       this.cube_r221.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1951 */       this.LeftArm.func_78792_a(this.cube_r221);
/* 1952 */       setRotationAngle(this.cube_r221, 0.0F, 0.3054F, -0.1745F);
/* 1953 */       this.cube_r221.field_78804_l.add(new ModelBox(this.cube_r221, 18, 13, 0.9258F, -0.3466F, -1.4324F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1955 */       this.cube_r222 = new ModelRenderer((ModelBase)this);
/* 1956 */       this.cube_r222.func_78793_a(2.3735F, -0.0859F, 0.0318F);
/* 1957 */       this.LeftArm.func_78792_a(this.cube_r222);
/* 1958 */       setRotationAngle(this.cube_r222, 0.0F, 1.0036F, -0.1745F);
/* 1959 */       this.cube_r222.field_78804_l.add(new ModelBox(this.cube_r222, 17, 12, 1.3984F, -0.3988F, -1.8555F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1961 */       this.cube_r223 = new ModelRenderer((ModelBase)this);
/* 1962 */       this.cube_r223.func_78793_a(1.4153F, 0.3805F, -0.0118F);
/* 1963 */       this.LeftArm.func_78792_a(this.cube_r223);
/* 1964 */       setRotationAngle(this.cube_r223, 0.0F, 1.5272F, -0.1745F);
/* 1965 */       this.cube_r223.field_78804_l.add(new ModelBox(this.cube_r223, 16, 11, 2.006F, -0.6917F, -2.3821F, 1, 1, 3, 0.0F, true));
/*      */       
/* 1967 */       this.cube_r224 = new ModelRenderer((ModelBase)this);
/* 1968 */       this.cube_r224.func_78793_a(1.2504F, -0.3665F, 0.0755F);
/* 1969 */       this.LeftArm.func_78792_a(this.cube_r224);
/* 1970 */       setRotationAngle(this.cube_r224, 0.0F, -1.5272F, -0.3491F);
/* 1971 */       this.cube_r224.field_78804_l.add(new ModelBox(this.cube_r224, 16, 11, 2.0052F, -0.5F, -0.6004F, 1, 1, 3, 0.0F, true));
/*      */       
/* 1973 */       this.cube_r225 = new ModelRenderer((ModelBase)this);
/* 1974 */       this.cube_r225.func_78793_a(2.1892F, -0.7082F, 0.0318F);
/* 1975 */       this.LeftArm.func_78792_a(this.cube_r225);
/* 1976 */       setRotationAngle(this.cube_r225, 0.0F, -1.0036F, -0.3491F);
/* 1977 */       this.cube_r225.field_78804_l.add(new ModelBox(this.cube_r225, 17, 12, 1.4028F, -0.5F, -0.1514F, 1, 1, 2, 0.0F, true));
/*      */       
/* 1979 */       this.cube_r226 = new ModelRenderer((ModelBase)this);
/* 1980 */       this.cube_r226.func_78793_a(2.2074F, -0.6053F, 0.0318F);
/* 1981 */       this.LeftArm.func_78792_a(this.cube_r226);
/* 1982 */       setRotationAngle(this.cube_r226, 0.0F, -0.3054F, -0.3491F);
/* 1983 */       this.cube_r226.field_78804_l.add(new ModelBox(this.cube_r226, 18, 13, 0.938F, -0.5F, 0.4285F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1985 */       this.cube_r227 = new ModelRenderer((ModelBase)this);
/* 1986 */       this.cube_r227.func_78793_a(2.2152F, -0.5607F, 0.0318F);
/* 1987 */       this.LeftArm.func_78792_a(this.cube_r227);
/* 1988 */       setRotationAngle(this.cube_r227, 0.0F, -0.1309F, -0.3491F);
/* 1989 */       this.cube_r227.field_78804_l.add(new ModelBox(this.cube_r227, 18, 13, 0.8341F, -0.5F, -0.2415F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1991 */       this.cube_r228 = new ModelRenderer((ModelBase)this);
/* 1992 */       this.cube_r228.func_78793_a(2.2152F, -0.5607F, 0.0318F);
/* 1993 */       this.LeftArm.func_78792_a(this.cube_r228);
/* 1994 */       setRotationAngle(this.cube_r228, 0.0F, 0.1309F, -0.3491F);
/* 1995 */       this.cube_r228.field_78804_l.add(new ModelBox(this.cube_r228, 18, 13, 0.8341F, -0.5F, -0.7585F, 1, 1, 1, 0.0F, true));
/*      */       
/* 1997 */       this.cube_r229 = new ModelRenderer((ModelBase)this);
/* 1998 */       this.cube_r229.func_78793_a(2.2074F, -0.6053F, 0.0318F);
/* 1999 */       this.LeftArm.func_78792_a(this.cube_r229);
/* 2000 */       setRotationAngle(this.cube_r229, 0.0F, 0.3054F, -0.3491F);
/* 2001 */       this.cube_r229.field_78804_l.add(new ModelBox(this.cube_r229, 18, 13, 0.938F, -0.5F, -1.4285F, 1, 1, 1, 0.0F, true));
/*      */       
/* 2003 */       this.cube_r230 = new ModelRenderer((ModelBase)this);
/* 2004 */       this.cube_r230.func_78793_a(2.4774F, -0.8131F, -2.0289F);
/* 2005 */       this.LeftArm.func_78792_a(this.cube_r230);
/* 2006 */       setRotationAngle(this.cube_r230, 0.0F, 1.0036F, -0.3491F);
/* 2007 */       this.cube_r230.field_78804_l.add(new ModelBox(this.cube_r230, 17, 12, -0.5F, -0.5F, -1.0F, 1, 1, 2, 0.0F, true));
/*      */       
/* 2009 */       this.cube_r231 = new ModelRenderer((ModelBase)this);
/* 2010 */       this.cube_r231.func_78793_a(1.4064F, -1.2231F, -2.2656F);
/* 2011 */       this.LeftArm.func_78792_a(this.cube_r231);
/* 2012 */       setRotationAngle(this.cube_r231, 0.5672F, 1.5272F, -0.3491F);
/* 2013 */       this.cube_r231.field_78804_l.add(new ModelBox(this.cube_r231, 17, 12, 0.075F, -0.5F, -1.5F, 1, 1, 2, -0.35F, true));
/*      */       
/* 2015 */       this.cube_r232 = new ModelRenderer((ModelBase)this);
/* 2016 */       this.cube_r232.func_78793_a(1.5254F, -1.0941F, -2.272F);
/* 2017 */       this.LeftArm.func_78792_a(this.cube_r232);
/* 2018 */       setRotationAngle(this.cube_r232, 0.3491F, 1.5272F, -0.3491F);
/* 2019 */       this.cube_r232.field_78804_l.add(new ModelBox(this.cube_r232, 17, 12, 0.075F, -0.5F, -1.5F, 1, 1, 2, -0.35F, true));
/*      */       
/* 2021 */       this.cube_r233 = new ModelRenderer((ModelBase)this);
/* 2022 */       this.cube_r233.func_78793_a(1.2504F, -0.3665F, -0.0118F);
/* 2023 */       this.LeftArm.func_78792_a(this.cube_r233);
/* 2024 */       setRotationAngle(this.cube_r233, 0.0F, 1.5272F, -0.3491F);
/* 2025 */       this.cube_r233.field_78804_l.add(new ModelBox(this.cube_r233, 16, 11, 2.0052F, -0.5F, -2.3996F, 1, 1, 3, 0.0F, true));
/*      */       
/* 2027 */       this.RightArm = new ModelRenderer((ModelBase)this);
/* 2028 */       this.RightArm.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 2029 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 17, 8, 0.0F, -2.0F, -2.0F, 1, 6, 4, 0.05F, false));
/*      */       
/* 2031 */       this.cube_r234 = new ModelRenderer((ModelBase)this);
/* 2032 */       this.cube_r234.func_78793_a(-2.2466F, 3.1951F, -2.1794F);
/* 2033 */       this.RightArm.func_78792_a(this.cube_r234);
/* 2034 */       setRotationAngle(this.cube_r234, 0.0883F, -1.0847F, 0.5005F);
/* 2035 */       this.cube_r234.field_78804_l.add(new ModelBox(this.cube_r234, 17, 12, -0.5F, -0.5F, -0.725F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2037 */       this.cube_r235 = new ModelRenderer((ModelBase)this);
/* 2038 */       this.cube_r235.func_78793_a(-1.0704F, 3.7569F, -2.4655F);
/* 2039 */       this.RightArm.func_78792_a(this.cube_r235);
/* 2040 */       setRotationAngle(this.cube_r235, -0.6104F, -1.5208F, 0.9604F);
/* 2041 */       this.cube_r235.field_78804_l.add(new ModelBox(this.cube_r235, 17, 12, -0.5F, -1.5F, -1.0F, 1, 2, 2, 0.0F, false));
/*      */       
/* 2043 */       this.cube_r236 = new ModelRenderer((ModelBase)this);
/* 2044 */       this.cube_r236.func_78793_a(0.4023F, 3.7012F, -2.5055F);
/* 2045 */       this.RightArm.func_78792_a(this.cube_r236);
/* 2046 */       setRotationAngle(this.cube_r236, -0.7413F, -1.5208F, 0.9604F);
/* 2047 */       this.cube_r236.field_78804_l.add(new ModelBox(this.cube_r236, 18, 13, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.0F, false));
/*      */       
/* 2049 */       this.cube_r237 = new ModelRenderer((ModelBase)this);
/* 2050 */       this.cube_r237.func_78793_a(-1.4163F, -1.22F, 2.3223F);
/* 2051 */       this.RightArm.func_78792_a(this.cube_r237);
/* 2052 */       setRotationAngle(this.cube_r237, -0.5672F, 1.5272F, 0.3491F);
/* 2053 */       this.cube_r237.field_78804_l.add(new ModelBox(this.cube_r237, 17, 12, -1.075F, -0.5F, -0.5F, 1, 1, 2, -0.35F, false));
/*      */       
/* 2055 */       this.cube_r238 = new ModelRenderer((ModelBase)this);
/* 2056 */       this.cube_r238.func_78793_a(-1.5353F, -1.0909F, 2.3287F);
/* 2057 */       this.RightArm.func_78792_a(this.cube_r238);
/* 2058 */       setRotationAngle(this.cube_r238, -0.3491F, 1.5272F, 0.3491F);
/* 2059 */       this.cube_r238.field_78804_l.add(new ModelBox(this.cube_r238, 17, 12, -1.075F, -0.5F, -0.5F, 1, 1, 2, -0.35F, false));
/*      */       
/* 2061 */       this.cube_r239 = new ModelRenderer((ModelBase)this);
/* 2062 */       this.cube_r239.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2063 */       this.RightArm.func_78792_a(this.cube_r239);
/* 2064 */       setRotationAngle(this.cube_r239, 1.0036F, 0.0F, -0.5236F);
/* 2065 */       this.cube_r239.field_78804_l.add(new ModelBox(this.cube_r239, 17, 12, -1.8945F, -2.5661F, -1.5923F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2067 */       this.cube_r240 = new ModelRenderer((ModelBase)this);
/* 2068 */       this.cube_r240.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2069 */       this.RightArm.func_78792_a(this.cube_r240);
/* 2070 */       setRotationAngle(this.cube_r240, 0.3054F, 0.0F, -0.5236F);
/* 2071 */       this.cube_r240.field_78804_l.add(new ModelBox(this.cube_r240, 18, 13, -1.7442F, -2.1894F, -1.3492F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2073 */       this.cube_r241 = new ModelRenderer((ModelBase)this);
/* 2074 */       this.cube_r241.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2075 */       this.RightArm.func_78792_a(this.cube_r241);
/* 2076 */       setRotationAngle(this.cube_r241, 0.1309F, 0.0F, -0.5236F);
/* 2077 */       this.cube_r241.field_78804_l.add(new ModelBox(this.cube_r241, 18, 13, -1.6789F, -2.0781F, -0.7264F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2079 */       this.cube_r242 = new ModelRenderer((ModelBase)this);
/* 2080 */       this.cube_r242.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2081 */       this.RightArm.func_78792_a(this.cube_r242);
/* 2082 */       setRotationAngle(this.cube_r242, -0.1309F, 0.0F, -0.5236F);
/* 2083 */       this.cube_r242.field_78804_l.add(new ModelBox(this.cube_r242, 18, 13, -1.6789F, -2.0781F, -0.2736F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2085 */       this.cube_r243 = new ModelRenderer((ModelBase)this);
/* 2086 */       this.cube_r243.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2087 */       this.RightArm.func_78792_a(this.cube_r243);
/* 2088 */       setRotationAngle(this.cube_r243, -0.3054F, 0.0F, -0.5236F);
/* 2089 */       this.cube_r243.field_78804_l.add(new ModelBox(this.cube_r243, 18, 13, -1.7442F, -2.1894F, 0.3492F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2091 */       this.cube_r244 = new ModelRenderer((ModelBase)this);
/* 2092 */       this.cube_r244.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2093 */       this.RightArm.func_78792_a(this.cube_r244);
/* 2094 */       setRotationAngle(this.cube_r244, -1.0036F, 0.0F, -0.5236F);
/* 2095 */       this.cube_r244.field_78804_l.add(new ModelBox(this.cube_r244, 17, 12, -1.8945F, -2.5661F, -0.4077F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2097 */       this.cube_r245 = new ModelRenderer((ModelBase)this);
/* 2098 */       this.cube_r245.func_78793_a(0.1072F, -1.0233F, 0.0685F);
/* 2099 */       this.RightArm.func_78792_a(this.cube_r245);
/* 2100 */       setRotationAngle(this.cube_r245, -1.5272F, 0.0F, -0.1745F);
/* 2101 */       this.cube_r245.field_78804_l.add(new ModelBox(this.cube_r245, 16, 11, -0.7378F, -3.0093F, -0.6937F, 2, 1, 3, 0.0F, false));
/*      */       
/* 2103 */       this.cube_r246 = new ModelRenderer((ModelBase)this);
/* 2104 */       this.cube_r246.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2105 */       this.RightArm.func_78792_a(this.cube_r246);
/* 2106 */       setRotationAngle(this.cube_r246, -1.0036F, 0.0F, -0.1745F);
/* 2107 */       this.cube_r246.field_78804_l.add(new ModelBox(this.cube_r246, 17, 12, -0.4449F, -2.4392F, -0.2085F, 2, 1, 2, 0.0F, false));
/*      */       
/* 2109 */       this.cube_r247 = new ModelRenderer((ModelBase)this);
/* 2110 */       this.cube_r247.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2111 */       this.RightArm.func_78792_a(this.cube_r247);
/* 2112 */       setRotationAngle(this.cube_r247, -0.3054F, 0.0F, -0.1745F);
/* 2113 */       this.cube_r247.field_78804_l.add(new ModelBox(this.cube_r247, 18, 13, -0.3927F, -1.9982F, 0.4095F, 2, 1, 1, 0.0F, false));
/*      */       
/* 2115 */       this.cube_r248 = new ModelRenderer((ModelBase)this);
/* 2116 */       this.cube_r248.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2117 */       this.RightArm.func_78792_a(this.cube_r248);
/* 2118 */       setRotationAngle(this.cube_r248, -0.1309F, 0.0F, -0.1745F);
/* 2119 */       this.cube_r248.field_78804_l.add(new ModelBox(this.cube_r248, 18, 13, -0.3701F, -1.8948F, -0.2495F, 2, 1, 1, 0.0F, false));
/*      */       
/* 2121 */       this.cube_r249 = new ModelRenderer((ModelBase)this);
/* 2122 */       this.cube_r249.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2123 */       this.RightArm.func_78792_a(this.cube_r249);
/* 2124 */       setRotationAngle(this.cube_r249, 0.1309F, 0.0F, -0.1745F);
/* 2125 */       this.cube_r249.field_78804_l.add(new ModelBox(this.cube_r249, 18, 13, -0.3701F, -1.8948F, -0.7505F, 2, 1, 1, 0.0F, false));
/*      */       
/* 2127 */       this.cube_r250 = new ModelRenderer((ModelBase)this);
/* 2128 */       this.cube_r250.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2129 */       this.RightArm.func_78792_a(this.cube_r250);
/* 2130 */       setRotationAngle(this.cube_r250, 0.3054F, 0.0F, -0.1745F);
/* 2131 */       this.cube_r250.field_78804_l.add(new ModelBox(this.cube_r250, 18, 13, -0.3927F, -1.9982F, -1.4095F, 2, 1, 1, 0.0F, false));
/*      */       
/* 2133 */       this.cube_r251 = new ModelRenderer((ModelBase)this);
/* 2134 */       this.cube_r251.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2135 */       this.RightArm.func_78792_a(this.cube_r251);
/* 2136 */       setRotationAngle(this.cube_r251, 1.0036F, 0.0F, -0.1745F);
/* 2137 */       this.cube_r251.field_78804_l.add(new ModelBox(this.cube_r251, 17, 12, -0.4449F, -2.4392F, -1.7915F, 2, 1, 2, 0.0F, false));
/*      */       
/* 2139 */       this.cube_r252 = new ModelRenderer((ModelBase)this);
/* 2140 */       this.cube_r252.func_78793_a(0.1072F, -1.0233F, -0.0188F);
/* 2141 */       this.RightArm.func_78792_a(this.cube_r252);
/* 2142 */       setRotationAngle(this.cube_r252, 1.5272F, 0.0F, -0.1745F);
/* 2143 */       this.cube_r252.field_78804_l.add(new ModelBox(this.cube_r252, 16, 11, -0.7378F, -3.0093F, -2.3063F, 2, 1, 3, 0.0F, false));
/*      */       
/* 2145 */       this.cube_r253 = new ModelRenderer((ModelBase)this);
/* 2146 */       this.cube_r253.func_78793_a(0.5594F, -1.1445F, 0.0685F);
/* 2147 */       this.RightArm.func_78792_a(this.cube_r253);
/* 2148 */       setRotationAngle(this.cube_r253, -1.5272F, 0.0F, -0.3491F);
/* 2149 */       this.cube_r253.field_78804_l.add(new ModelBox(this.cube_r253, 16, 11, -1.757F, -3.015F, -0.8244F, 1, 1, 3, 0.0F, false));
/*      */       
/* 2151 */       this.cube_r254 = new ModelRenderer((ModelBase)this);
/* 2152 */       this.cube_r254.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2153 */       this.RightArm.func_78792_a(this.cube_r254);
/* 2154 */       setRotationAngle(this.cube_r254, -1.0036F, 0.0F, -0.3491F);
/* 2155 */       this.cube_r254.field_78804_l.add(new ModelBox(this.cube_r254, 17, 12, -1.1801F, -2.4686F, -0.2546F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2157 */       this.cube_r255 = new ModelRenderer((ModelBase)this);
/* 2158 */       this.cube_r255.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2159 */       this.RightArm.func_78792_a(this.cube_r255);
/* 2160 */       setRotationAngle(this.cube_r255, -0.3054F, 0.0F, -0.3491F);
/* 2161 */       this.cube_r255.field_78804_l.add(new ModelBox(this.cube_r255, 18, 13, -1.0772F, -2.0374F, 0.3972F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2163 */       this.cube_r256 = new ModelRenderer((ModelBase)this);
/* 2164 */       this.cube_r256.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2165 */       this.RightArm.func_78792_a(this.cube_r256);
/* 2166 */       setRotationAngle(this.cube_r256, -0.1309F, 0.0F, -0.3491F);
/* 2167 */       this.cube_r256.field_78804_l.add(new ModelBox(this.cube_r256, 18, 13, -1.0326F, -1.9297F, -0.254F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2169 */       this.cube_r257 = new ModelRenderer((ModelBase)this);
/* 2170 */       this.cube_r257.func_78793_a(-0.7904F, -1.5503F, 0.0249F);
/* 2171 */       this.RightArm.func_78792_a(this.cube_r257);
/* 2172 */       setRotationAngle(this.cube_r257, 0.1309F, 0.0F, -0.3491F);
/* 2173 */       this.cube_r257.field_78804_l.add(new ModelBox(this.cube_r257, 18, 13, -1.0326F, -1.9297F, -0.746F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2175 */       this.cube_r258 = new ModelRenderer((ModelBase)this);
/* 2176 */       this.cube_r258.func_78793_a(-0.6599F, -1.6809F, 0.0249F);
/* 2177 */       this.RightArm.func_78792_a(this.cube_r258);
/* 2178 */       setRotationAngle(this.cube_r258, 0.3054F, 0.0F, -0.3491F);
/* 2179 */       this.cube_r258.field_78804_l.add(new ModelBox(this.cube_r258, 18, 13, -1.0772F, -2.0374F, -1.3972F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2181 */       this.cube_r259 = new ModelRenderer((ModelBase)this);
/* 2182 */       this.cube_r259.func_78793_a(-0.3592F, -1.9816F, 0.0249F);
/* 2183 */       this.RightArm.func_78792_a(this.cube_r259);
/* 2184 */       setRotationAngle(this.cube_r259, 1.0036F, 0.0F, -0.3491F);
/* 2185 */       this.cube_r259.field_78804_l.add(new ModelBox(this.cube_r259, 17, 12, -1.1801F, -2.4686F, -1.7454F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2187 */       this.cube_r260 = new ModelRenderer((ModelBase)this);
/* 2188 */       this.cube_r260.func_78793_a(0.5594F, -1.1445F, -0.0188F);
/* 2189 */       this.RightArm.func_78792_a(this.cube_r260);
/* 2190 */       setRotationAngle(this.cube_r260, 1.5272F, 0.0F, -0.3491F);
/* 2191 */       this.cube_r260.field_78804_l.add(new ModelBox(this.cube_r260, 16, 11, -1.757F, -3.015F, -2.1756F, 1, 1, 3, 0.0F, false));
/*      */       
/* 2193 */       this.cube_r261 = new ModelRenderer((ModelBase)this);
/* 2194 */       this.cube_r261.func_78793_a(-1.8107F, -1.5733F, 0.0249F);
/* 2195 */       this.RightArm.func_78792_a(this.cube_r261);
/* 2196 */       setRotationAngle(this.cube_r261, 0.0F, -1.0036F, 0.5236F);
/* 2197 */       this.cube_r261.field_78804_l.add(new ModelBox(this.cube_r261, 17, 12, -2.3853F, -0.2999F, -1.8761F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2199 */       this.cube_r262 = new ModelRenderer((ModelBase)this);
/* 2200 */       this.cube_r262.func_78793_a(-1.8153F, -1.5211F, 0.0249F);
/* 2201 */       this.RightArm.func_78792_a(this.cube_r262);
/* 2202 */       setRotationAngle(this.cube_r262, 0.0F, -0.3054F, 0.5236F);
/* 2203 */       this.cube_r262.field_78804_l.add(new ModelBox(this.cube_r262, 18, 13, -1.8896F, -0.1971F, -1.4438F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2205 */       this.cube_r263 = new ModelRenderer((ModelBase)this);
/* 2206 */       this.cube_r263.func_78793_a(-1.8173F, -1.4984F, 0.0249F);
/* 2207 */       this.RightArm.func_78792_a(this.cube_r263);
/* 2208 */       setRotationAngle(this.cube_r263, 0.0F, -0.1309F, 0.5236F);
/* 2209 */       this.cube_r263.field_78804_l.add(new ModelBox(this.cube_r263, 18, 13, -1.7761F, -0.1525F, -0.7662F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2211 */       this.cube_r264 = new ModelRenderer((ModelBase)this);
/* 2212 */       this.cube_r264.func_78793_a(-1.8173F, -1.4984F, 0.0249F);
/* 2213 */       this.RightArm.func_78792_a(this.cube_r264);
/* 2214 */       setRotationAngle(this.cube_r264, 0.0F, 0.1309F, 0.5236F);
/* 2215 */       this.cube_r264.field_78804_l.add(new ModelBox(this.cube_r264, 18, 13, -1.7761F, -0.1525F, -0.2338F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2217 */       this.cube_r265 = new ModelRenderer((ModelBase)this);
/* 2218 */       this.cube_r265.func_78793_a(-1.8153F, -1.5211F, 0.0249F);
/* 2219 */       this.RightArm.func_78792_a(this.cube_r265);
/* 2220 */       setRotationAngle(this.cube_r265, 0.0F, 0.3054F, 0.5236F);
/* 2221 */       this.cube_r265.field_78804_l.add(new ModelBox(this.cube_r265, 18, 13, -1.8896F, -0.1971F, 0.4438F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2223 */       this.cube_r266 = new ModelRenderer((ModelBase)this);
/* 2224 */       this.cube_r266.func_78793_a(-1.8107F, -1.5733F, 0.0249F);
/* 2225 */       this.RightArm.func_78792_a(this.cube_r266);
/* 2226 */       setRotationAngle(this.cube_r266, 0.0F, 1.0036F, 0.5236F);
/* 2227 */       this.cube_r266.field_78804_l.add(new ModelBox(this.cube_r266, 17, 12, -2.3853F, -0.2999F, -0.1239F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2229 */       this.cube_r267 = new ModelRenderer((ModelBase)this);
/* 2230 */       this.cube_r267.func_78793_a(-4.187F, 1.7644F, 0.3935F);
/* 2231 */       this.RightArm.func_78792_a(this.cube_r267);
/* 2232 */       setRotationAngle(this.cube_r267, 0.0F, -0.3054F, 0.0F);
/* 2233 */       this.cube_r267.field_78804_l.add(new ModelBox(this.cube_r267, 18, 13, -0.4F, -1.5F, -2.3F, 1, 3, 1, 0.0F, false));
/*      */       
/* 2235 */       this.cube_r268 = new ModelRenderer((ModelBase)this);
/* 2236 */       this.cube_r268.func_78793_a(-2.7648F, 1.7644F, -2.0359F);
/* 2237 */       this.RightArm.func_78792_a(this.cube_r268);
/* 2238 */       setRotationAngle(this.cube_r268, 0.0F, -1.0036F, 0.0F);
/* 2239 */       this.cube_r268.field_78804_l.add(new ModelBox(this.cube_r268, 17, 12, -0.5F, -1.5F, -1.0F, 1, 3, 2, 0.0F, false));
/*      */       
/* 2241 */       this.cube_r269 = new ModelRenderer((ModelBase)this);
/* 2242 */       this.cube_r269.func_78793_a(-1.13F, 1.7644F, -1.6398F);
/* 2243 */       this.RightArm.func_78792_a(this.cube_r269);
/* 2244 */       setRotationAngle(this.cube_r269, 0.0F, -1.5272F, 0.0F);
/* 2245 */       this.cube_r269.field_78804_l.add(new ModelBox(this.cube_r269, 16, 11, -1.4F, -1.5F, -2.0F, 1, 3, 3, 0.0F, false));
/*      */       
/* 2247 */       this.cube_r270 = new ModelRenderer((ModelBase)this);
/* 2248 */       this.cube_r270.func_78793_a(-4.187F, 1.7644F, -0.3438F);
/* 2249 */       this.RightArm.func_78792_a(this.cube_r270);
/* 2250 */       setRotationAngle(this.cube_r270, 0.0F, 0.3054F, 0.0F);
/* 2251 */       this.cube_r270.field_78804_l.add(new ModelBox(this.cube_r270, 18, 13, -0.4F, -1.5F, 1.3F, 1, 3, 1, 0.0F, false));
/*      */       
/* 2253 */       this.cube_r271 = new ModelRenderer((ModelBase)this);
/* 2254 */       this.cube_r271.func_78793_a(-2.7648F, 1.7644F, 2.0856F);
/* 2255 */       this.RightArm.func_78792_a(this.cube_r271);
/* 2256 */       setRotationAngle(this.cube_r271, 0.0F, 1.0036F, 0.0F);
/* 2257 */       this.cube_r271.field_78804_l.add(new ModelBox(this.cube_r271, 17, 12, -0.5F, -1.5F, -1.0F, 1, 3, 2, 0.0F, false));
/*      */       
/* 2259 */       this.cube_r272 = new ModelRenderer((ModelBase)this);
/* 2260 */       this.cube_r272.func_78793_a(-1.13F, 1.7644F, 1.6896F);
/* 2261 */       this.RightArm.func_78792_a(this.cube_r272);
/* 2262 */       setRotationAngle(this.cube_r272, 0.0F, 1.5272F, 0.0F);
/* 2263 */       this.cube_r272.field_78804_l.add(new ModelBox(this.cube_r272, 16, 11, -1.4F, -1.5F, -1.0F, 1, 3, 3, 0.0F, false));
/*      */       
/* 2265 */       this.cube_r273 = new ModelRenderer((ModelBase)this);
/* 2266 */       this.cube_r273.func_78793_a(-3.9324F, 1.7644F, -0.5308F);
/* 2267 */       this.RightArm.func_78792_a(this.cube_r273);
/* 2268 */       setRotationAngle(this.cube_r273, 0.0F, -0.1309F, 0.0F);
/* 2269 */       this.cube_r273.field_78804_l.add(new ModelBox(this.cube_r273, 18, 13, -0.3F, -1.5F, -0.4F, 1, 3, 1, 0.0F, false));
/*      */       
/* 2271 */       this.cube_r274 = new ModelRenderer((ModelBase)this);
/* 2272 */       this.cube_r274.func_78793_a(-3.9324F, 1.7644F, 0.5806F);
/* 2273 */       this.RightArm.func_78792_a(this.cube_r274);
/* 2274 */       setRotationAngle(this.cube_r274, 0.0F, 0.1309F, 0.0F);
/* 2275 */       this.cube_r274.field_78804_l.add(new ModelBox(this.cube_r274, 18, 13, -0.3F, -1.5F, -0.6F, 1, 3, 1, 0.0F, false));
/*      */       
/* 2277 */       this.cube_r275 = new ModelRenderer((ModelBase)this);
/* 2278 */       this.cube_r275.func_78793_a(-1.4251F, 0.3836F, 0.0685F);
/* 2279 */       this.RightArm.func_78792_a(this.cube_r275);
/* 2280 */       setRotationAngle(this.cube_r275, 0.0F, 1.5272F, 0.1745F);
/* 2281 */       this.cube_r275.field_78804_l.add(new ModelBox(this.cube_r275, 16, 11, -3.006F, -0.6917F, -0.6179F, 1, 1, 3, 0.0F, false));
/*      */       
/* 2283 */       this.cube_r276 = new ModelRenderer((ModelBase)this);
/* 2284 */       this.cube_r276.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2285 */       this.RightArm.func_78792_a(this.cube_r276);
/* 2286 */       setRotationAngle(this.cube_r276, 0.0F, 1.0036F, 0.1745F);
/* 2287 */       this.cube_r276.field_78804_l.add(new ModelBox(this.cube_r276, 17, 12, -2.3984F, -0.3988F, -0.1445F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2289 */       this.cube_r277 = new ModelRenderer((ModelBase)this);
/* 2290 */       this.cube_r277.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2291 */       this.RightArm.func_78792_a(this.cube_r277);
/* 2292 */       setRotationAngle(this.cube_r277, 0.0F, 0.3054F, 0.1745F);
/* 2293 */       this.cube_r277.field_78804_l.add(new ModelBox(this.cube_r277, 18, 13, -1.9258F, -0.3466F, 0.4324F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2295 */       this.cube_r278 = new ModelRenderer((ModelBase)this);
/* 2296 */       this.cube_r278.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2297 */       this.RightArm.func_78792_a(this.cube_r278);
/* 2298 */       setRotationAngle(this.cube_r278, 0.0F, 0.1309F, 0.1745F);
/* 2299 */       this.cube_r278.field_78804_l.add(new ModelBox(this.cube_r278, 18, 13, -1.8195F, -0.3239F, -0.2395F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2301 */       this.cube_r279 = new ModelRenderer((ModelBase)this);
/* 2302 */       this.cube_r279.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2303 */       this.RightArm.func_78792_a(this.cube_r279);
/* 2304 */       setRotationAngle(this.cube_r279, 0.0F, -0.1309F, 0.1745F);
/* 2305 */       this.cube_r279.field_78804_l.add(new ModelBox(this.cube_r279, 18, 13, -1.8195F, -0.3239F, -0.7605F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2307 */       this.cube_r280 = new ModelRenderer((ModelBase)this);
/* 2308 */       this.cube_r280.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2309 */       this.RightArm.func_78792_a(this.cube_r280);
/* 2310 */       setRotationAngle(this.cube_r280, 0.0F, -0.3054F, 0.1745F);
/* 2311 */       this.cube_r280.field_78804_l.add(new ModelBox(this.cube_r280, 18, 13, -1.9258F, -0.3466F, -1.4324F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2313 */       this.cube_r281 = new ModelRenderer((ModelBase)this);
/* 2314 */       this.cube_r281.func_78793_a(-2.3834F, -0.0828F, 0.0249F);
/* 2315 */       this.RightArm.func_78792_a(this.cube_r281);
/* 2316 */       setRotationAngle(this.cube_r281, 0.0F, -1.0036F, 0.1745F);
/* 2317 */       this.cube_r281.field_78804_l.add(new ModelBox(this.cube_r281, 17, 12, -2.3984F, -0.3988F, -1.8555F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2319 */       this.cube_r282 = new ModelRenderer((ModelBase)this);
/* 2320 */       this.cube_r282.func_78793_a(-1.4251F, 0.3836F, -0.0188F);
/* 2321 */       this.RightArm.func_78792_a(this.cube_r282);
/* 2322 */       setRotationAngle(this.cube_r282, 0.0F, -1.5272F, 0.1745F);
/* 2323 */       this.cube_r282.field_78804_l.add(new ModelBox(this.cube_r282, 16, 11, -3.006F, -0.6917F, -2.3821F, 1, 1, 3, 0.0F, false));
/*      */       
/* 2325 */       this.cube_r283 = new ModelRenderer((ModelBase)this);
/* 2326 */       this.cube_r283.func_78793_a(-1.2603F, -0.3633F, 0.0685F);
/* 2327 */       this.RightArm.func_78792_a(this.cube_r283);
/* 2328 */       setRotationAngle(this.cube_r283, 0.0F, 1.5272F, 0.3491F);
/* 2329 */       this.cube_r283.field_78804_l.add(new ModelBox(this.cube_r283, 16, 11, -3.0052F, -0.5F, -0.6004F, 1, 1, 3, 0.0F, false));
/*      */       
/* 2331 */       this.cube_r284 = new ModelRenderer((ModelBase)this);
/* 2332 */       this.cube_r284.func_78793_a(-2.1991F, -0.705F, 0.0249F);
/* 2333 */       this.RightArm.func_78792_a(this.cube_r284);
/* 2334 */       setRotationAngle(this.cube_r284, 0.0F, 1.0036F, 0.3491F);
/* 2335 */       this.cube_r284.field_78804_l.add(new ModelBox(this.cube_r284, 17, 12, -2.4028F, -0.5F, -0.1514F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2337 */       this.cube_r285 = new ModelRenderer((ModelBase)this);
/* 2338 */       this.cube_r285.func_78793_a(-2.2172F, -0.6022F, 0.0249F);
/* 2339 */       this.RightArm.func_78792_a(this.cube_r285);
/* 2340 */       setRotationAngle(this.cube_r285, 0.0F, 0.3054F, 0.3491F);
/* 2341 */       this.cube_r285.field_78804_l.add(new ModelBox(this.cube_r285, 18, 13, -1.938F, -0.5F, 0.4285F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2343 */       this.cube_r286 = new ModelRenderer((ModelBase)this);
/* 2344 */       this.cube_r286.func_78793_a(-2.2251F, -0.5575F, 0.0249F);
/* 2345 */       this.RightArm.func_78792_a(this.cube_r286);
/* 2346 */       setRotationAngle(this.cube_r286, 0.0F, 0.1309F, 0.3491F);
/* 2347 */       this.cube_r286.field_78804_l.add(new ModelBox(this.cube_r286, 18, 13, -1.8341F, -0.5F, -0.2415F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2349 */       this.cube_r287 = new ModelRenderer((ModelBase)this);
/* 2350 */       this.cube_r287.func_78793_a(-2.2251F, -0.5575F, 0.0249F);
/* 2351 */       this.RightArm.func_78792_a(this.cube_r287);
/* 2352 */       setRotationAngle(this.cube_r287, 0.0F, -0.1309F, 0.3491F);
/* 2353 */       this.cube_r287.field_78804_l.add(new ModelBox(this.cube_r287, 18, 13, -1.8341F, -0.5F, -0.7585F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2355 */       this.cube_r288 = new ModelRenderer((ModelBase)this);
/* 2356 */       this.cube_r288.func_78793_a(-2.2172F, -0.6022F, 0.0249F);
/* 2357 */       this.RightArm.func_78792_a(this.cube_r288);
/* 2358 */       setRotationAngle(this.cube_r288, 0.0F, -0.3054F, 0.3491F);
/* 2359 */       this.cube_r288.field_78804_l.add(new ModelBox(this.cube_r288, 18, 13, -1.938F, -0.5F, -1.4285F, 1, 1, 1, 0.0F, false));
/*      */       
/* 2361 */       this.cube_r289 = new ModelRenderer((ModelBase)this);
/* 2362 */       this.cube_r289.func_78793_a(-2.4872F, -0.8099F, -2.0359F);
/* 2363 */       this.RightArm.func_78792_a(this.cube_r289);
/* 2364 */       setRotationAngle(this.cube_r289, 0.0F, -1.0036F, 0.3491F);
/* 2365 */       this.cube_r289.field_78804_l.add(new ModelBox(this.cube_r289, 17, 12, -0.5F, -0.5F, -1.0F, 1, 1, 2, 0.0F, false));
/*      */       
/* 2367 */       this.cube_r290 = new ModelRenderer((ModelBase)this);
/* 2368 */       this.cube_r290.func_78793_a(-1.4163F, -1.22F, -2.2726F);
/* 2369 */       this.RightArm.func_78792_a(this.cube_r290);
/* 2370 */       setRotationAngle(this.cube_r290, 0.5672F, -1.5272F, 0.3491F);
/* 2371 */       this.cube_r290.field_78804_l.add(new ModelBox(this.cube_r290, 17, 12, -1.075F, -0.5F, -1.5F, 1, 1, 2, -0.35F, false));
/*      */       
/* 2373 */       this.cube_r291 = new ModelRenderer((ModelBase)this);
/* 2374 */       this.cube_r291.func_78793_a(-1.5353F, -1.0909F, -2.279F);
/* 2375 */       this.RightArm.func_78792_a(this.cube_r291);
/* 2376 */       setRotationAngle(this.cube_r291, 0.3491F, -1.5272F, 0.3491F);
/* 2377 */       this.cube_r291.field_78804_l.add(new ModelBox(this.cube_r291, 17, 12, -1.075F, -0.5F, -1.5F, 1, 1, 2, -0.35F, false));
/*      */       
/* 2379 */       this.cube_r292 = new ModelRenderer((ModelBase)this);
/* 2380 */       this.cube_r292.func_78793_a(-1.2603F, -0.3633F, -0.0188F);
/* 2381 */       this.RightArm.func_78792_a(this.cube_r292);
/* 2382 */       setRotationAngle(this.cube_r292, 0.0F, -1.5272F, 0.3491F);
/* 2383 */       this.cube_r292.field_78804_l.add(new ModelBox(this.cube_r292, 16, 11, -3.0052F, -0.5F, -2.3996F, 1, 1, 3, 0.0F, false));
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 2388 */       this.Head.func_78785_a(f5);
/* 2389 */       this.Body.func_78785_a(f5);
/* 2390 */       this.LeftArm.func_78785_a(f5);
/* 2391 */       this.RightArm.func_78785_a(f5);
/*      */     }
/*      */     
/*      */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 2395 */       modelRenderer.field_78795_f = x;
/* 2396 */       modelRenderer.field_78796_g = y;
/* 2397 */       modelRenderer.field_78808_h = z;
/*      */     }
/*      */     
/*      */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity e) {
/* 2401 */       super.func_78087_a(f, f1, f2, f3, f4, f5, e);
/*      */     }
/*      */   }
/*      */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemBaryonCloak.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */