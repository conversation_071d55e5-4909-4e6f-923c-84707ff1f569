/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.creativetab.CreativeTabs;
/*    */ import net.minecraft.item.EnumAction;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemFood;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ @Tag
/*    */ public class ItemHotdog
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   @ObjectHolder("borutomodaddononero:hotdog")
/* 22 */   public static final Item block = null;
/*    */   public ItemHotdog(ElementsBorutomodaddononeroMod instance) {
/* 24 */     super(instance, 14);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 29 */     this.elements.items.add(() -> new ItemFoodCustom());
/*    */   }
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 35 */     ModelLoader.setCustomModelResourceLocation(block, 0, new ModelResourceLocation("borutomodaddononero:hotdog", "inventory"));
/*    */   }
/*    */   
/*    */   public static class ItemFoodCustom extends ItemFood { public ItemFoodCustom() {
/* 39 */       super(5, 0.3F, false);
/* 40 */       func_77655_b("hotdog");
/* 41 */       setRegistryName("hotdog");
/* 42 */       func_77637_a(CreativeTabs.field_78039_h);
/* 43 */       func_77625_d(3);
/*    */     }
/*    */ 
/*    */     
/*    */     public EnumAction func_77661_b(ItemStack par1ItemStack) {
/* 48 */       return EnumAction.EAT;
/*    */     } }
/*    */ 
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemHotdog.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */