/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.attributes.IAttribute;
/*     */ import net.minecraft.entity.ai.attributes.IAttributeInstance;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.network.datasync.DataParameter;
/*     */ import net.minecraft.network.datasync.DataSerializers;
/*     */ import net.minecraft.network.datasync.EntityDataManager;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.EnumHand;
/*     */ import net.minecraft.util.EnumHandSide;
/*     */ import net.minecraft.util.NonNullList;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.world.World;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ @Tag
/*     */ public abstract class EntityShieldBase
/*     */   extends EntityLivingBase
/*     */ {
/*  28 */   private static final DataParameter<Integer> SUMMONERID = EntityDataManager.func_187226_a(EntityShieldBase.class, DataSerializers.field_187192_b);
/*     */   private EntityLivingBase summoner;
/*     */   private boolean ownerCanSteer = false;
/*     */   private float steerSpeed;
/*     */   protected boolean dieOnNoPassengers = true;
/*     */   
/*     */   public EntityShieldBase(World world) {
/*  35 */     super(world);
/*  36 */     this.field_70178_ae = true;
/*  37 */     func_174805_g(false);
/*     */   }
/*     */   
/*     */   public EntityShieldBase(EntityLivingBase summonerIn) {
/*  41 */     this(summonerIn, summonerIn.field_70165_t, summonerIn.field_70163_u, summonerIn.field_70161_v);
/*     */   }
/*     */   
/*     */   public EntityShieldBase(EntityLivingBase summonerIn, double x, double y, double z) {
/*  45 */     this(summonerIn.field_70170_p);
/*  46 */     setSummoner(summonerIn);
/*  47 */     func_70012_b(x, y, z, summonerIn.field_70177_z, summonerIn.field_70125_A);
/*  48 */     func_174805_g(false);
/*  49 */     summonerIn.func_184220_m((Entity)this);
/*     */   }
/*     */ 
/*     */   
/*     */   protected void func_70088_a() {
/*  54 */     super.func_70088_a();
/*  55 */     this.field_70180_af.func_187214_a(SUMMONERID, Integer.valueOf(-1));
/*     */   }
/*     */   
/*     */   protected void setSummoner(EntityLivingBase entity) {
/*  59 */     this.field_70180_af.func_187227_b(SUMMONERID, Integer.valueOf(entity.func_145782_y()));
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public EntityLivingBase getSummoner() {
/*  64 */     Entity entity = this.field_70170_p.func_73045_a(((Integer)func_184212_Q().func_187225_a(SUMMONERID)).intValue());
/*  65 */     return (entity instanceof EntityLivingBase) ? (EntityLivingBase)entity : null;
/*     */   }
/*     */ 
/*     */   
/*     */   public SoundEvent func_184601_bQ(DamageSource ds) {
/*  70 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   public SoundEvent func_184615_bR() {
/*  75 */     return null;
/*     */   }
/*     */ 
/*     */   
/*     */   protected float func_70599_aP() {
/*  80 */     return 1.0F;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean func_70097_a(DamageSource source, float amount) {
/*  85 */     if (source.func_76364_f() instanceof EntityLivingBase && source.func_76364_f().equals(func_184179_bs()))
/*  86 */       return false; 
/*  87 */     if (source == DamageSource.field_76379_h || source == DamageSource.field_76367_g || source == DamageSource.field_76368_d)
/*  88 */       return false; 
/*  89 */     return super.func_70097_a(source, amount);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean func_184230_a(EntityPlayer entity, EnumHand hand) {
/*  94 */     super.func_184230_a(entity, hand);
/*  95 */     if (!this.field_70170_p.field_72995_K && entity.equals(getSummoner())) {
/*  96 */       entity.func_184220_m((Entity)this);
/*  97 */       return true;
/*     */     } 
/*  99 */     return false;
/*     */   }
/*     */ 
/*     */   
/*     */   protected void func_110147_ax() {
/* 104 */     super.func_110147_ax();
/* 105 */     func_110140_aT().func_111150_b(ProcedureUtils.MAXHEALTH);
/* 106 */     func_110148_a(SharedMonsterAttributes.field_188791_g).func_111128_a(100.0D);
/* 107 */     func_110148_a(SharedMonsterAttributes.field_111263_d).func_111128_a(0.1D);
/* 108 */     func_110148_a(SharedMonsterAttributes.field_111267_a).func_111128_a(10.0D);
/* 109 */     func_110148_a(SharedMonsterAttributes.field_111266_c).func_111128_a(1.0D);
/*     */   }
/*     */ 
/*     */   
/*     */   public IAttributeInstance func_110148_a(IAttribute attribute) {
/* 114 */     return super.func_110148_a((attribute == SharedMonsterAttributes.field_111267_a) ? ProcedureUtils.MAXHEALTH : attribute);
/*     */   }
/*     */   
/*     */   protected void turnBodyAndHead(Entity passenger) {
/* 118 */     this.field_70177_z = passenger.field_70177_z;
/* 119 */     this.field_70126_B = this.field_70177_z;
/* 120 */     this.field_70125_A = passenger.field_70125_A;
/* 121 */     func_70101_b(this.field_70177_z, this.field_70125_A);
/* 122 */     this.field_70761_aq = passenger.field_70177_z;
/* 123 */     this.field_70759_as = passenger.field_70177_z;
/*     */   }
/*     */ 
/*     */   
/*     */   public void func_191986_a(float ti, float tj, float tk) {
/* 128 */     if (func_184207_aI()) {
/* 129 */       Entity entity = func_184179_bs();
/* 130 */       turnBodyAndHead(entity);
/* 131 */       if (entity instanceof EntityLivingBase && this.ownerCanSteer) {
/* 132 */         this.field_70747_aH = ((EntityLivingBase)entity).func_70689_ay() * 0.15F;
/* 133 */         func_70659_e((float)ProcedureUtils.getModifiedSpeed((EntityLivingBase)entity) * this.steerSpeed);
/* 134 */         float forward = ((EntityLivingBase)entity).field_191988_bg;
/* 135 */         float strafe = ((EntityLivingBase)entity).field_70702_br;
/* 136 */         super.func_191986_a(strafe, 0.0F, forward);
/*     */       } 
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public double func_70042_X() {
/* 143 */     return 0.35D;
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean shouldRiderSit() {
/* 148 */     return false;
/*     */   }
/*     */   
/*     */   public void setOwnerCanSteer(boolean canSteer, float speed) {
/* 152 */     this.ownerCanSteer = canSteer;
/* 153 */     this.steerSpeed = speed;
/*     */   }
/*     */ 
/*     */   
/*     */   public Entity func_184179_bs() {
/* 158 */     return func_184188_bt().isEmpty() ? null : func_184188_bt().get(0);
/*     */   }
/*     */ 
/*     */   
/*     */   public boolean shouldDismountInWater(Entity rider) {
/* 163 */     return false;
/*     */   }
/*     */   
/*     */   private void clampMotion(double d) {
/* 167 */     if (func_70643_av() != null && this.field_70173_aa - func_142015_aE() < 10) {
/* 168 */       if (Math.abs(this.field_70159_w) > d)
/* 169 */         this.field_70159_w = (this.field_70159_w > 0.0D) ? d : -d; 
/* 170 */       if (Math.abs(this.field_70181_x) > d)
/* 171 */         this.field_70181_x = (this.field_70181_x > 0.0D) ? d : -d; 
/* 172 */       if (Math.abs(this.field_70179_y) > d) {
/* 173 */         this.field_70179_y = (this.field_70179_y > 0.0D) ? d : -d;
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   public void func_70636_d() {
/* 179 */     super.func_70636_d();
/* 180 */     clampMotion(0.1D);
/* 181 */     EntityLivingBase summoner = getSummoner();
/* 182 */     if ((func_184188_bt().isEmpty() && this.dieOnNoPassengers) || (summoner != null && 
/* 183 */       !summoner.func_70089_S()))
/*     */     {
/* 185 */       func_70106_y();
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void func_70645_a(DamageSource cause) {
/* 191 */     if (!this.field_70729_aU) {
/* 192 */       this.field_70729_aU = true;
/* 193 */       this.field_70170_p.func_72960_a((Entity)this, (byte)3);
/*     */     } 
/*     */   }
/*     */ 
/*     */   
/*     */   public EnumHandSide func_184591_cq() {
/* 199 */     return EnumHandSide.RIGHT;
/*     */   }
/*     */ 
/*     */   
/*     */   public Iterable<ItemStack> func_184193_aE() {
/* 204 */     return (Iterable<ItemStack>)NonNullList.func_191197_a(1, ItemStack.field_190927_a);
/*     */   }
/*     */ 
/*     */   
/*     */   public ItemStack func_184582_a(EntityEquipmentSlot slotIn) {
/* 209 */     return ItemStack.field_190927_a;
/*     */   }
/*     */   
/*     */   public void func_184201_a(EntityEquipmentSlot slotIn, ItemStack stack) {}
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityShieldBase.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */