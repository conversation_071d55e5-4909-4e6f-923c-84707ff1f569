/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.Minecraft;
/*     */ import net.minecraft.client.entity.AbstractClientPlayer;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.client.event.RenderPlayerEvent;
/*     */ import net.minecraftforge.common.MinecraftForge;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.ReflectionHelper;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.entity.EntityClone;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ @Tag
/*     */ public class EntitySukunahikona extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 58;
/*     */   public static final int ENTITYID_RANGED = 59;
/*     */   
/*     */   public EntitySukunahikona(ElementsBorutomodaddononeroMod instance) {
/*  40 */     super(instance, 262);
/*     */   }
/*     */   
/*     */   public void initElements() {
/*  44 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(Sukunahikona.class).id(new ResourceLocation("borutomodaddononero", "sukunahikonaentity"), 58).name("sukunahikonaentity").tracker(64, 1, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  52 */     RenderingRegistry.registerEntityRenderingHandler(Sukunahikona.class, renderManager -> new RenderSmallerMe(renderManager));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  60 */     MinecraftForge.EVENT_BUS.register(new PlayerRenderHook());
/*     */   }
/*     */   
/*     */   public static class Sukunahikona extends EntityClone.Base {
/*  64 */     private final int growTime = 20;
/*     */     private float scale;
/*     */     private EntityLivingBase targetEntity;
/*     */     
/*     */     public Sukunahikona(World a) {
/*  69 */       super(a);
/*     */     }
/*     */     
/*     */     public Sukunahikona(EntityLivingBase user, float scaleIn) {
/*  73 */       super(user);
/*  74 */       this.scale = scaleIn;
/*  75 */       this.field_70138_W = scaleIn * this.field_70131_O / 3.0F;
/*  76 */       func_94061_f(true);
/*  77 */       double d = MathHelper.func_76133_a(4.0D * scaleIn * scaleIn + (this.field_70131_O * this.field_70131_O));
/*  78 */       func_110148_a(EntityPlayer.REACH_DISTANCE).func_111121_a(new AttributeModifier("sukunahikona.reach", d, 0));
/*  79 */       func_110148_a(SharedMonsterAttributes.field_111264_e).func_111121_a(new AttributeModifier("sukunahikona.damage", (scaleIn * scaleIn), 0));
/*  80 */       func_70690_d(new PotionEffect(MobEffects.field_76430_j, 999999, (int)scaleIn, false, false));
/*  81 */       user.func_184220_m((Entity)this);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_110147_ax() {
/*  86 */       super.func_110147_ax();
/*  87 */       func_110140_aT().func_111150_b(EntityPlayer.REACH_DISTANCE);
/*  88 */       func_110148_a(EntityPlayer.REACH_DISTANCE).func_111128_a(2.0D);
/*     */     }
/*     */ 
/*     */     
/*     */     public double func_70042_X() {
/*  93 */       return this.field_70131_O - 1.3D;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean shouldRiderSit() {
/*  98 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_82171_bF() {
/* 103 */       return true;
/*     */     }
/*     */ 
/*     */     
/*     */     public Entity func_184179_bs() {
/* 108 */       return func_184188_bt().isEmpty() ? null : func_184188_bt().get(0);
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70097_a(DamageSource source, float amount) {
/* 113 */       if (source.func_76346_g() != null && source.func_76346_g().equals(getSummoner())) {
/* 114 */         return false;
/*     */       }
/* 116 */       if (func_184179_bs() != null) {
/* 117 */         return func_184179_bs().func_70097_a(source, amount);
/*     */       }
/* 119 */       return super.func_70097_a(source, amount);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_191986_a(float ti, float tj, float tk) {
/* 124 */       if (func_184207_aI() && func_184186_bw()) {
/* 125 */         Entity entity = func_184179_bs();
/* 126 */         this.field_70177_z = entity.field_70177_z;
/* 127 */         this.field_70126_B = this.field_70177_z;
/* 128 */         this.field_70125_A = entity.field_70125_A;
/* 129 */         func_70101_b(this.field_70177_z, this.field_70125_A);
/* 130 */         this.field_70747_aH = func_70689_ay() * 0.15F;
/* 131 */         this.field_70761_aq = entity.field_70177_z;
/* 132 */         this.field_70759_as = entity.field_70177_z;
/* 133 */         this.field_70138_W = this.field_70131_O / 3.0F;
/* 134 */         if (entity instanceof EntityLivingBase) {
/* 135 */           checkJump((EntityLivingBase)entity);
/* 136 */           func_70659_e((float)ProcedureUtils.getModifiedSpeed((EntityLivingBase)this) / 3.5F);
/* 137 */           float forward = ((EntityLivingBase)entity).field_191988_bg;
/* 138 */           float strafe = ((EntityLivingBase)entity).field_70702_br;
/* 139 */           super.func_191986_a(strafe, 0.0F, forward);
/*     */         } 
/*     */       } else {
/* 142 */         this.field_70747_aH = 0.02F;
/* 143 */         super.func_191986_a(ti, tj, tk);
/*     */       } 
/*     */     }
/*     */     
/*     */     private void checkJump(EntityLivingBase entity) {
/* 148 */       if (this.field_70170_p.field_72995_K && (
/* 149 */         (Boolean)ReflectionHelper.getPrivateValue(EntityLivingBase.class, entity, 49)).booleanValue() && this.field_70122_E) {
/* 150 */         func_70664_aZ();
/* 151 */         ReflectionHelper.setPrivateValue(EntityLivingBase.class, entity, Boolean.valueOf(false), 49);
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/* 158 */       getClass(); if (!this.field_70170_p.field_72995_K && this.field_70173_aa <= 20) {
/* 159 */         getClass(); setScale(1.0F + (this.scale - 1.0F) * this.field_70173_aa / 20.0F);
/*     */       } 
/* 161 */       if (!func_184207_aI()) {
/* 162 */         func_70106_y();
/*     */       }
/* 164 */       super.func_70071_h_();
/*     */     }
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class RenderSmallerMe extends EntityClone.ClientRLM.RenderClone<Sukunahikona> {
/*     */     public RenderSmallerMe(RenderManager renderManager) {
/* 171 */       super(new EntityClone.ClientRLM(), renderManager);
/*     */     }
/*     */ 
/*     */     
/*     */     public void doRender(EntitySukunahikona.Sukunahikona entity, double x, double y, double z, float entityYaw, float partialTicks) {
/* 176 */       Entity passenger = entity.func_184179_bs();
/* 177 */       if (entity.func_184207_aI() && passenger instanceof AbstractClientPlayer) {
/* 178 */         copyLimbSwing(entity, (AbstractClientPlayer)passenger);
/*     */       }
/* 180 */       if (!Minecraft.func_71410_x().func_175606_aa().equals(passenger) || this.field_76990_c.field_78733_k.field_74320_O != 0) {
/* 181 */         super.doRender((EntityClone._Base)entity, x, y, z, entityYaw, partialTicks);
/*     */       }
/*     */     }
/*     */     
/*     */     private void copyLimbSwing(EntitySukunahikona.Sukunahikona entity, AbstractClientPlayer rider) {
/* 186 */       entity.field_70733_aJ = rider.field_70733_aJ;
/* 187 */       entity.field_110158_av = rider.field_110158_av;
/* 188 */       entity.field_70732_aI = rider.field_70732_aI;
/* 189 */       entity.field_82175_bq = rider.field_82175_bq;
/* 190 */       entity.field_184622_au = rider.field_184622_au;
/*     */     }
/*     */   }
/*     */   
/*     */   public class PlayerRenderHook {
/*     */     @SubscribeEvent
/*     */     @SideOnly(Side.CLIENT)
/*     */     public void onPlayerRender(RenderPlayerEvent.Pre event) {
/* 198 */       if (event.getEntityPlayer().func_184187_bx() instanceof EntitySukunahikona.Sukunahikona)
/* 199 */         event.setCanceled(true); 
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntitySukunahikona.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */