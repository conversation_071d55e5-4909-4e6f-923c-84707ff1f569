{"credit": "Made with Blockbench", "elements": [{"from": [7, 0, 7], "to": [9, 12, 9], "shade": false, "rotation": {"angle": 0, "axis": "y", "origin": [8, 3.5, 8]}, "faces": {"north": {"uv": [5, 2, 13, 16], "texture": "#0"}, "east": {"uv": [6, 2, 14, 16], "texture": "#0"}, "south": {"uv": [4, 2, 12, 16], "texture": "#0"}, "west": {"uv": [5, 2, 13, 16], "texture": "#0"}, "down": {"uv": [5, 6, 9, 16], "texture": "#0"}}}, {"from": [6, 6, 7], "to": [10, 12, 11], "shade": false, "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 9, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [3, 6, 13, 16], "texture": "#1"}}}, {"from": [6.25, 7.75, 8], "to": [9.75, 15.75, 11.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [4, 7, 12, 15], "texture": "#0"}}}, {"from": [6.5, 9.25, 7.5], "to": [9.5, 19.25, 10.5], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [4, 7, 12, 15], "texture": "#1"}}}, {"from": [6.5, 10.75, 7], "to": [9.5, 20.75, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 13, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [4, 8, 12, 16], "texture": "#0"}}}, {"from": [6.75, 12.5, 8.5], "to": [9.25, 22.5, 11], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 18, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [7, 10, 13, 16], "texture": "#1"}}}, {"from": [7, 14, 7.75], "to": [9, 24, 9.75], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 18, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [7, 10, 13, 16], "texture": "#0"}}}, {"from": [7, 15.75, 8], "to": [9, 25.75, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 20, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [5, 10, 11, 16], "texture": "#1"}}}, {"from": [7, 17.5, 8.25], "to": [9, 27.5, 10.25], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 22, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [4, 9, 10, 15], "texture": "#0"}}}, {"from": [7.25, 19.5, 8.5], "to": [8.75, 29.5, 10], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 24, 8]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#1"}, "east": {"uv": [0, 0, 16, 16], "texture": "#1"}, "south": {"uv": [0, 0, 16, 16], "texture": "#1"}, "west": {"uv": [0, 0, 16, 16], "texture": "#1"}, "down": {"uv": [6, 9, 12, 15], "texture": "#1"}}}, {"from": [7.5, 20.46424, 9.04343], "to": [8.5, 30.46424, 10.04343], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, 26.46424, 9.29343]}, "faces": {"north": {"uv": [0, 0, 16, 16], "texture": "#0"}, "east": {"uv": [0, 0, 16, 16], "texture": "#0"}, "south": {"uv": [0, 0, 16, 16], "texture": "#0"}, "west": {"uv": [0, 0, 16, 16], "texture": "#0"}, "down": {"uv": [3, 8, 9, 14], "texture": "#0"}}}, {"from": [6, -2, 6], "to": [10, 2, 10], "shade": false, "rotation": {"angle": 22.5, "axis": "x", "origin": [8, -4, 8]}, "faces": {"north": {"uv": [10, 12, 14, 16], "rotation": 180, "texture": "#0"}, "east": {"uv": [5, 10, 11, 16], "rotation": 90, "texture": "#0"}, "south": {"uv": [4, 10, 10, 16], "texture": "#0"}, "west": {"uv": [1, 10, 7, 16], "rotation": 270, "texture": "#0"}, "up": {"uv": [7, 9, 11, 13], "texture": "#0"}, "down": {"uv": [3, 10, 9, 16], "rotation": 180, "texture": "#0"}}}, {"from": [5.5, -2.5, 10], "to": [10.5, 2.5, 14], "rotation": {"angle": 22.5, "axis": "x", "origin": [8, -4, 8]}, "faces": {"north": {"uv": [6, 11, 11, 16], "rotation": 180, "texture": "#1"}, "east": {"uv": [8, 10, 14, 16], "rotation": 90, "texture": "#1"}, "south": {"uv": [5, 9, 12, 16], "texture": "#1"}, "west": {"uv": [1, 10, 7, 16], "rotation": 270, "texture": "#1"}, "up": {"uv": [6, 10, 12, 16], "texture": "#1"}, "down": {"uv": [7, 10, 13, 16], "rotation": 180, "texture": "#1"}}}], "display": {"thirdperson_righthand": {"translation": [0, 2, 1.5]}, "thirdperson_lefthand": {"translation": [0, 2, 1.5]}, "firstperson_righthand": {"rotation": [-15, 0, 0]}, "firstperson_lefthand": {"rotation": [-15, 0, 0]}, "gui": {"rotation": [-90, -45, -90], "translation": [-0.5, -3.25, 0], "scale": [0.6, 0.6, 0.6]}, "fixed": {"rotation": [90, 135, -90], "translation": [0.25, -3.5, 0], "scale": [0.8, 0.8, 0.8]}}}