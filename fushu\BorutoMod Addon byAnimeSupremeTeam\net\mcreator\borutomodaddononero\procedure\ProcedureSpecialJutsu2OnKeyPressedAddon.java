/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemKokugan;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.world.World;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSpecialJutsu2OnKeyPressedAddon
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSpecialJutsu2OnKeyPressedAddon(ElementsBorutomodaddononeroMod instance) {
/* 17 */     super(instance, 107);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 21 */     if (dependencies.get("is_pressed") == null) {
/* 22 */       System.err.println("Failed to load dependency is_pressed for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 25 */     if (dependencies.get("entity") == null) {
/* 26 */       System.err.println("Failed to load dependency entity for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 29 */     if (dependencies.get("x") == null) {
/* 30 */       System.err.println("Failed to load dependency x for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 33 */     if (dependencies.get("y") == null) {
/* 34 */       System.err.println("Failed to load dependency y for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 37 */     if (dependencies.get("z") == null) {
/* 38 */       System.err.println("Failed to load dependency z for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 41 */     if (dependencies.get("world") == null) {
/* 42 */       System.err.println("Failed to load dependency world for procedure SpecialJutsu2OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 45 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 46 */     Entity entity = (Entity)dependencies.get("entity");
/* 47 */     int x = ((Integer)dependencies.get("x")).intValue();
/* 48 */     int y = ((Integer)dependencies.get("y")).intValue();
/* 49 */     int z = ((Integer)dependencies.get("z")).intValue();
/* 50 */     World world = (World)dependencies.get("world");
/* 51 */     if (!world.field_72995_K && entity instanceof EntityPlayer) {
/* 52 */       EntityPlayer player = (EntityPlayer)entity;
/* 53 */       ItemStack helmet = (ItemStack)player.field_71071_by.field_70460_b.get(3);
/* 54 */       ItemStack chestplate = (ItemStack)player.field_71071_by.field_70460_b.get(2);
/* 55 */       if (!helmet.func_190926_b() && helmet.func_77973_b() == ItemKokugan.helmet && 
/* 56 */         is_pressed) {
/* 57 */         Map<String, Object> $_dependencies = new HashMap<>();
/* 58 */         $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 59 */         $_dependencies.put("entity", entity);
/* 60 */         $_dependencies.put("x", Integer.valueOf(x));
/* 61 */         $_dependencies.put("y", Integer.valueOf(y));
/* 62 */         $_dependencies.put("z", Integer.valueOf(z));
/* 63 */         $_dependencies.put("world", world);
/* 64 */         ProcedureDaikokutenCube.executeProcedure($_dependencies);
/*    */       } 
/*    */       
/* 67 */       Map<String, Object> $_dependencies2 = new HashMap<>();
/* 68 */       $_dependencies2.put("is_pressed", Boolean.valueOf(is_pressed));
/* 69 */       $_dependencies2.put("entity", entity);
/* 70 */       ProcedureBaryonEquipCloak.executeProcedure($_dependencies2);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSpecialJutsu2OnKeyPressedAddon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */