/*      */ package net.mcreator.borutomodaddononero.entity;
/*      */ 
/*      */ import com.google.common.collect.Lists;
/*      */ import com.google.common.collect.Maps;
/*      */ import io.netty.buffer.ByteBuf;
/*      */ import java.io.IOException;
/*      */ import java.util.Arrays;
/*      */ import java.util.Iterator;
/*      */ import java.util.List;
/*      */ import java.util.Map;
/*      */ import net.mcreator.borutomodaddononero.BorutomodaddononeroMod;
/*      */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*      */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*      */ import net.minecraft.block.material.Material;
/*      */ import net.minecraft.client.Minecraft;
/*      */ import net.minecraft.client.model.ModelBiped;
/*      */ import net.minecraft.client.renderer.GlStateManager;
/*      */ import net.minecraft.client.renderer.block.model.ItemCameraTransforms;
/*      */ import net.minecraft.client.renderer.entity.RenderBiped;
/*      */ import net.minecraft.client.renderer.entity.RenderLivingBase;
/*      */ import net.minecraft.client.renderer.entity.RenderManager;
/*      */ import net.minecraft.client.renderer.entity.layers.LayerRenderer;
/*      */ import net.minecraft.entity.Entity;
/*      */ import net.minecraft.entity.EntityCreature;
/*      */ import net.minecraft.entity.EntityLiving;
/*      */ import net.minecraft.entity.EntityLivingBase;
/*      */ import net.minecraft.entity.IRangedAttackMob;
/*      */ import net.minecraft.entity.SharedMonsterAttributes;
/*      */ import net.minecraft.entity.ai.EntityAIBase;
/*      */ import net.minecraft.entity.ai.EntityAITarget;
/*      */ import net.minecraft.entity.ai.EntityMoveHelper;
/*      */ import net.minecraft.entity.player.EntityPlayer;
/*      */ import net.minecraft.entity.player.EntityPlayerMP;
/*      */ import net.minecraft.init.SoundEvents;
/*      */ import net.minecraft.inventory.EntityEquipmentSlot;
/*      */ import net.minecraft.item.EnumAction;
/*      */ import net.minecraft.item.ItemStack;
/*      */ import net.minecraft.nbt.NBTBase;
/*      */ import net.minecraft.nbt.NBTTagCompound;
/*      */ import net.minecraft.nbt.NBTTagList;
/*      */ import net.minecraft.network.PacketBuffer;
/*      */ import net.minecraft.network.datasync.DataParameter;
/*      */ import net.minecraft.network.datasync.DataSerializers;
/*      */ import net.minecraft.network.datasync.EntityDataManager;
/*      */ import net.minecraft.pathfinding.Path;
/*      */ import net.minecraft.pathfinding.PathNavigate;
/*      */ import net.minecraft.pathfinding.PathNavigateGround;
/*      */ import net.minecraft.potion.PotionEffect;
/*      */ import net.minecraft.util.DamageSource;
/*      */ import net.minecraft.util.EnumHandSide;
/*      */ import net.minecraft.util.NonNullList;
/*      */ import net.minecraft.util.SoundCategory;
/*      */ import net.minecraft.util.SoundEvent;
/*      */ import net.minecraft.util.math.AxisAlignedBB;
/*      */ import net.minecraft.util.math.BlockPos;
/*      */ import net.minecraft.util.math.MathHelper;
/*      */ import net.minecraft.util.math.Vec3d;
/*      */ import net.minecraft.util.math.Vec3i;
/*      */ import net.minecraft.world.EnumDifficulty;
/*      */ import net.minecraft.world.EnumSkyBlock;
/*      */ import net.minecraft.world.World;
/*      */ import net.minecraftforge.common.MinecraftForge;
/*      */ import net.minecraftforge.event.entity.living.LivingSpawnEvent;
/*      */ import net.minecraftforge.event.entity.player.PlayerEvent;
/*      */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*      */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*      */ import net.minecraftforge.fml.common.eventhandler.Event;
/*      */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*      */ import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
/*      */ import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
/*      */ import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
/*      */ import net.minecraftforge.fml.relauncher.Side;
/*      */ import net.minecraftforge.fml.relauncher.SideOnly;
/*      */ import net.narutomod.Chakra;
/*      */ import net.narutomod.PlayerRender;
/*      */ import net.narutomod.entity.EntityClone;
/*      */ import net.narutomod.item.ItemOnBody;
/*      */ import net.narutomod.potion.PotionFeatherFalling;
/*      */ import net.narutomod.procedure.ProcedureUtils;
/*      */ 
/*      */ 
/*      */ @Tag
/*      */ public class EntityNinjaMobAddon
/*      */   extends ElementsBorutomodaddononeroMod.ModElement
/*      */ {
/*   86 */   public static final List<Class<? extends Base>> TeamOtsutsuki = Arrays.asList((Class<? extends Base>[])new Class[] { EntityIsshiki.EntityCustom.class });
/*      */   
/*      */   public EntityNinjaMobAddon(ElementsBorutomodaddononeroMod instance) {
/*   89 */     super(instance, 404);
/*      */   }
/*      */   
/*      */   protected static class SpawnData {
/*   93 */     protected static final Map<Class<? extends EntityNinjaMobAddon.Base>, List<SpawnData>> map = Maps.newHashMap();
/*      */     protected World world;
/*      */     protected AxisAlignedBB area;
/*      */     protected long time;
/*      */     
/*      */     SpawnData(EntityNinjaMobAddon.Base entity) {
/*   99 */       this.world = entity.field_70170_p;
/*  100 */       this.area = entity.func_174813_aQ().func_72314_b(512.0D, 64.0D, 512.0D);
/*  101 */       this.time = entity.field_70170_p.func_82737_E();
/*      */     }
/*      */     
/*      */     protected static void addSpawnData(EntityNinjaMobAddon.Base entity) {
/*  105 */       List<SpawnData> spawndatalist = map.get(entity.getClass());
/*  106 */       if (spawndatalist == null) {
/*  107 */         spawndatalist = Lists.newArrayList();
/*      */       }
/*  109 */       spawndatalist.add(new SpawnData(entity));
/*  110 */       map.put(entity.getClass(), spawndatalist);
/*      */     }
/*      */     
/*      */     protected static boolean spawnedRecentlyHere(EntityNinjaMobAddon.Base entity, long interval) {
/*  114 */       List<SpawnData> spawndatalist = map.get(entity.getClass());
/*  115 */       if (spawndatalist != null) {
/*  116 */         Iterator<SpawnData> iter = spawndatalist.iterator();
/*  117 */         while (iter.hasNext()) {
/*  118 */           SpawnData spawndata = iter.next();
/*  119 */           if (entity.field_70170_p == spawndata.world && spawndata.area.func_72318_a(entity.func_174791_d())) {
/*  120 */             if (spawndata.world.func_82737_E() - spawndata.time <= interval) {
/*  121 */               return true;
/*      */             }
/*  123 */             iter.remove();
/*      */           } 
/*      */         } 
/*      */       } 
/*      */       
/*  128 */       return false;
/*      */     }
/*      */   }
/*      */   
/*      */   public static abstract class Base extends EntityCreature {
/*  133 */     private static final DataParameter<Float> CHAKRA_MAX = EntityDataManager.func_187226_a(Base.class, DataSerializers.field_187193_c);
/*  134 */     private static final DataParameter<Float> CHAKRA = EntityDataManager.func_187226_a(Base.class, DataSerializers.field_187193_c);
/*      */     private final PathwayNinjaMob chakraPathway;
/*  136 */     private final int inventorySize = 2;
/*  137 */     private final NonNullList<ItemStack> inventory = NonNullList.func_191197_a(2, ItemStack.field_190927_a);
/*      */     public int peacefulTicks;
/*      */     private int standStillTicks;
/*      */     private float haltedYaw;
/*      */     private float haltedYawHead;
/*      */     private float haltedPitch;
/*      */     
/*      */     public Base(World worldIn, int level, double chakraAmountIn) {
/*  145 */       super(worldIn);
/*  146 */       func_70105_a(0.6F, 1.8F);
/*  147 */       this.field_70728_aV = level;
/*  148 */       this.field_70178_ae = false;
/*  149 */       this.field_70138_W = 16.0F;
/*  150 */       this.field_70765_h = new EntityNinjaMobAddon.MoveHelper((EntityLiving)this);
/*  151 */       func_94061_f(false);
/*  152 */       func_98053_h(false);
/*  153 */       func_96094_a(func_70005_c_());
/*  154 */       func_174805_g(true);
/*  155 */       this.chakraPathway = new PathwayNinjaMob(this, chakraAmountIn);
/*  156 */       func_110148_a(SharedMonsterAttributes.field_111267_a).func_111128_a(50.0D + 0.005D * level * level);
/*  157 */       func_70606_j(func_110138_aP());
/*      */     }
/*      */ 
/*      */     
/*      */     protected PathNavigate func_175447_b(World worldIn) {
/*  162 */       PathNavigateGround navi = new EntityNinjaMobAddon.NavigateGround((EntityLiving)this, worldIn);
/*  163 */       navi.func_179693_d(true);
/*  164 */       return (PathNavigate)navi;
/*      */     }
/*      */ 
/*      */     
/*      */     protected void func_70088_a() {
/*  169 */       super.func_70088_a();
/*  170 */       func_184212_Q().func_187214_a(CHAKRA_MAX, Float.valueOf(0.0F));
/*  171 */       func_184212_Q().func_187214_a(CHAKRA, Float.valueOf(0.0F));
/*      */     }
/*      */ 
/*      */     
/*      */     protected void func_110147_ax() {
/*  176 */       super.func_110147_ax();
/*  177 */       func_110140_aT().func_111150_b(SharedMonsterAttributes.field_111264_e);
/*  178 */       func_110148_a(SharedMonsterAttributes.field_188791_g).func_111128_a(10.0D);
/*      */       
/*  180 */       func_110148_a(SharedMonsterAttributes.field_111265_b).func_111128_a(64.0D);
/*      */     }
/*      */ 
/*      */     
/*      */     public int func_82143_as() {
/*  185 */       return 12;
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_184652_a(EntityPlayer player) {
/*  190 */       return player.func_184812_l_();
/*      */     }
/*      */     
/*      */     public int getNinjaLevel() {
/*  194 */       return this.field_70728_aV;
/*      */     }
/*      */     
/*      */     public PathwayNinjaMob getChakraPathway() {
/*  198 */       return this.chakraPathway;
/*      */     }
/*      */     
/*      */     public double getChakra() {
/*  202 */       return this.chakraPathway.getAmount();
/*      */     }
/*      */     
/*      */     public float remainingChakra() {
/*  206 */       return (float)(getChakra() / this.chakraPathway.getMax());
/*      */     }
/*      */     
/*      */     public boolean consumeChakra(double amount) {
/*  210 */       return this.chakraPathway.consume(amount);
/*      */     }
/*      */     
/*      */     private void fixOnClientSpawn() {
/*  214 */       if (this.field_70170_p.field_72995_K && this.field_70173_aa < 20) {
/*  215 */         this.chakraPathway.fixOnClientSpawn();
/*      */       }
/*      */     }
/*      */ 
/*      */     
/*      */     protected void func_70619_bc() {
/*  221 */       super.func_70619_bc();
/*  222 */       EntityLivingBase target = func_70638_az();
/*      */ 
/*      */ 
/*      */ 
/*      */       
/*  227 */       if (ProcedureUtils.isWeapon(getItemFromInventory(0)) || ProcedureUtils.isWeapon(func_184614_ca())) {
/*      */         
/*  229 */         boolean flag = (func_70643_av() != null || target != null || (func_110144_aD() != null && this.field_70173_aa <= func_142013_aG() + 100));
/*  230 */         if (func_184614_ca().func_190926_b() == flag) {
/*  231 */           swapWithInventory(EntityEquipmentSlot.MAINHAND, 0);
/*      */         }
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_70071_h_() {
/*  238 */       fixOnClientSpawn();
/*  239 */       super.func_70071_h_();
/*  240 */       BlockPos pos = new BlockPos((Entity)this);
/*  241 */       if (this.field_70699_by instanceof PathNavigateGround && this.field_70170_p
/*  242 */         .func_180495_p(pos).func_185904_a() == Material.field_151586_h && this.field_70170_p
/*  243 */         .func_180495_p(pos.func_177984_a()).func_185904_a() != Material.field_151586_h) {
/*  244 */         this.field_70181_x = 0.01D;
/*  245 */         this.field_70122_E = true;
/*      */       } 
/*  247 */       if (!this.field_70170_p.field_72995_K && func_70089_S()) {
/*  248 */         if (this.field_70173_aa % 200 == 1) {
/*  249 */           func_70690_d(new PotionEffect(PotionFeatherFalling.potion, 201, 1, false, false));
/*      */         }
/*  251 */         this.chakraPathway.onUpdate();
/*  252 */         if (this instanceof net.minecraft.entity.monster.IMob && this.field_70170_p.func_175659_aa() == EnumDifficulty.PEACEFUL) {
/*  253 */           func_70106_y();
/*      */         }
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_191986_a(float strafe, float vertical, float forward) {
/*  260 */       if (this.standStillTicks > 0) {
/*  261 */         vertical = forward = strafe = 0.0F;
/*  262 */         this.field_70177_z = this.haltedYaw;
/*  263 */         this.field_70759_as = this.haltedYawHead;
/*  264 */         this.field_70125_A = this.haltedPitch;
/*  265 */         this.standStillTicks--;
/*      */       } 
/*  267 */       super.func_191986_a(strafe, vertical, forward);
/*      */     }
/*      */     
/*      */     protected void standStillFor(int ticks) {
/*  271 */       this.standStillTicks = ticks;
/*  272 */       this.haltedYaw = this.field_70177_z;
/*  273 */       this.haltedYawHead = this.field_70759_as;
/*  274 */       this.haltedPitch = this.field_70125_A;
/*  275 */       if (!this.field_70170_p.field_72995_K) {
/*  276 */         EntityNinjaMobAddon.StandStillMessage.sendToTracking(this);
/*      */       }
/*      */     }
/*      */     
/*      */     protected boolean isStandingStill() {
/*  281 */       return (this.standStillTicks > 0);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_70636_d() {
/*  286 */       decrementAnimations();
/*  287 */       super.func_70636_d();
/*      */     }
/*      */ 
/*      */     
/*      */     protected float func_189749_co() {
/*  292 */       return 0.98F;
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_70652_k(Entity entityIn) {
/*  297 */       return ProcedureUtils.attackEntityAsMob((EntityLivingBase)this, entityIn);
/*      */     }
/*      */     
/*      */     protected void decrementAnimations() {
/*  301 */       func_82168_bl();
/*  302 */       for (ItemStack stack : func_184214_aD()) {
/*  303 */         if (!stack.func_190926_b())
/*  304 */           stack.func_77945_a(this.field_70170_p, (Entity)this, 0, false); 
/*      */       } 
/*  306 */       for (ItemStack stack : this.inventory) {
/*  307 */         if (!stack.func_190926_b())
/*  308 */           stack.func_77945_a(this.field_70170_p, (Entity)this, 0, false); 
/*      */       } 
/*      */     }
/*      */     
/*      */     public ItemStack getItemFromInventory(int slotno) {
/*  313 */       return (slotno >= 0 && slotno < this.inventory.size()) ? (ItemStack)this.inventory.get(slotno) : ItemStack.field_190927_a;
/*      */     }
/*      */     
/*      */     public void setItemToInventory(ItemStack stack, int slotno) {
/*  317 */       if (slotno >= 0 && slotno < this.inventory.size()) {
/*  318 */         this.inventory.set(slotno, stack);
/*      */       }
/*      */     }
/*      */     
/*      */     public void swapWithInventory(EntityEquipmentSlot slot, int slotno) {
/*  323 */       ItemStack stack = (ItemStack)this.inventory.get(slotno);
/*  324 */       this.inventory.set(slotno, func_184582_a(slot));
/*  325 */       func_184201_a(slot, stack);
/*  326 */       EntityNinjaMobAddon.InventoryMessage.sendToTracking(this);
/*      */     }
/*      */     
/*      */     public int getInventorySize() {
/*  330 */       getClass(); return 2;
/*      */     }
/*      */     
/*      */     protected boolean isValidLightLevel() {
/*  334 */       BlockPos blockpos = new BlockPos(this.field_70165_t, (func_174813_aQ()).field_72338_b, this.field_70161_v);
/*  335 */       if (this.field_70170_p.func_175642_b(EnumSkyBlock.SKY, blockpos) > this.field_70146_Z.nextInt(32)) {
/*  336 */         return false;
/*      */       }
/*  338 */       int i = this.field_70170_p.func_175671_l(blockpos);
/*  339 */       if (this.field_70170_p.func_72911_I()) {
/*  340 */         int j = this.field_70170_p.func_175657_ab();
/*  341 */         this.field_70170_p.func_175692_b(10);
/*  342 */         i = this.field_70170_p.func_175671_l(blockpos);
/*  343 */         this.field_70170_p.func_175692_b(j);
/*      */       } 
/*  345 */       return (i <= this.field_70146_Z.nextInt(8));
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*      */     public boolean func_70601_bi() {
/*  351 */       return (super.func_70601_bi() && (!(this instanceof net.minecraft.entity.monster.IMob) || this.field_70170_p.func_175659_aa() != EnumDifficulty.PEACEFUL));
/*      */     }
/*      */ 
/*      */     
/*      */     public void onRemovedFromWorld() {
/*  356 */       super.onRemovedFromWorld();
/*  357 */       if (!this.field_70170_p.field_72995_K) {
/*  358 */         EntityNinjaMobAddon.SpawnData.addSpawnData(this);
/*      */       }
/*      */     }
/*      */ 
/*      */     
/*      */     public SoundCategory func_184176_by() {
/*  364 */       return (this instanceof net.minecraft.entity.monster.IMob) ? SoundCategory.HOSTILE : SoundCategory.NEUTRAL;
/*      */     }
/*      */ 
/*      */     
/*      */     public SoundEvent func_184639_G() {
/*  369 */       return null;
/*      */     }
/*      */ 
/*      */     
/*      */     public SoundEvent func_184601_bQ(DamageSource ds) {
/*  374 */       return (this instanceof net.minecraft.entity.monster.IMob) ? SoundEvents.field_187741_cz : SoundEvents.field_187543_bD;
/*      */     }
/*      */ 
/*      */     
/*      */     public SoundEvent func_184615_bR() {
/*  379 */       return (this instanceof net.minecraft.entity.monster.IMob) ? SoundEvents.field_187738_cy : SoundEvents.field_187661_by;
/*      */     }
/*      */ 
/*      */     
/*      */     protected float func_70599_aP() {
/*  384 */       return 1.0F;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*      */     public Vec3d func_70040_Z() {
/*  390 */       return func_174806_f(this.field_70125_A, this.field_70759_as);
/*      */     }
/*      */     
/*      */     protected boolean canSeeInvisible(Entity entityIn) {
/*  394 */       if (entityIn.func_82150_aj()) {
/*  395 */         double d0 = entityIn.func_70032_d(entityIn);
/*  396 */         double d1 = ProcedureUtils.getFollowRange((EntityLivingBase)this);
/*  397 */         return 
/*  398 */           (d0 <= d1 * ((entityIn.equals(func_70643_av()) || entityIn.equals(func_110144_aD()) || entityIn.equals(func_70638_az())) ? 0.25D : 0.1D));
/*      */       } 
/*  400 */       return true;
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_70685_l(Entity entityIn) {
/*  405 */       return (super.func_70685_l(entityIn) && canSeeInvisible(entityIn));
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_70014_b(NBTTagCompound compound) {
/*  410 */       super.func_70014_b(compound);
/*  411 */       compound.func_74780_a("maxChakra", this.chakraPathway.getMax());
/*  412 */       compound.func_74780_a("chakra", getChakra());
/*  413 */       NBTTagList nbttaglist = new NBTTagList();
/*  414 */       for (int i = 0; i < this.inventory.size(); i++) {
/*  415 */         ItemStack stack = (ItemStack)this.inventory.get(i);
/*  416 */         if (!stack.func_190926_b()) {
/*  417 */           NBTTagCompound nbttagcompound = new NBTTagCompound();
/*  418 */           nbttagcompound.func_74768_a("slotNo", i);
/*  419 */           stack.func_77955_b(nbttagcompound);
/*  420 */           nbttaglist.func_74742_a((NBTBase)nbttagcompound);
/*      */         } 
/*      */       } 
/*  423 */       compound.func_74782_a("sideInventory", (NBTBase)nbttaglist);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_70037_a(NBTTagCompound compound) {
/*  428 */       super.func_70037_a(compound);
/*  429 */       this.chakraPathway.setMax(compound.func_74769_h("maxChakra"));
/*  430 */       this.chakraPathway.set(compound.func_74769_h("chakra"));
/*  431 */       if (compound.func_150297_b("sideInventory", 9)) {
/*  432 */         NBTTagList nbttaglist = compound.func_150295_c("sideInventory", 10);
/*  433 */         for (int i = 0; i < nbttaglist.func_74745_c(); i++) {
/*  434 */           NBTTagCompound cmp = nbttaglist.func_150305_b(i);
/*  435 */           int j = cmp.func_74762_e("slotNo");
/*  436 */           if (j >= 0 && j < this.inventory.size())
/*  437 */             this.inventory.set(j, new ItemStack(cmp)); 
/*      */         } 
/*      */       } 
/*      */     }
/*      */     
/*      */     public class PathwayNinjaMob
/*      */       extends Chakra.Pathway<Base>
/*      */     {
/*      */       protected PathwayNinjaMob(EntityNinjaMobAddon.Base entityIn, double max) {
/*  446 */         super((EntityLivingBase)entityIn);
/*  447 */         setMax(max);
/*  448 */         set(max);
/*      */       }
/*      */       
/*      */       private float getMaxFromSync() {
/*  452 */         return ((Float)EntityNinjaMobAddon.Base.this.func_184212_Q().func_187225_a(EntityNinjaMobAddon.Base.CHAKRA_MAX)).floatValue();
/*      */       }
/*      */ 
/*      */       
/*      */       public double getMax() {
/*  457 */         float f = getMaxFromSync();
/*  458 */         return (f == 0.0F) ? super.getMax() : f;
/*      */       }
/*      */ 
/*      */       
/*      */       public Chakra.Pathway<EntityNinjaMobAddon.Base> setMax(double d) {
/*  463 */         EntityNinjaMobAddon.Base.this.func_184212_Q().func_187227_b(EntityNinjaMobAddon.Base.CHAKRA_MAX, Float.valueOf((float)d));
/*  464 */         return super.setMax(d);
/*      */       }
/*      */       
/*      */       private void fixOnClientSpawn() {
/*  468 */         if (getMaxFromSync() != super.getMax()) {
/*  469 */           BorutomodaddononeroMod.PACKET_HANDLER.sendToServer(new EntityNinjaMobAddon.ChakraMessage(EntityNinjaMobAddon.Base.this, super.getMax(), super.getAmount()));
/*      */         }
/*      */       }
/*      */ 
/*      */       
/*      */       public double getAmount() {
/*  475 */         return (getMaxFromSync() == 0.0F) ? super.getAmount() : ((Float)EntityNinjaMobAddon.Base.this
/*  476 */           .func_184212_Q().func_187225_a(EntityNinjaMobAddon.Base.CHAKRA)).floatValue();
/*      */       }
/*      */ 
/*      */       
/*      */       protected void set(double amountIn) {
/*  481 */         EntityNinjaMobAddon.Base.this.func_184212_Q().func_187227_b(EntityNinjaMobAddon.Base.CHAKRA, Float.valueOf((float)amountIn));
/*  482 */         super.set(amountIn);
/*      */       }
/*      */ 
/*      */ 
/*      */       
/*      */       protected void onUpdate() {
/*  488 */         if ((((EntityNinjaMobAddon.Base)this.user).func_70638_az() == null || !((EntityNinjaMobAddon.Base)this.user).func_70638_az().func_70089_S()) && (((EntityNinjaMobAddon.Base)this.user)
/*  489 */           .func_94060_bK() == null || !((EntityNinjaMobAddon.Base)this.user).func_94060_bK().func_70089_S())) {
/*  490 */           ((EntityNinjaMobAddon.Base)this.user).peacefulTicks++;
/*  491 */           if (((EntityNinjaMobAddon.Base)this.user).peacefulTicks % 20 == 19) {
/*  492 */             consume(-0.04F);
/*  493 */             if (((EntityNinjaMobAddon.Base)this.user).func_110143_aJ() < ((EntityNinjaMobAddon.Base)this.user).func_110138_aP()) {
/*  494 */               ((EntityNinjaMobAddon.Base)this.user).func_70606_j(((EntityNinjaMobAddon.Base)this.user).func_110143_aJ() + 1.0F);
/*      */             }
/*      */           } 
/*      */         } else {
/*  498 */           ((EntityNinjaMobAddon.Base)this.user).peacefulTicks = 0;
/*      */         } 
/*      */       }
/*      */     }
/*      */   }
/*      */   
/*      */   public static class AILeapAtTarget extends EntityAIBase {
/*      */     protected EntityLiving leaper;
/*      */     protected EntityLivingBase target;
/*      */     protected float leapStrength;
/*      */     
/*      */     public AILeapAtTarget(EntityLiving leapingEntity, float leapStrengthIn) {
/*  510 */       this.leaper = leapingEntity;
/*  511 */       this.leapStrength = leapStrengthIn;
/*  512 */       func_75248_a(5);
/*      */     }
/*      */     
/*      */     public boolean func_75250_a() {
/*  516 */       this.target = this.leaper.func_70638_az();
/*  517 */       if (this.target == null) {
/*  518 */         return false;
/*      */       }
/*  520 */       double d0 = this.leaper.func_70032_d((Entity)this.target);
/*  521 */       if (d0 >= 3.0D && d0 <= this.leapStrength * 12.0D && this.leaper.field_70122_E) {
/*  522 */         return (this.leaper.func_70681_au().nextInt(5) == 0);
/*      */       }
/*  524 */       return false;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*      */     public boolean func_75253_b() {
/*  530 */       return !this.leaper.field_70122_E;
/*      */     }
/*      */     
/*      */     public void func_75249_e() {
/*  534 */       double d0 = this.target.field_70165_t - this.leaper.field_70165_t;
/*  535 */       double d1 = this.target.field_70161_v - this.leaper.field_70161_v;
/*  536 */       double d4 = MathHelper.func_76133_a(d0 * d0 + d1 * d1);
/*  537 */       double d2 = this.target.field_70163_u + this.target.field_70131_O / 3.0D - this.leaper.field_70163_u + d4 * 0.2D;
/*  538 */       double d3 = MathHelper.func_76133_a(d0 * d0 + d1 * d1 + d2 * d2);
/*  539 */       if (d3 >= 1.0E-4D) {
/*  540 */         this.leaper.field_70159_w = d0 / d3 * this.leapStrength;
/*  541 */         this.leaper.field_70179_y = d1 / d3 * this.leapStrength;
/*  542 */         this.leaper.field_70181_x = d2 / d3 * this.leapStrength;
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class AIAttackRangedTactical<T extends EntityCreature & IRangedAttackMob> extends EntityAIBase {
/*      */     protected final T entity;
/*      */     private final double moveSpeedAmp;
/*      */     private int attackCooldown;
/*      */     private final float attackRadius;
/*      */     private final float maxAttackDistance;
/*  553 */     private int attackTime = -1;
/*      */     private int seeTime;
/*      */     private boolean strafingClockwise;
/*      */     private boolean strafingBackwards;
/*  557 */     private int strafingTime = -1;
/*      */     
/*      */     public AIAttackRangedTactical(T entityIn, double moveSpeed, int cooldown, float maxDistance) {
/*  560 */       this.entity = entityIn;
/*  561 */       this.moveSpeedAmp = moveSpeed;
/*  562 */       this.attackCooldown = cooldown;
/*  563 */       this.attackRadius = maxDistance;
/*  564 */       this.maxAttackDistance = maxDistance * maxDistance;
/*  565 */       func_75248_a(3);
/*      */     }
/*      */     
/*      */     public boolean func_75250_a() {
/*  569 */       return (this.entity.func_70638_az() != null);
/*      */     }
/*      */     
/*      */     public boolean func_75253_b() {
/*  573 */       return (func_75250_a() || !this.entity.func_70661_as().func_75500_f());
/*      */     }
/*      */     
/*      */     public void func_75249_e() {
/*  577 */       super.func_75249_e();
/*  578 */       ((IRangedAttackMob)this.entity).func_184724_a(true);
/*      */     }
/*      */     
/*      */     public void func_75251_c() {
/*  582 */       super.func_75251_c();
/*  583 */       ((IRangedAttackMob)this.entity).func_184724_a(false);
/*  584 */       this.seeTime = 0;
/*  585 */       this.attackTime = -1;
/*  586 */       this.entity.func_184602_cy();
/*      */     }
/*      */     
/*      */     public void func_75246_d() {
/*  590 */       EntityLivingBase entitylivingbase = this.entity.func_70638_az();
/*  591 */       if (entitylivingbase != null) {
/*  592 */         double d0 = this.entity.func_70092_e(entitylivingbase.field_70165_t, (entitylivingbase.func_174813_aQ()).field_72338_b, entitylivingbase.field_70161_v);
/*  593 */         boolean flag = this.entity.func_70635_at().func_75522_a((Entity)entitylivingbase);
/*  594 */         boolean flag1 = (this.seeTime > 0);
/*  595 */         if (flag != flag1) {
/*  596 */           this.seeTime = 0;
/*      */         }
/*  598 */         if (flag) {
/*  599 */           this.seeTime++;
/*      */         } else {
/*  601 */           this.seeTime--;
/*      */         } 
/*  603 */         if (d0 <= this.maxAttackDistance && this.seeTime >= 20) {
/*  604 */           this.entity.func_70661_as().func_75499_g();
/*  605 */           this.strafingTime++;
/*      */         } else {
/*  607 */           this.entity.func_70661_as().func_75497_a((Entity)entitylivingbase, this.moveSpeedAmp);
/*  608 */           this.strafingTime = -1;
/*      */         } 
/*  610 */         if (this.strafingTime >= 20) {
/*  611 */           if (this.entity.func_70681_au().nextFloat() < 0.3F) {
/*  612 */             this.strafingClockwise = !this.strafingClockwise;
/*      */           }
/*  614 */           if (this.entity.func_70681_au().nextFloat() < 0.3F) {
/*  615 */             this.strafingBackwards = !this.strafingBackwards;
/*      */           }
/*  617 */           this.strafingTime = 0;
/*      */         } 
/*  619 */         if (this.strafingTime > -1) {
/*  620 */           if (d0 > (this.maxAttackDistance * 0.75F)) {
/*  621 */             this.strafingBackwards = false;
/*  622 */           } else if (d0 < (this.maxAttackDistance * 0.25F)) {
/*  623 */             this.strafingBackwards = true;
/*      */           } 
/*  625 */           float f = (float)this.moveSpeedAmp;
/*  626 */           this.entity.func_70605_aq().func_188488_a(this.strafingBackwards ? -f : f, this.strafingClockwise ? 0.5F : -0.5F);
/*  627 */           this.entity.func_70625_a((Entity)entitylivingbase, 30.0F, 30.0F);
/*      */         } else {
/*  629 */           this.entity.func_70671_ap().func_75651_a((Entity)entitylivingbase, 30.0F, 30.0F);
/*      */         } 
/*  631 */         if (--this.attackTime == 0) {
/*  632 */           if (!flag) {
/*      */             return;
/*      */           }
/*  635 */           float f = MathHelper.func_76133_a(d0) / this.attackRadius;
/*  636 */           float lvt_5_1_ = MathHelper.func_76131_a(f, 0.1F, 1.0F);
/*  637 */           ((IRangedAttackMob)this.entity).func_82196_d(entitylivingbase, lvt_5_1_);
/*  638 */           this.attackTime = MathHelper.func_76141_d(f * this.attackCooldown);
/*  639 */         } else if (this.attackTime < 0) {
/*  640 */           float f = MathHelper.func_76133_a(d0) / this.attackRadius;
/*  641 */           this.attackTime = MathHelper.func_76141_d(f * this.attackCooldown);
/*      */         } 
/*      */       } else {
/*  644 */         this.entity.func_70661_as().func_75499_g();
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class AIAttackRangedJutsu<T extends EntityLiving & IRangedAttackMob> extends EntityAIBase {
/*      */     protected final T entity;
/*      */     private int attackCooldown;
/*      */     private final float attackRadius;
/*      */     private int attackTime;
/*      */     private Vec3d targetPos;
/*      */     private boolean strafingBackwards;
/*  656 */     private int strafingTime = -1;
/*      */     
/*      */     public AIAttackRangedJutsu(T entityIn, int cooldown, float maxDistance) {
/*  659 */       this.entity = entityIn;
/*  660 */       this.attackCooldown = cooldown;
/*  661 */       this.attackTime = cooldown;
/*  662 */       this.attackRadius = maxDistance;
/*  663 */       func_75248_a(3);
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_75250_a() {
/*  668 */       this.attackTime--;
/*  669 */       EntityLivingBase target = this.entity.func_70638_az();
/*      */       
/*  671 */       if (target != null && this.attackTime <= 0) {
/*  672 */         this.attackTime = 0;
/*      */         
/*  674 */         Vec3d vec = this.entity.func_174791_d().func_178788_d(target.func_174791_d()).func_72432_b().func_186678_a(this.attackRadius).func_178787_e(this.entity.func_174791_d());
/*  675 */         for (double d = vec.field_72448_b - 3.0D; d < vec.field_72448_b + 7.0D; d++) {
/*  676 */           BlockPos pos = new BlockPos(vec.field_72450_a, d, vec.field_72449_c);
/*  677 */           if (this.entity.func_70661_as().func_188555_b(pos)) {
/*  678 */             this.targetPos = new Vec3d((Vec3i)pos);
/*  679 */             return true;
/*      */           } 
/*      */         } 
/*      */       } 
/*  683 */       return false;
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_75253_b() {
/*  688 */       return (this.targetPos != null && --this.attackTime >= -100);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75249_e() {
/*  693 */       double d0 = this.targetPos.field_72450_a - ((EntityLiving)this.entity).field_70165_t;
/*  694 */       double d1 = this.targetPos.field_72449_c - ((EntityLiving)this.entity).field_70161_v;
/*  695 */       double d4 = MathHelper.func_76133_a(d0 * d0 + d1 * d1);
/*  696 */       double d2 = this.targetPos.field_72448_b - ((EntityLiving)this.entity).field_70163_u + d4 * 0.2D;
/*  697 */       double d3 = MathHelper.func_76133_a(d0 * d0 + d1 * d1 + d2 * d2);
/*  698 */       if (d3 >= 1.0E-4D) {
/*  699 */         ((EntityLiving)this.entity).field_70159_w = d0 / d3;
/*  700 */         ((EntityLiving)this.entity).field_70179_y = d1 / d3;
/*  701 */         ((EntityLiving)this.entity).field_70181_x = d2 / d3;
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75246_d() {
/*  707 */       EntityLivingBase entitylivingbase = this.entity.func_70638_az();
/*  708 */       if (entitylivingbase != null) {
/*  709 */         double d0 = this.entity.func_70032_d((Entity)entitylivingbase);
/*  710 */         if (d0 < this.attackRadius - 2.0D) {
/*  711 */           this.entity.func_70661_as().func_75499_g();
/*  712 */           this.strafingTime++;
/*  713 */         } else if (d0 > this.attackRadius + 2.0D) {
/*  714 */           this.entity.func_70661_as().func_75497_a((Entity)entitylivingbase, 1.0D);
/*  715 */           this.strafingTime = -1;
/*      */         } else {
/*  717 */           float f = (float)d0 / this.attackRadius;
/*  718 */           ((IRangedAttackMob)this.entity).func_82196_d(entitylivingbase, MathHelper.func_76131_a(f, 0.1F, 1.0F));
/*  719 */           this.attackTime = MathHelper.func_76141_d(f * this.attackCooldown);
/*  720 */           this.targetPos = null;
/*      */           return;
/*      */         } 
/*  723 */         if (this.strafingTime >= 20) {
/*  724 */           if (this.entity.func_70681_au().nextFloat() < 0.3D) {
/*  725 */             this.strafingBackwards = !this.strafingBackwards;
/*      */           }
/*  727 */           this.strafingTime = 0;
/*      */         } 
/*  729 */         if (this.strafingTime > -1) {
/*  730 */           if (d0 > this.attackRadius + 2.0D) {
/*  731 */             this.strafingBackwards = false;
/*  732 */           } else if (d0 < this.attackRadius - 2.0D) {
/*  733 */             this.strafingBackwards = true;
/*      */           } 
/*  735 */           this.entity.func_70605_aq().func_188488_a(this.strafingBackwards ? -1.25F : 1.25F, 0.0F);
/*  736 */           this.entity.func_70625_a((Entity)entitylivingbase, 30.0F, 30.0F);
/*      */         } else {
/*  738 */           this.entity.func_70671_ap().func_75651_a((Entity)entitylivingbase, 30.0F, 30.0F);
/*      */         } 
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class AIDefendEntity extends EntityAITarget {
/*      */     private final EntityLivingBase defendedEntity;
/*      */     
/*      */     public AIDefendEntity(EntityNinjaMobAddon.Base ninjamob, EntityLivingBase protectedEntity) {
/*  748 */       super(ninjamob, false);
/*  749 */       this.defendedEntity = protectedEntity;
/*  750 */       func_75248_a(1);
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_75250_a() {
/*  755 */       this.field_188509_g = this.defendedEntity.func_70643_av();
/*  756 */       if (this.field_188509_g == null) {
/*  757 */         EntityLivingBase living = this.defendedEntity.func_110144_aD();
/*      */         
/*  759 */         if (living != null) {
/*  760 */           this.field_188509_g = living;
/*      */         }
/*      */       } 
/*  763 */       if (this.field_188509_g == null && this.defendedEntity instanceof EntityLiving) {
/*  764 */         this.field_188509_g = ((EntityLiving)this.defendedEntity).func_70638_az();
/*      */       }
/*  766 */       return (this.field_188509_g != null && func_75296_a(this.field_188509_g, false));
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75249_e() {
/*  771 */       this.field_75299_d.func_70624_b(this.field_188509_g);
/*  772 */       super.func_75249_e();
/*      */     }
/*      */   }
/*      */   
/*      */   public static class NavigateGround extends PathNavigateGround {
/*      */     private BlockPos targetPosition;
/*      */     
/*      */     public NavigateGround(EntityLiving entityLivingIn, World worldIn) {
/*  780 */       super(entityLivingIn, worldIn);
/*      */     }
/*      */ 
/*      */     
/*      */     public Path func_179680_a(BlockPos pos) {
/*  785 */       this.targetPosition = pos;
/*  786 */       return super.func_179680_a(pos);
/*      */     }
/*      */ 
/*      */     
/*      */     public Path func_75494_a(Entity entityIn) {
/*  791 */       this.targetPosition = new BlockPos(entityIn);
/*  792 */       return super.func_75494_a(entityIn);
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_75497_a(Entity entityIn, double speedIn) {
/*  797 */       Path path = func_75494_a(entityIn);
/*  798 */       if (path != null) {
/*  799 */         return func_75484_a(path, speedIn);
/*      */       }
/*  801 */       this.targetPosition = new BlockPos(entityIn);
/*  802 */       this.field_75511_d = speedIn;
/*  803 */       return true;
/*      */     }
/*      */ 
/*      */ 
/*      */     
/*      */     public void func_75499_g() {
/*  809 */       super.func_75499_g();
/*  810 */       this.targetPosition = null;
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75501_e() {
/*  815 */       if (!func_75500_f()) {
/*  816 */         super.func_75501_e();
/*      */       }
/*  818 */       else if (this.targetPosition != null) {
/*  819 */         double d0 = (this.field_75515_a.field_70130_N * this.field_75515_a.field_70130_N);
/*  820 */         double d1 = this.targetPosition.func_177956_o() - this.field_75515_a.field_70163_u;
/*  821 */         double d2 = this.field_75515_a.func_174831_c(new BlockPos(this.targetPosition.func_177958_n(), 
/*  822 */               MathHelper.func_76128_c(this.field_75515_a.field_70163_u), this.targetPosition.func_177952_p()));
/*  823 */         if (d2 >= d0 && d1 <= this.field_75515_a.field_70138_W && d1 >= -12.0D * this.field_75515_a.field_70131_O) {
/*  824 */           this.field_75515_a.func_70605_aq().func_75642_a(this.targetPosition.func_177958_n() + 0.5D, this.targetPosition
/*  825 */               .func_177956_o(), this.targetPosition.func_177952_p() + 0.5D, this.field_75511_d);
/*      */         } else {
/*  827 */           this.targetPosition = null;
/*      */         } 
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class MoveHelper
/*      */     extends EntityMoveHelper {
/*      */     public MoveHelper(EntityLiving entityIn) {
/*  836 */       super(entityIn);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75641_c() {
/*  841 */       if (func_75640_a()) {
/*  842 */         this.field_188491_h = EntityMoveHelper.Action.WAIT;
/*  843 */         double d0 = this.field_75646_b - this.field_75648_a.field_70165_t;
/*  844 */         double d1 = this.field_75644_d - this.field_75648_a.field_70161_v;
/*  845 */         double d2 = this.field_75647_c - this.field_75648_a.field_70163_u;
/*  846 */         double d3 = d0 * d0 + d2 * d2 + d1 * d1;
/*  847 */         if (d3 < 2.5E-7D) {
/*  848 */           this.field_75648_a.func_191989_p(0.0F);
/*      */           return;
/*      */         } 
/*  851 */         float f9 = (float)(MathHelper.func_181159_b(d1, d0) * 57.29577951308232D) - 90.0F;
/*  852 */         this.field_75648_a.field_70177_z = func_75639_a(this.field_75648_a.field_70177_z, f9, 90.0F);
/*  853 */         this.field_75648_a.func_70659_e((float)(this.field_75645_e * this.field_75648_a.func_110148_a(SharedMonsterAttributes.field_111263_d).func_111126_e()));
/*  854 */         if (d2 > 0.01D && this.field_75648_a.field_70123_F) {
/*  855 */           this.field_75648_a.field_70181_x = 0.42D;
/*  856 */         } else if (d2 > this.field_75648_a.field_70138_W && d0 * d0 + d1 * d1 < Math.max(1.0F, this.field_75648_a.field_70130_N)) {
/*  857 */           this.field_75648_a.func_70683_ar().func_75660_a();
/*  858 */           this.field_188491_h = EntityMoveHelper.Action.JUMPING;
/*      */         } 
/*      */       } else {
/*  861 */         super.func_75641_c();
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class SwimHelper extends EntityMoveHelper {
/*      */     public SwimHelper(EntityLiving entityIn) {
/*  868 */       super(entityIn);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_75641_c() {
/*  873 */       if (this.field_188491_h == EntityMoveHelper.Action.MOVE_TO) {
/*  874 */         this.field_188491_h = EntityMoveHelper.Action.WAIT;
/*  875 */         double d0 = this.field_75646_b - this.field_75648_a.field_70165_t;
/*  876 */         double d1 = this.field_75647_c - this.field_75648_a.field_70163_u;
/*  877 */         double d2 = this.field_75644_d - this.field_75648_a.field_70161_v;
/*  878 */         double d3 = MathHelper.func_76133_a(d0 * d0 + d1 * d1 + d2 * d2);
/*  879 */         if (d3 < 1.6E-7D) {
/*  880 */           ProcedureUtils.multiplyVelocity((Entity)this.field_75648_a, 0.0D);
/*      */         } else {
/*  882 */           float f = (float)(this.field_75645_e * ProcedureUtils.getModifiedSpeed((EntityLivingBase)this.field_75648_a));
/*  883 */           this.field_75648_a.field_70159_w = d0 / d3 * f;
/*  884 */           this.field_75648_a.field_70181_x = d1 / d3 * f;
/*  885 */           this.field_75648_a.field_70179_y = d2 / d3 * f;
/*  886 */           float f1 = -((float)MathHelper.func_181159_b(this.field_75648_a.field_70159_w, this.field_75648_a.field_70179_y)) * 57.295776F;
/*  887 */           this.field_75648_a.field_70177_z = func_75639_a(this.field_75648_a.field_70177_z, f1, 10.0F);
/*  888 */           this.field_75648_a.field_70761_aq = this.field_75648_a.field_70177_z;
/*      */         } 
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   @SideOnly(Side.CLIENT)
/*      */   public static abstract class RenderBase<T extends Base> extends RenderBiped<T> {
/*      */     public RenderBase(RenderManager renderManager, ModelBiped model) {
/*  897 */       super(renderManager, model, 0.5F);
/*  898 */       EntityClone.ClientRLM.getInstance().getClass(); func_177094_a((LayerRenderer)new EntityClone.ClientRLM.BipedArmorLayer(EntityClone.ClientRLM.getInstance(), (RenderLivingBase)this));
/*  899 */       func_177094_a(new EntityNinjaMobAddon.LayerInventoryItem(this));
/*      */     }
/*      */ 
/*      */     
/*      */     public void doRender(T entity, double x, double y, double z, float entityYaw, float partialTicks) {
/*  904 */       setPose(entity);
/*  905 */       super.func_76986_a((EntityLiving)entity, x, y, z, entityYaw, partialTicks);
/*      */     }
/*      */     
/*      */     private void setPose(T entity) {
/*  909 */       ModelBiped model = (ModelBiped)func_177087_b();
/*  910 */       model.field_78117_n = entity.func_70093_af();
/*  911 */       ItemStack itemstack = entity.func_184614_ca();
/*  912 */       ItemStack itemstack1 = entity.func_184592_cb();
/*  913 */       ModelBiped.ArmPose mainhandpose = ModelBiped.ArmPose.EMPTY;
/*  914 */       ModelBiped.ArmPose offhandpose = ModelBiped.ArmPose.EMPTY;
/*  915 */       if (!itemstack.func_190926_b()) {
/*  916 */         mainhandpose = ModelBiped.ArmPose.ITEM;
/*  917 */         if (entity.func_184605_cv() > 0) {
/*  918 */           EnumAction enumaction = itemstack.func_77975_n();
/*  919 */           if (enumaction == EnumAction.BLOCK) {
/*  920 */             mainhandpose = ModelBiped.ArmPose.BLOCK;
/*  921 */           } else if (enumaction == EnumAction.BOW) {
/*  922 */             mainhandpose = ModelBiped.ArmPose.BOW_AND_ARROW;
/*      */           } 
/*      */         } 
/*      */       } 
/*  926 */       if (!itemstack1.func_190926_b()) {
/*  927 */         offhandpose = ModelBiped.ArmPose.ITEM;
/*  928 */         if (entity.func_184605_cv() > 0) {
/*  929 */           EnumAction enumaction1 = itemstack1.func_77975_n();
/*  930 */           if (enumaction1 == EnumAction.BLOCK) {
/*  931 */             offhandpose = ModelBiped.ArmPose.BLOCK;
/*  932 */           } else if (enumaction1 == EnumAction.BOW) {
/*  933 */             offhandpose = ModelBiped.ArmPose.BOW_AND_ARROW;
/*      */           } 
/*      */         } 
/*      */       } 
/*  937 */       if (entity.func_184591_cq() == EnumHandSide.RIGHT) {
/*  938 */         model.field_187076_m = mainhandpose;
/*  939 */         model.field_187075_l = offhandpose;
/*      */       } else {
/*  941 */         model.field_187076_m = offhandpose;
/*  942 */         model.field_187075_l = mainhandpose;
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   @SideOnly(Side.CLIENT)
/*      */   public static class LayerInventoryItem implements LayerRenderer<Base> {
/*      */     private final RenderBiped renderer;
/*      */     
/*      */     public LayerInventoryItem(RenderBiped rendererIn) {
/*  952 */       this.renderer = rendererIn;
/*      */     }
/*      */ 
/*      */     
/*      */     public void doRenderLayer(EntityNinjaMobAddon.Base entityIn, float f1, float f2, float f3, float f4, float f5, float f6, float f7) {
/*  957 */       for (int i = 0; i < entityIn.inventory.size(); i++) {
/*  958 */         ItemStack stack = (ItemStack)entityIn.inventory.get(i);
/*  959 */         if (stack.func_77973_b() instanceof ItemOnBody.Interface) {
/*  960 */           ItemOnBody.Interface item = (ItemOnBody.Interface)stack.func_77973_b();
/*  961 */           if (item.showOnBody() != ItemOnBody.BodyPart.NONE) {
/*  962 */             Vec3d offset = item.getOffset();
/*  963 */             GlStateManager.func_179094_E();
/*  964 */             ModelBiped model = (ModelBiped)this.renderer.func_177087_b();
/*  965 */             switch (item.showOnBody()) {
/*      */               case HEAD:
/*  967 */                 model.field_78116_c.func_78794_c(0.0625F);
/*      */                 break;
/*      */               case TORSO:
/*  970 */                 model.field_78115_e.func_78794_c(0.0625F);
/*      */                 break;
/*      */               case RIGHT_ARM:
/*  973 */                 model.field_178723_h.func_78794_c(0.0625F);
/*      */                 break;
/*      */               case LEFT_ARM:
/*  976 */                 model.field_178724_i.func_78794_c(0.0625F);
/*      */                 break;
/*      */               case RIGHT_LEG:
/*  979 */                 model.field_178721_j.func_78794_c(0.0625F);
/*      */                 break;
/*      */               case LEFT_LEG:
/*  982 */                 model.field_178722_k.func_78794_c(0.0625F);
/*      */                 break;
/*      */             } 
/*  985 */             GlStateManager.func_179131_c(1.0F, 1.0F, 1.0F, 1.0F);
/*  986 */             GlStateManager.func_179137_b(offset.field_72450_a, -0.25D + offset.field_72448_b, offset.field_72449_c);
/*  987 */             GlStateManager.func_179114_b(180.0F, 0.0F, 1.0F, 0.0F);
/*  988 */             GlStateManager.func_179152_a(0.625F, -0.625F, -0.625F);
/*  989 */             Minecraft.func_71410_x().func_175597_ag().func_178099_a((EntityLivingBase)entityIn, stack, ItemCameraTransforms.TransformType.HEAD);
/*  990 */             GlStateManager.func_179121_F();
/*      */           } 
/*      */         } 
/*      */       } 
/*      */     }
/*      */ 
/*      */     
/*      */     public boolean func_177142_b() {
/*  998 */       return false;
/*      */     }
/*      */   }
/*      */   
/*      */   @SideOnly(Side.CLIENT)
/*      */   public static class ModelOtsutsuki extends ModelBiped {
/*      */     public ModelOtsutsuki() {
/* 1005 */       this.field_78090_t = 128;
/* 1006 */       this.field_78089_u = 128;
/* 1007 */       this.field_187075_l = ModelBiped.ArmPose.EMPTY;
/* 1008 */       this.field_187076_m = ModelBiped.ArmPose.EMPTY;
/*      */     }
/*      */     
/*      */     public ModelOtsutsuki(float modelSize) {
/* 1012 */       super(modelSize);
/*      */     }
/*      */     
/*      */     public ModelOtsutsuki(float modelSize, float p_i1149_2_, int textureWidthIn, int textureHeightIn) {
/* 1016 */       super(modelSize, p_i1149_2_, textureWidthIn, textureHeightIn);
/*      */     }
/*      */ 
/*      */     
/*      */     public void func_78087_a(float f0, float f1, float f2, float f3, float f4, float f5, Entity entityIn) {
/* 1021 */       boolean flag2 = (PlayerRender.shouldNarutoRun(entityIn) && this.field_78095_p == 0.0F);
/* 1022 */       if (flag2) {
/* 1023 */         this.field_78117_n = true;
/*      */       }
/* 1025 */       super.func_78087_a(f0, f1, f2, f3, f4, f5, entityIn);
/* 1026 */       if (flag2) {
/* 1027 */         this.field_178723_h.field_78795_f = 1.4835F;
/* 1028 */         this.field_178723_h.field_78796_g = -0.3927F;
/* 1029 */         this.field_178724_i.field_78795_f = 1.4835F;
/* 1030 */         this.field_178724_i.field_78796_g = 0.3927F;
/*      */       } 
/*      */     }
/*      */   }
/*      */   
/*      */   public static class ChakraMessage
/*      */     implements IMessage {
/*      */     int id;
/*      */     double d0;
/*      */     double d1;
/*      */     
/*      */     public ChakraMessage() {}
/*      */     
/*      */     public ChakraMessage(EntityNinjaMobAddon.Base entity, double max, double chakra) {
/* 1044 */       this.id = entity.func_145782_y();
/* 1045 */       this.d0 = max;
/* 1046 */       this.d1 = chakra;
/*      */     }
/*      */     
/*      */     public static class Handler
/*      */       implements IMessageHandler<ChakraMessage, IMessage> {
/*      */       public IMessage onMessage(EntityNinjaMobAddon.ChakraMessage message, MessageContext context) {
/* 1052 */         EntityPlayerMP entity = (context.getServerHandler()).field_147369_b;
/* 1053 */         entity.func_71121_q().func_152344_a(() -> {
/*      */               World world = entity.field_70170_p;
/*      */               if (!world.func_175667_e(new BlockPos(entity.field_70165_t, entity.field_70163_u, entity.field_70161_v)))
/*      */                 return; 
/*      */               Entity entity1 = world.func_73045_a(message.id);
/*      */               if (entity1 instanceof EntityNinjaMobAddon.Base) {
/*      */                 ((EntityNinjaMobAddon.Base)entity1).chakraPathway.setMax(message.d0);
/*      */                 ((EntityNinjaMobAddon.Base)entity1).chakraPathway.set(message.d1);
/*      */               } 
/*      */             });
/* 1063 */         return null;
/*      */       }
/*      */     }
/*      */     
/*      */     public void toBytes(ByteBuf buf) {
/* 1068 */       buf.writeInt(this.id);
/* 1069 */       buf.writeDouble(this.d0);
/* 1070 */       buf.writeDouble(this.d1);
/*      */     }
/*      */     
/*      */     public void fromBytes(ByteBuf buf) {
/* 1074 */       this.id = buf.readInt();
/* 1075 */       this.d0 = buf.readDouble();
/* 1076 */       this.d1 = buf.readDouble();
/*      */     } } public static class Handler implements IMessageHandler<ChakraMessage, IMessage> { public IMessage onMessage(EntityNinjaMobAddon.ChakraMessage message, MessageContext context) { EntityPlayerMP entity = (context.getServerHandler()).field_147369_b; entity.func_71121_q().func_152344_a(() -> {
/*      */             World world = entity.field_70170_p;
/*      */             if (!world.func_175667_e(new BlockPos(entity.field_70165_t, entity.field_70163_u, entity.field_70161_v)))
/*      */               return; 
/*      */             Entity entity1 = world.func_73045_a(message.id);
/*      */             if (entity1 instanceof EntityNinjaMobAddon.Base) {
/*      */               ((EntityNinjaMobAddon.Base)entity1).chakraPathway.setMax(message.d0);
/*      */               ((EntityNinjaMobAddon.Base)entity1).chakraPathway.set(message.d1);
/*      */             } 
/*      */           });
/*      */       return null; } } public static class InventoryMessage implements IMessage { int id; List<ItemStack> list; public InventoryMessage() {}
/* 1088 */     public InventoryMessage(EntityNinjaMobAddon.Base entity) { this.id = entity.func_145782_y();
/* 1089 */       this.list = (List<ItemStack>)entity.inventory; }
/*      */ 
/*      */     
/*      */     public static void sendToTracking(EntityNinjaMobAddon.Base entity) {
/* 1093 */       BorutomodaddononeroMod.PACKET_HANDLER.sendToAllTracking(new InventoryMessage(entity), (Entity)entity);
/*      */     }
/*      */     
/*      */     public static void sendTo(EntityPlayerMP player, EntityNinjaMobAddon.Base entity) {
/* 1097 */       BorutomodaddononeroMod.PACKET_HANDLER.sendTo(new InventoryMessage(entity), player);
/*      */     }
/*      */     
/*      */     public static class Handler
/*      */       implements IMessageHandler<InventoryMessage, IMessage> {
/*      */       @SideOnly(Side.CLIENT)
/*      */       public IMessage onMessage(EntityNinjaMobAddon.InventoryMessage message, MessageContext context) {
/* 1104 */         Minecraft mc = Minecraft.func_71410_x();
/* 1105 */         mc.func_152344_a(() -> {
/*      */               Entity entity = mc.field_71441_e.func_73045_a(message.id); if (entity instanceof EntityNinjaMobAddon.Base) {
/*      */                 int i = 0; while (i < message.list.size() && i < ((EntityNinjaMobAddon.Base)entity).getInventorySize()) {
/*      */                   ((EntityNinjaMobAddon.Base)entity).inventory.set(i, message.list.get(i));
/*      */                   i++;
/*      */                 } 
/*      */               } 
/*      */             });
/* 1113 */         return null;
/*      */       }
/*      */     }
/*      */     
/*      */     public void toBytes(ByteBuf buf) {
/* 1118 */       PacketBuffer pbuf = new PacketBuffer(buf);
/* 1119 */       pbuf.writeInt(this.id);
/* 1120 */       int j = this.list.size();
/* 1121 */       pbuf.writeInt(j);
/* 1122 */       for (int i = 0; i < j; i++) {
/* 1123 */         pbuf.func_150788_a(this.list.get(i));
/*      */       }
/*      */     }
/*      */     
/*      */     public void fromBytes(ByteBuf buf) {
/* 1128 */       PacketBuffer pbuf = new PacketBuffer(buf);
/* 1129 */       this.id = pbuf.readInt();
/* 1130 */       int j = pbuf.readInt();
/* 1131 */       this.list = Lists.newArrayList();
/*      */       try {
/* 1133 */         for (int i = 0; i < j; i++) {
/* 1134 */           this.list.add(pbuf.func_150791_c());
/*      */         }
/* 1136 */       } catch (Exception e) {
/* 1137 */         new IOException("NinjaMob@inventory packet: ", e);
/*      */       }  } } public static class Handler implements IMessageHandler<InventoryMessage, IMessage> {
/*      */     @SideOnly(Side.CLIENT) public IMessage onMessage(EntityNinjaMobAddon.InventoryMessage message, MessageContext context) { Minecraft mc = Minecraft.func_71410_x(); mc.func_152344_a(() -> {
/*      */             Entity entity = mc.field_71441_e.func_73045_a(message.id); if (entity instanceof EntityNinjaMobAddon.Base) {
/*      */               int i = 0;
/*      */               while (i < message.list.size() && i < ((EntityNinjaMobAddon.Base)entity).getInventorySize()) {
/*      */                 ((EntityNinjaMobAddon.Base)entity).inventory.set(i, message.list.get(i));
/*      */                 i++;
/*      */               } 
/*      */             } 
/*      */           });
/*      */       return null; }
/* 1149 */   } public static class StandStillMessage implements IMessage { int id; public StandStillMessage(EntityNinjaMobAddon.Base entity) { this.id = entity.func_145782_y();
/* 1150 */       this.ticks = entity.standStillTicks; }
/*      */      int ticks;
/*      */     public StandStillMessage() {}
/*      */     public static void sendToTracking(EntityNinjaMobAddon.Base entity) {
/* 1154 */       BorutomodaddononeroMod.PACKET_HANDLER.sendToAllTracking(new StandStillMessage(entity), (Entity)entity);
/*      */     }
/*      */     
/*      */     public static class Handler
/*      */       implements IMessageHandler<StandStillMessage, IMessage> {
/*      */       @SideOnly(Side.CLIENT)
/*      */       public IMessage onMessage(EntityNinjaMobAddon.StandStillMessage message, MessageContext context) {
/* 1161 */         Minecraft mc = Minecraft.func_71410_x();
/* 1162 */         mc.func_152344_a(() -> {
/*      */               Entity entity = mc.field_71441_e.func_73045_a(message.id);
/*      */               if (entity instanceof EntityNinjaMobAddon.Base) {
/*      */                 ((EntityNinjaMobAddon.Base)entity).standStillFor(message.ticks);
/*      */               }
/*      */             });
/* 1168 */         return null;
/*      */       }
/*      */     }
/*      */     
/*      */     public void toBytes(ByteBuf buf) {
/* 1173 */       buf.writeInt(this.id);
/* 1174 */       buf.writeInt(this.ticks);
/*      */     }
/*      */     
/*      */     public void fromBytes(ByteBuf buf) {
/* 1178 */       this.id = buf.readInt();
/* 1179 */       this.ticks = buf.readInt();
/*      */     } } public static class Handler implements IMessageHandler<StandStillMessage, IMessage> { @SideOnly(Side.CLIENT) public IMessage onMessage(EntityNinjaMobAddon.StandStillMessage message, MessageContext context) { Minecraft mc = Minecraft.func_71410_x(); mc.func_152344_a(() -> {
/*      */             Entity entity = mc.field_71441_e.func_73045_a(message.id); if (entity instanceof EntityNinjaMobAddon.Base)
/*      */               ((EntityNinjaMobAddon.Base)entity).standStillFor(message.ticks); 
/*      */           });
/*      */       return null; } }
/* 1185 */   @SubscribeEvent public void onSpawnCheck(LivingSpawnEvent.CheckSpawn event) { EntityLivingBase entity = event.getEntityLiving();
/* 1186 */     if (!event.isSpawner() && entity instanceof Base) {
/* 1187 */       entity.func_70107_b(event.getX(), event.getY(), event.getZ());
/* 1188 */       if (!((Base)entity).func_70601_bi()) {
/* 1189 */         event.setResult(Event.Result.DENY);
/*      */       }
/*      */     }  }
/*      */ 
/*      */   
/*      */   @SubscribeEvent
/*      */   public void onTracking(PlayerEvent.StartTracking event) {
/* 1196 */     Entity target = event.getTarget();
/* 1197 */     if (target instanceof Base) {
/* 1198 */       InventoryMessage.sendTo((EntityPlayerMP)event.getEntityPlayer(), (Base)target);
/*      */     }
/*      */   }
/*      */ 
/*      */   
/*      */   public void preInit(FMLPreInitializationEvent event) {
/* 1204 */     this.elements.addNetworkMessage(ChakraMessage.Handler.class, ChakraMessage.class, new Side[] { Side.SERVER });
/* 1205 */     this.elements.addNetworkMessage(InventoryMessage.Handler.class, InventoryMessage.class, new Side[] { Side.CLIENT });
/* 1206 */     this.elements.addNetworkMessage(StandStillMessage.Handler.class, StandStillMessage.class, new Side[] { Side.CLIENT });
/*      */   }
/*      */ 
/*      */   
/*      */   public void init(FMLInitializationEvent event) {
/* 1211 */     MinecraftForge.EVENT_BUS.register(this);
/*      */   }
/*      */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityNinjaMobAddon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */