{"credit": "Made with Blockbench", "ambientocclusion": false, "elements": [{"from": [7, 7, 9], "to": [9, 9, 23], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 16]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}, {"from": [7.2, 7.2, 3.2], "to": [8.8, 8.8, 16.8], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 10]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}, {"from": [7.4, 7.4, -2.6], "to": [8.6, 8.6, 10.6], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, 4]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}, {"from": [7.6, 7.6, -7.4], "to": [8.4, 8.4, 5.4], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -1]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}, {"from": [7.7, 7.7, -9.55], "to": [8.3, 8.3, 3.05], "rotation": {"angle": 0, "axis": "y", "origin": [8, 8, -3.25]}, "faces": {"north": {"uv": [6, 0, 4, 2], "rotation": 180, "texture": "#0"}, "east": {"uv": [0, 2, 2, 16], "rotation": 270, "texture": "#0"}, "south": {"uv": [4, 2, 2, 0], "texture": "#0"}, "west": {"uv": [4, 2, 6, 16], "rotation": 90, "texture": "#0"}, "up": {"uv": [2, 2, 4, 16], "rotation": 180, "texture": "#0"}, "down": {"uv": [6, 2, 8, 16], "texture": "#0"}}}], "gui_light": "front", "display": {"thirdperson_righthand": {"rotation": [75, 0, 0], "translation": [0, 3.5, 0]}, "thirdperson_lefthand": {"rotation": [75, 0, 0], "translation": [0, 3.5, 0]}, "firstperson_righthand": {"rotation": [75, 0, 0], "translation": [0, 3.5, 0]}, "firstperson_lefthand": {"rotation": [75, 0, 0], "translation": [0, 3.5, 0]}, "ground": {"rotation": [0, -90, -180]}, "gui": {"rotation": [75, -45, 0], "translation": [6.75, 6.5, 0], "scale": [1.25, 1.25, 1.25]}, "fixed": {"rotation": [90, 45, 0], "translation": [-7, 6.75, 0.25], "scale": [1.4, 1.4, 1.4]}}, "groups": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [{"name": "group", "origin": [8, 8, 8], "color": 0, "children": [0]}, {"name": "group", "origin": [8, 8, 8], "color": 0, "children": [1]}, {"name": "group", "origin": [8, 8, 8], "color": 0, "children": [2]}, {"name": "group", "origin": [8, 8, 8], "color": 0, "children": [3]}, {"name": "group", "origin": [8, 8, 8], "color": 0, "children": [4]}]}]}