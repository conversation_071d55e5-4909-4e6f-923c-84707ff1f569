/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import com.google.common.collect.Multimap;
/*    */ import java.util.HashMap;
/*    */ import java.util.Set;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.SharedMonsterAttributes;
/*    */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.item.ItemSword;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.common.util.EnumHelper;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ 
/*    */ 
/*    */ @Tag
/*    */ public class ItemChakraKnife
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   @ObjectHolder("borutomodaddononero:chakra_knife")
/* 30 */   public static final Item block = null;
/*    */   public ItemChakraKnife(ElementsBorutomodaddononeroMod instance) {
/* 32 */     super(instance, 15);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 37 */     this.elements.items.add(() -> ((Item)(new ItemSword(EnumHelper.addToolMaterial("CHAKRA_KNIFE", 0, 30, 0.0F, 13.0F, 0))
/*    */         {
/*    */           public Multimap<String, AttributeModifier> func_111205_h(EntityEquipmentSlot slot) {
/* 40 */             Multimap<String, AttributeModifier> multimap = super.func_111205_h(slot);
/* 41 */             if (slot == EntityEquipmentSlot.MAINHAND) {
/* 42 */               multimap.put(SharedMonsterAttributes.field_111264_e.func_111108_a(), new AttributeModifier(field_111210_e, "Weapon modifier", 
/* 43 */                     func_150931_i(), 0));
/* 44 */               multimap.put(SharedMonsterAttributes.field_188790_f.func_111108_a(), new AttributeModifier(field_185050_h, "Weapon modifier", -2.4D, 0));
/*    */             } 
/*    */             
/* 47 */             return multimap;
/*    */           }
/*    */           
/*    */           public Set<String> getToolClasses(ItemStack stack) {
/* 51 */             HashMap<Object, Object> ret = new HashMap<>();
/* 52 */             ret.put("sword", Integer.valueOf(0));
/* 53 */             return ret.keySet();
/*    */           }
/*    */         }).func_77655_b("chakra_knife").setRegistryName("chakra_knife")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 61 */     ModelLoader.setCustomModelResourceLocation(block, 0, new ModelResourceLocation("borutomodaddononero:chakra_knife", "inventory"));
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemChakraKnife.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */