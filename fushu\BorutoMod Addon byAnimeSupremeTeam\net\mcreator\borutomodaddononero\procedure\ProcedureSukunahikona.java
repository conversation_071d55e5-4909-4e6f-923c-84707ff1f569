/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.entity.EntitySukunahikona;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundCategory;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.Chakra;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSukunahikona
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSukunahikona(ElementsBorutomodaddononeroMod instance) {
/* 20 */     super(instance, 263);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 24 */     if (dependencies.get("is_pressed") == null) {
/* 25 */       System.err.println("Failed to load dependency is_pressed for procedure Sukunahikona!");
/*    */       return;
/*    */     } 
/* 28 */     if (dependencies.get("entity") == null) {
/* 29 */       System.err.println("Failed to load dependency entity for procedure Sukunahikona!");
/*    */       return;
/*    */     } 
/* 32 */     if (dependencies.get("world") == null) {
/* 33 */       System.err.println("Failed to load dependency world for procedure Sukunahikona!");
/*    */       return;
/*    */     } 
/* 36 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 37 */     Entity entity = (Entity)dependencies.get("entity");
/* 38 */     World world = (World)dependencies.get("world");
/*    */     
/* 40 */     if (is_pressed) {
/* 41 */       if (!(entity.func_184187_bx() instanceof EntitySukunahikona.Sukunahikona) && 
/* 42 */         Chakra.pathway((EntityPlayer)entity).getAmount() >= 450.0D) {
/* 43 */         Chakra.pathway((EntityPlayer)entity).consume(450.0D);
/* 44 */         world.func_184148_a((EntityPlayer)null, entity.field_70165_t, entity.field_70163_u, entity.field_70161_v, (SoundEvent)SoundEvent.field_187505_a
/*    */             
/* 46 */             .func_82594_a(new ResourceLocation("borutomodaddononero:sukunahikona")), SoundCategory.NEUTRAL, 3.0F, 1.0F);
/*    */         
/* 48 */         EntitySukunahikona.Sukunahikona entitySmallerMe = new EntitySukunahikona.Sukunahikona((EntityLivingBase)entity, 0.1F);
/* 49 */         entitySmallerMe.func_70107_b(entity.field_70165_t, entity.field_70163_u, entity.field_70161_v);
/* 50 */         entity.field_70170_p.func_72838_d((Entity)entitySmallerMe);
/*    */       } 
/*    */     } else {
/*    */       
/* 54 */       Entity ridingEntity = entity.func_184187_bx();
/* 55 */       if (ridingEntity instanceof EntitySukunahikona.Sukunahikona) {
/* 56 */         double ticksExisted = ridingEntity.field_70173_aa;
/* 57 */         entity.func_184210_p();
/* 58 */         ridingEntity.func_70106_y();
/*    */ 
/*    */         
/* 61 */         if (entity instanceof EntityPlayer)
/* 62 */           ((EntityPlayer)entity).func_71024_bL().func_75114_a(((EntityPlayer)entity)
/* 63 */               .func_71024_bL().func_75116_a() - (int)(ticksExisted / 60.0D + 1.0D)); 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSukunahikona.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */