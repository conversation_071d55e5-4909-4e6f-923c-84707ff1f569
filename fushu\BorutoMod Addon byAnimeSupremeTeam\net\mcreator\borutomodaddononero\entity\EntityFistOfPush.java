/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.OpenGlHelper;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.util.math.RayTraceResult;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.client.model.obj.OBJLoader;
/*     */ import net.minecraftforge.event.ForgeEventFactory;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.Particles;
/*     */ import net.narutomod.entity.EntityRendererRegister;
/*     */ 
/*     */ @Tag
/*     */ public class EntityFistOfPush
/*     */   extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public EntityFistOfPush(ElementsBorutomodaddononeroMod instance) {
/*  36 */     super(instance, 363);
/*     */   }
/*     */   public static final int ENTITYID = 160;
/*     */   public void initElements() {
/*  40 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(FistOfPush.class).id(new ResourceLocation("borutomodaddononero", "fistofpush"), 160).name("fistofpush").tracker(64, 1, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  48 */     OBJLoader.INSTANCE.addDomain("borutomodaddononero");
/*     */   }
/*     */   
/*     */   public static class FistOfPush extends EntityScalableProjectile.Base {
/*  52 */     public final float explosionStrength = 6.0F;
/*     */     
/*     */     public FistOfPush(World a) {
/*  55 */       super(a);
/*  56 */       setOGSize(0.5F, 0.25F);
/*  57 */       setEntityScale(2.0F);
/*     */     }
/*     */     
/*     */     public FistOfPush(EntityLivingBase shooter) {
/*  61 */       super(shooter);
/*  62 */       setOGSize(0.5F, 0.25F);
/*  63 */       setEntityScale(2.0F);
/*     */ 
/*     */ 
/*     */       
/*  67 */       Vec3d vec = shooter.func_70040_Z().func_186678_a(1.8D).func_178785_b((this.field_70146_Z.nextFloat() - 0.5F) * 90.0F * 0.01745329F).func_178789_a((this.field_70146_Z.nextFloat() - 0.5F) * 60.0F * 0.01745329F).func_72441_c(shooter.field_70165_t, shooter.field_70163_u + 1.2D, shooter.field_70161_v);
/*  68 */       func_70012_b(vec.field_72450_a, vec.field_72448_b, vec.field_72449_c, shooter.field_70759_as, shooter.field_70125_A);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/*  73 */       super.func_70071_h_();
/*  74 */       setEntityScale(getEntityScale() + 1.0F);
/*  75 */       if (!this.field_70170_p.field_72995_K && this.ticksAlive > 10) {
/*  76 */         func_70106_y();
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     protected void onImpact(RayTraceResult result) {
/*  82 */       if (!this.field_70170_p.field_72995_K) {
/*  83 */         if (result.field_72308_g != null) {
/*  84 */           if (result.field_72308_g.equals(this.shootingEntity) || result.field_72308_g instanceof FistOfPush) {
/*     */             return;
/*     */           }
/*  87 */           if (result.field_72308_g instanceof EntityLivingBase) {
/*     */             
/*  89 */             result.field_72308_g.field_70172_ad = 10;
/*  90 */             result.field_72308_g.func_70097_a(DamageSource.func_188403_a(this, this.shootingEntity).func_76348_h(), 500.0F);
/*     */           } 
/*     */         } 
/*  93 */         func_70106_y();
/*  94 */         getClass(); this.field_70170_p.func_72885_a((Entity)this.shootingEntity, this.field_70165_t, this.field_70163_u, this.field_70161_v, 6.0F, false, 
/*  95 */             ForgeEventFactory.getMobGriefingEvent(this.field_70170_p, (Entity)this.shootingEntity));
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void renderParticles() {
/* 101 */       for (int i = 0; i < 100; i++) {
/* 102 */         Particles.spawnParticle(this.field_70170_p, Particles.Types.SMOKE, this.field_70165_t, this.field_70163_u + (this.field_70131_O * 0.5F), this.field_70161_v, 1, 0.0D, (this.field_70131_O * 0.5F), 0.0D, (this.field_70146_Z
/* 103 */             .nextDouble() - 0.5D) * 1.5D, (this.field_70146_Z
/* 104 */             .nextDouble() - 0.5D) * 1.5D, (this.field_70146_Z.nextDouble() - 0.5D) * 1.5D, new int[] { 553582592, 30 + this.field_70146_Z.nextInt(20), 0 });
/*     */       } 
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     protected void checkOnGround() {}
/*     */ 
/*     */     
/*     */     public boolean func_180427_aV() {
/* 114 */       return true;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void preInit(FMLPreInitializationEvent event) {
/* 120 */     (new Renderer()).register();
/*     */   }
/*     */   
/*     */   public static class Renderer
/*     */     extends EntityRendererRegister {
/*     */     @SideOnly(Side.CLIENT)
/*     */     public void register() {
/* 127 */       RenderingRegistry.registerEntityRenderingHandler(EntityFistOfPush.FistOfPush.class, renderManager -> new RenderFistOfPush(renderManager));
/*     */     }
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public class RenderFistOfPush extends Render<EntityFistOfPush.FistOfPush> {
/* 132 */       private final ResourceLocation texture = new ResourceLocation("borutomodaddononero:textures/fistofpush.png");
/* 133 */       private final EntityFistOfPush.Renderer.ModelArmFist mainModel = new EntityFistOfPush.Renderer.ModelArmFist();
/*     */       
/*     */       public RenderFistOfPush(RenderManager renderManager) {
/* 136 */         super(renderManager);
/* 137 */         this.field_76989_e = 0.1F;
/*     */       }
/*     */ 
/*     */       
/*     */       public void doRender(EntityFistOfPush.FistOfPush entity, double x, double y, double z, float entityYaw, float pt) {
/* 142 */         func_180548_c(entity);
/* 143 */         GlStateManager.func_179094_E();
/* 144 */         GlStateManager.func_179129_p();
/* 145 */         float scale = entity.getEntityScale();
/* 146 */         GlStateManager.func_179109_b((float)x, (float)y, (float)z);
/* 147 */         GlStateManager.func_179152_a(scale, scale, scale);
/* 148 */         GlStateManager.func_179114_b(-entity.field_70126_B - MathHelper.func_76142_g(entity.field_70177_z - entity.field_70126_B) * pt, 0.0F, 1.0F, 0.0F);
/* 149 */         GlStateManager.func_179114_b(entity.field_70127_C + (entity.field_70125_A - entity.field_70127_C) * pt - 180.0F, 1.0F, 0.0F, 0.0F);
/* 150 */         GlStateManager.func_179147_l();
/* 151 */         GlStateManager.func_179140_f();
/* 152 */         GlStateManager.func_187401_a(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
/* 153 */         OpenGlHelper.func_77475_a(OpenGlHelper.field_77476_b, 240.0F, 240.0F);
/* 154 */         this.mainModel.func_78088_a(entity, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0625F);
/* 155 */         GlStateManager.func_179145_e();
/* 156 */         GlStateManager.func_179084_k();
/* 157 */         GlStateManager.func_179089_o();
/* 158 */         GlStateManager.func_179121_F();
/*     */       }
/*     */ 
/*     */       
/*     */       protected ResourceLocation getEntityTexture(EntityFistOfPush.FistOfPush entity) {
/* 163 */         return this.texture;
/*     */       }
/*     */     }
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public class ModelArmFist extends ModelBase {
/*     */       private final ModelRenderer bb_main;
/*     */       
/*     */       public ModelArmFist() {
/* 172 */         this.field_78090_t = 32;
/* 173 */         this.field_78089_u = 32;
/*     */         
/* 175 */         this.bb_main = new ModelRenderer(this);
/* 176 */         this.bb_main.func_78793_a(0.0F, 0.0F, 0.0F);
/* 177 */         this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 12, -2.0F, -4.0F, -1.0F, 4, 4, 8, 0.0F, false));
/* 178 */         this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 12, -2.0F, -4.0F, -2.0F, 4, 4, 8, 0.1F, false));
/* 179 */         this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 12, -2.0F, -4.0F, -3.0F, 4, 4, 8, 0.2F, false));
/* 180 */         this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 0, -2.0F, -4.0F, -4.0F, 4, 4, 8, 0.3F, false));
/*     */       }
/*     */ 
/*     */       
/*     */       public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 185 */         this.bb_main.func_78785_a(f5);
/*     */       }
/*     */       
/*     */       public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 189 */         modelRenderer.field_78795_f = x;
/* 190 */         modelRenderer.field_78796_g = y;
/* 191 */         modelRenderer.field_78808_h = z;
/*     */       }
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityFistOfPush.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */