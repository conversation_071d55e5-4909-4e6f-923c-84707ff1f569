package net.narutomod.item;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
import net.minecraftforge.client.model.ModelLoader;
import net.minecraftforge.client.event.ModelRegistryEvent;

import net.minecraft.world.World;
import net.minecraft.util.text.TextFormatting;
import net.minecraft.item.ItemStack;
import net.minecraft.item.ItemArmor;
import net.minecraft.item.Item;
import net.minecraft.inventory.EntityEquipmentSlot;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.Entity;
import net.minecraft.client.util.ITooltipFlag;
import net.minecraft.client.renderer.block.model.ModelResourceLocation;
import net.minecraft.client.model.ModelBiped;
import net.minecraft.entity.EntityLivingBase;

import net.narutomod.creativetab.TabModTab;
import net.narutomod.ElementsNarutomodMod;
import net.narutomod.procedure.ProcedureDaikokutenCube;
import net.narutomod.Chakra;

import javax.annotation.Nullable;
import java.util.List;
import java.util.HashMap;
import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ItemIsshikiDojutsu extends ElementsNarutomodMod.ModElement {
	@ObjectHolder("narutomod:isshikidojutsuhelmet")
	public static final Item helmet = null;
	
	// 查克拉消耗常量
	private static final double SUKUNAHIKONA_CHAKRA_USAGE = 50d;
	private static final double DAIKOKUTEN_CHAKRA_USAGE = 100d;
	
	public ItemIsshikiDojutsu(ElementsNarutomodMod instance) {
		super(instance, 602); // 使用新的ID
	}

	public static double getSukunahikonaChakraUsage(EntityLivingBase entity) {
		ItemStack stack = entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD);
		return stack.getItem() == helmet 
		 ? ((ItemDojutsu.Base)helmet).isOwner(stack, entity) ? SUKUNAHIKONA_CHAKRA_USAGE 
		 : SUKUNAHIKONA_CHAKRA_USAGE * 2 : (Double.MAX_VALUE * 0.001d);
	}

	public static double getDaikokutenChakraUsage(EntityLivingBase entity) {
		ItemStack stack = entity.getItemStackFromSlot(EntityEquipmentSlot.HEAD);
		return stack.getItem() == helmet 
		 ? ((ItemDojutsu.Base)helmet).isOwner(stack, entity) ? DAIKOKUTEN_CHAKRA_USAGE 
		 : DAIKOKUTEN_CHAKRA_USAGE * 2 : (Double.MAX_VALUE * 0.001d);
	}

	public void initElements() {
		ItemArmor.ArmorMaterial enuma = net.minecraftforge.common.util.EnumHelper.addArmorMaterial("ISSHIKIDOJUTSU", "narutomod:isshiki_dojutsu_", 2048,
				new int[]{2, 5, 6, 20}, 0, null, 3.0F);
		this.elements.items.add(() -> new Base(enuma) {
			@Override
			public void onArmorTick(World world, EntityPlayer entity, ItemStack itemstack) {
				super.onArmorTick(world, entity, itemstack);
				
				// 疲劳值处理 - 戴着时每秒消耗2点疲劳（比万花筒更消耗）
				if (!world.isRemote && entity.ticksExisted % 20 == 0) {
					net.narutomod.yongheng.Pilaozhi.drainFatigue(entity);
					net.narutomod.yongheng.Pilaozhi.drainFatigue(entity);
				}
			}

			@Override
			public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
				return "narutomod:textures/isshiki_dojutsu_helmet.png";
			}

			@Override
			public void addInformation(ItemStack stack, @Nullable World worldIn, List<String> tooltip, ITooltipFlag flagIn) {
				super.addInformation(stack, worldIn, tooltip, flagIn);
				tooltip.add(TextFormatting.ITALIC + "特殊忍术1: " + TextFormatting.GRAY + "少名毘古那 (Sukunahikona)");
				tooltip.add(TextFormatting.ITALIC + "特殊忍术2: " + TextFormatting.GRAY + "大黑天 (Daikokuten)");
				tooltip.add(TextFormatting.DARK_RED + "大筒木一族的强大瞳术");
			}

			@Override
			public String getItemStackDisplayName(ItemStack stack) {
				return TextFormatting.DARK_PURPLE + "大筒木黑眼" + TextFormatting.WHITE;
			}

			@Override
			public boolean onJutsuKey1(boolean is_pressed, ItemStack stack, EntityPlayer entity) {
				// 暂时不实现技能，只是基础框架
				if (is_pressed) {
					entity.sendMessage(new net.minecraft.util.text.TextComponentString(
						TextFormatting.GOLD + "少名毘古那技能暂未实现"));
				}
				return true;
			}

			@Override
			public boolean onJutsuKey2(boolean is_pressed, ItemStack stack, EntityPlayer entity) {
				// 大黑天立方体技能
				if (is_pressed) {
					// 检查查克拉是否足够
					if (Chakra.pathway(entity).getAmount() >= getDaikokutenChakraUsage(entity)) {
						// 创建依赖参数映射
						Map<String, Object> dependencies = new HashMap<>();
						dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
						dependencies.put("entity", entity);
						dependencies.put("world", entity.world);

						// 执行大黑天立方体技能
						ProcedureDaikokutenCube.executeProcedure(dependencies);

						entity.sendMessage(new net.minecraft.util.text.TextComponentString(
							TextFormatting.DARK_PURPLE + "大黑天立方体召唤！"));
					} else {
						entity.sendMessage(new net.minecraft.util.text.TextComponentString(
							TextFormatting.RED + "查克拉不足！需要 " + getDaikokutenChakraUsage(entity) + " 查克拉"));
					}
				}
				return true;
			}
		}.setUnlocalizedName("isshikidojutsuhelmet").setRegistryName("isshikidojutsuhelmet").setCreativeTab(TabModTab.tab));
	}

	public static class Base extends ItemDojutsu.Base {
		public Base(ItemArmor.ArmorMaterial material) {
			super(material);
		}

		@Override
		public ItemDojutsu.Type getType() {
			return ItemDojutsu.Type.ISSHIKI_DOJUTSU;
		}

		@SideOnly(Side.CLIENT)
		@Override
		public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
			ItemDojutsu.ClientModel.ModelHelmetSnug armorModel = (ItemDojutsu.ClientModel.ModelHelmetSnug)super.getArmorModel(living, stack, slot, defaultModel);
			armorModel.headwearHide = true;
			armorModel.onface.showModel = true; // 始终显示瞳术效果
			armorModel.highlightHide = false; // 显示高亮效果
			return armorModel;
		}

		@Override
		public void onUpdate(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
			super.onUpdate(itemstack, world, entity, par4, par5);
			// 可以在这里添加特殊的更新逻辑
			if (entity instanceof EntityPlayer) {
				this.onUpdatePost((EntityPlayer)entity);
			}
		}

		public void onUpdatePost(EntityPlayer player) {
			// 可以在这里添加特殊的后更新逻辑
			// 例如：启用特殊忍术等
		}
	}

	@Override
	@SideOnly(Side.CLIENT)
	public void registerModels(ModelRegistryEvent event) {
		ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("narutomod:isshikidojutsuhelmet", "inventory"));
	}
}
