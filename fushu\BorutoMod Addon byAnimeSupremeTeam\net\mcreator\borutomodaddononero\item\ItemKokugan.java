/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ 
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*     */ import net.mcreator.borutomodaddononero.procedure.ProcedureDaikokutenCube;
/*     */ import net.mcreator.borutomodaddononero.procedure.ProcedureKokuganHelmetTickEvent;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.client.util.ITooltipFlag;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.item.ItemArmor;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.util.text.translation.I18n;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.common.util.EnumHelper;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.item.ItemDojutsu;
/*     */ 
/*     */ 
/*     */ 
/*     */ @Tag
/*     */ public class ItemKokugan
/*     */   extends ElementsBorutomodaddononeroMod.ModElement
/*     */ {
/*     */   @ObjectHolder("borutomodaddononero:kokuganhelmet")
/*  37 */   public static final Item helmet = null;
/*     */   public static final int ENTITYID = 49;
/*     */   
/*     */   public ItemKokugan(ElementsBorutomodaddononeroMod instance) {
/*  41 */     super(instance, 98);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  46 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("KOKUGAN", "borutomodaddononero:kokugan_", 25, new int[] { 15, 50, 40, 15 }, 9, null, 0.0F);
/*     */     
/*  48 */     this.elements.items.add(() -> ((Item)(new ItemDojutsu.Base(enuma)
/*     */         {
/*     */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type)
/*     */           {
/*  52 */             return "borutomodaddononero:textures/isshiki_dojutsu_texture.png";
/*     */           }
/*     */ 
/*     */           
/*     */           public void onArmorTick(World world, EntityPlayer entity, ItemStack itemstack) {
/*  57 */             super.onArmorTick(world, entity, itemstack);
/*  58 */             isOwner(itemstack, (EntityLivingBase)entity);
/*  59 */             int x = (int)entity.field_70165_t;
/*  60 */             int y = (int)entity.field_70163_u;
/*  61 */             int z = (int)entity.field_70161_v;
/*  62 */             HashMap<Object, Object> $_dependencies = new HashMap<>();
/*  63 */             $_dependencies.put("entity", entity);
/*  64 */             $_dependencies.put("world", world);
/*  65 */             $_dependencies.put("itemstack", itemstack);
/*  66 */             ProcedureKokuganHelmetTickEvent.executeProcedure($_dependencies);
/*     */             
/*  68 */             if (!entity.func_70093_af() && 
/*  69 */               entity.func_70093_af()) {
/*  70 */               ProcedureDaikokutenCube.executeProcedure($_dependencies);
/*     */             }
/*     */           }
/*     */ 
/*     */ 
/*     */           
/*     */           public void setOwner(ItemStack stack, EntityLivingBase entityIn) {
/*  77 */             super.setOwner(stack, entityIn);
/*  78 */             stack.func_77978_p().func_74780_a("KokuganCount", 1.0D);
/*     */           }
/*     */ 
/*     */           
/*     */           public int func_77612_l() {
/*  83 */             return 0;
/*     */           }
/*     */ 
/*     */           
/*     */           public boolean func_77645_m() {
/*  88 */             return false;
/*     */           }
/*     */ 
/*     */           
/*     */           @SideOnly(Side.CLIENT)
/*     */           public void func_77624_a(ItemStack stack, @Nullable World worldIn, List<String> tooltip, ITooltipFlag flagIn) {
/*  94 */             super.func_77624_a(stack, worldIn, tooltip, flagIn);
/*  95 */             tooltip.add(I18n.func_74838_a("key.mcreator.specialjutsu1") + ": " + I18n.func_74838_a("entity.sukunahikona.name"));
/*  96 */             tooltip.add(I18n.func_74838_a("key.mcreator.specialjutsu2") + ": " + I18n.func_74838_a("entity.daikokutencube.name"));
/*  97 */             tooltip.add(I18n.func_74838_a("key.mcreator.specialjutsu3") + ": " + I18n.func_74838_a("entity.samidare_spear.name"));
/*     */           }
/*     */         }).func_77655_b("kokuganhelmet").setRegistryName("kokuganhelmet")).func_77637_a(TabBorutoExpansion.tab));
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/* 104 */     ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("borutomodaddononero:kokuganhelmet", "inventory"));
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemKokugan.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */