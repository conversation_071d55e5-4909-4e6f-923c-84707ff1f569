/*     */ package net.mcreator.borutomodaddononero;
/*     */ 
/*     */ import io.netty.buffer.ByteBuf;
/*     */ import net.minecraft.client.Minecraft;
/*     */ import net.minecraft.nbt.NBTTagCompound;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraft.world.storage.WorldSavedData;
/*     */ import net.minecraftforge.fml.common.network.ByteBufUtils;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ 
/*     */ public class BorutomodaddononeroModVariables {
/*     */   public static class MapVariables
/*     */     extends WorldSavedData {
/*     */     public MapVariables() {
/*  18 */       super("borutomodaddononero_mapvars");
/*     */     }
/*     */     public static final String DATA_NAME = "borutomodaddononero_mapvars";
/*     */     public MapVariables(String s) {
/*  22 */       super(s);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void func_76184_a(NBTTagCompound nbt) {}
/*     */ 
/*     */     
/*     */     public NBTTagCompound func_189551_b(NBTTagCompound nbt) {
/*  31 */       return nbt;
/*     */     }
/*     */     
/*     */     public void syncData(World world) {
/*  35 */       func_76185_a();
/*  36 */       if (world.field_72995_K) {
/*  37 */         BorutomodaddononeroMod.PACKET_HANDLER.sendToServer(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(0, this));
/*     */       } else {
/*  39 */         BorutomodaddononeroMod.PACKET_HANDLER.sendToAll(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(0, this));
/*     */       } 
/*     */     }
/*     */     
/*     */     public static MapVariables get(World world) {
/*  44 */       MapVariables instance = (MapVariables)world.func_175693_T().func_75742_a(MapVariables.class, "borutomodaddononero_mapvars");
/*  45 */       if (instance == null) {
/*  46 */         instance = new MapVariables();
/*  47 */         world.func_175693_T().func_75745_a("borutomodaddononero_mapvars", instance);
/*     */       } 
/*  49 */       return instance;
/*     */     } }
/*     */   
/*     */   public static class WorldVariables extends WorldSavedData {
/*     */     public static final String DATA_NAME = "borutomodaddononero_worldvars";
/*     */     
/*     */     public WorldVariables() {
/*  56 */       super("borutomodaddononero_worldvars");
/*     */     }
/*     */     
/*     */     public WorldVariables(String s) {
/*  60 */       super(s);
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void func_76184_a(NBTTagCompound nbt) {}
/*     */ 
/*     */     
/*     */     public NBTTagCompound func_189551_b(NBTTagCompound nbt) {
/*  69 */       return nbt;
/*     */     }
/*     */     
/*     */     public void syncData(World world) {
/*  73 */       func_76185_a();
/*  74 */       if (world.field_72995_K) {
/*  75 */         BorutomodaddononeroMod.PACKET_HANDLER.sendToServer(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(1, this));
/*     */       } else {
/*  77 */         BorutomodaddononeroMod.PACKET_HANDLER.sendToDimension(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(1, this), world.field_73011_w.getDimension());
/*     */       } 
/*     */     }
/*     */     
/*     */     public static WorldVariables get(World world) {
/*  82 */       WorldVariables instance = (WorldVariables)world.getPerWorldStorage().func_75742_a(WorldVariables.class, "borutomodaddononero_worldvars");
/*  83 */       if (instance == null) {
/*  84 */         instance = new WorldVariables();
/*  85 */         world.getPerWorldStorage().func_75745_a("borutomodaddononero_worldvars", instance);
/*     */       } 
/*  87 */       return instance;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class WorldSavedDataSyncMessageHandler
/*     */     implements IMessageHandler<WorldSavedDataSyncMessage, IMessage> {
/*     */     public IMessage onMessage(BorutomodaddononeroModVariables.WorldSavedDataSyncMessage message, MessageContext context) {
/*  94 */       if (context.side == Side.SERVER) {
/*  95 */         (context.getServerHandler()).field_147369_b.func_71121_q()
/*  96 */           .func_152344_a(() -> syncData(message, context, (context.getServerHandler()).field_147369_b.field_70170_p));
/*     */       } else {
/*  98 */         Minecraft.func_71410_x().func_152344_a(() -> syncData(message, context, (Minecraft.func_71410_x()).field_71439_g.field_70170_p));
/*  99 */       }  return null;
/*     */     }
/*     */     
/*     */     private void syncData(BorutomodaddononeroModVariables.WorldSavedDataSyncMessage message, MessageContext context, World world) {
/* 103 */       if (context.side == Side.SERVER) {
/* 104 */         message.data.func_76185_a();
/* 105 */         if (message.type == 0) {
/* 106 */           BorutomodaddononeroMod.PACKET_HANDLER.sendToAll(message);
/*     */         } else {
/* 108 */           BorutomodaddononeroMod.PACKET_HANDLER.sendToDimension(message, world.field_73011_w.getDimension());
/*     */         } 
/* 110 */       }  if (message.type == 0) {
/* 111 */         world.func_175693_T().func_75745_a("borutomodaddononero_mapvars", message.data);
/*     */       } else {
/* 113 */         world.getPerWorldStorage().func_75745_a("borutomodaddononero_worldvars", message.data);
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   public static class WorldSavedDataSyncMessage implements IMessage {
/*     */     public int type;
/*     */     public WorldSavedData data;
/*     */     
/*     */     public WorldSavedDataSyncMessage() {}
/*     */     
/*     */     public WorldSavedDataSyncMessage(int type, WorldSavedData data) {
/* 125 */       this.type = type;
/* 126 */       this.data = data;
/*     */     }
/*     */ 
/*     */     
/*     */     public void toBytes(ByteBuf buf) {
/* 131 */       buf.writeInt(this.type);
/* 132 */       ByteBufUtils.writeTag(buf, this.data.func_189551_b(new NBTTagCompound()));
/*     */     }
/*     */ 
/*     */     
/*     */     public void fromBytes(ByteBuf buf) {
/* 137 */       this.type = buf.readInt();
/* 138 */       if (this.type == 0) {
/* 139 */         this.data = new BorutomodaddononeroModVariables.MapVariables();
/*     */       } else {
/* 141 */         this.data = new BorutomodaddononeroModVariables.WorldVariables();
/* 142 */       }  this.data.func_76184_a(ByteBufUtils.readTag(buf));
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\BorutomodaddononeroModVariables.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */