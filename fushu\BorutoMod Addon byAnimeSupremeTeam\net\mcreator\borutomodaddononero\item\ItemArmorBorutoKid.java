/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ 
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.item.ItemNinjaArmor;
/*     */ 
/*     */ @Tag
/*     */ public class ItemArmorBorutoKid extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   @ObjectHolder("borutomodaddononero:armor_boruto_kidbody")
/*  23 */   public static final Item body = null;
/*     */   @ObjectHolder("borutomodaddononero:armor_boruto_kidlegs")
/*  25 */   public static final Item legs = null;
/*     */   
/*     */   public ItemArmorBorutoKid(ElementsBorutomodaddononeroMod instance) {
/*  28 */     super(instance, 691);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  33 */     this.elements.items.add(() -> ((Item)(new ItemNinjaArmor.Base(ItemNinjaArmor.Type.OTHER, EntityEquipmentSlot.CHEST)
/*     */         {
/*     */           protected ItemNinjaArmor.ArmorData setArmorData(ItemNinjaArmor.Type type, EntityEquipmentSlot slotIn) {
/*  36 */             return new Armor4SlotBody();
/*     */           }
/*     */           
/*     */           class Armor4SlotBody
/*     */             extends ItemNinjaArmor.ArmorData {
/*     */             @SideOnly(Side.CLIENT)
/*     */             protected void init() {
/*  43 */               this.model = new ItemArmorBorutoKid.ModelArmorBorutoKid();
/*  44 */               this.texture = "borutomodaddononero:textures/armor_boruto_kid.png";
/*     */             }
/*     */ 
/*     */             
/*     */             @SideOnly(Side.CLIENT)
/*     */             public void setSlotVisible() {
/*  50 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178720_f.field_78806_j = true;
/*  51 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirt.field_78806_j = true;
/*  52 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).vest.field_78806_j = false;
/*  53 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).rightArmVestLayer.field_78806_j = false;
/*  54 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).leftArmVestLayer.field_78806_j = false;
/*  55 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirtRightArm.field_78806_j = true;
/*  56 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirtLeftArm.field_78806_j = true;
/*  57 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178721_j.field_78806_j = false;
/*  58 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178722_k.field_78806_j = false;
/*  59 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_78116_c.field_78806_j = false;
/*     */             }
/*     */           }
/*     */         }).func_77655_b("armor_boruto_kidbody").setRegistryName("armor_boruto_kidbody")).func_77637_a(TabBorutoExpansion.tab));
/*     */     
/*  64 */     this.elements.items.add(() -> ((Item)(new ItemNinjaArmor.Base(ItemNinjaArmor.Type.OTHER, EntityEquipmentSlot.LEGS)
/*     */         {
/*     */           protected ItemNinjaArmor.ArmorData setArmorData(ItemNinjaArmor.Type type, EntityEquipmentSlot slotIn) {
/*  67 */             return new Armor4SlotLegs();
/*     */           }
/*     */           
/*     */           class Armor4SlotLegs
/*     */             extends ItemNinjaArmor.ArmorData {
/*     */             @SideOnly(Side.CLIENT)
/*     */             protected void init() {
/*  74 */               this.model = new ItemArmorBorutoKid.ModelArmorBorutoKid();
/*  75 */               this.texture = "borutomodaddononero:textures/armor_boruto_kid.png";
/*     */             }
/*     */ 
/*     */             
/*     */             @SideOnly(Side.CLIENT)
/*     */             public void setSlotVisible() {
/*  81 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178720_f.field_78806_j = false;
/*  82 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirt.field_78806_j = false;
/*  83 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).vest.field_78806_j = true;
/*  84 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178723_h.field_78806_j = true;
/*  85 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178724_i.field_78806_j = true;
/*  86 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirtRightArm.field_78806_j = false;
/*  87 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).shirtLeftArm.field_78806_j = false;
/*  88 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178721_j.field_78806_j = true;
/*  89 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_178722_k.field_78806_j = true;
/*  90 */               ((ItemArmorBorutoKid.ModelArmorBorutoKid)this.model).field_78116_c.field_78806_j = false;
/*     */             }
/*     */           }
/*     */         }).func_77655_b("armor_boruto_kidlegs").setRegistryName("armor_boruto_kidlegs")).func_77637_a(TabBorutoExpansion.tab));
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/*  99 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:armor_boruto_kidbody", "inventory"));
/* 100 */     ModelLoader.setCustomModelResourceLocation(legs, 0, new ModelResourceLocation("borutomodaddononero:armor_boruto_kidlegs", "inventory"));
/*     */   }
/*     */   
/*     */   public static class ModelArmorBorutoKid
/*     */     extends ModelBiped
/*     */   {
/*     */     public final ModelRenderer shirt;
/*     */     public final ModelRenderer vest;
/*     */     public final ModelRenderer shirtRightArm;
/*     */     
/*     */     public ModelArmorBorutoKid() {
/* 111 */       super(1.0F);
/*     */       
/* 113 */       this.field_78090_t = 64;
/* 114 */       this.field_78089_u = 64;
/*     */       
/* 116 */       this.field_178720_f = new ModelRenderer((ModelBase)this);
/* 117 */       this.field_178720_f.func_78793_a(0.0F, 0.0F, 0.0F);
/* 118 */       this.field_178720_f.field_78804_l.add(new ModelBox(this.field_178720_f, 0, 6, -4.0F, -2.0F, -4.0F, 8, 2, 8, 0.125F, false));
/* 119 */       this.field_178720_f.field_78804_l.add(new ModelBox(this.field_178720_f, 32, 6, -4.0F, -2.0F, -4.0F, 8, 2, 8, 0.15F, false));
/*     */       
/* 121 */       this.field_78115_e = new ModelRenderer((ModelBase)this);
/* 122 */       this.field_78115_e.func_78793_a(0.0F, 0.0F, 0.0F);
/*     */       
/* 124 */       this.shirt = new ModelRenderer((ModelBase)this);
/* 125 */       this.shirt.func_78793_a(0.0F, 0.0F, 0.0F);
/* 126 */       this.field_78115_e.func_78792_a(this.shirt);
/* 127 */       this.shirt.field_78804_l.add(new ModelBox(this.shirt, 16, 32, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.125F, false));
/* 128 */       this.shirt.field_78804_l.add(new ModelBox(this.shirt, 40, 32, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.15F, false));
/*     */       
/* 130 */       this.vest = new ModelRenderer((ModelBase)this);
/* 131 */       this.vest.func_78793_a(0.0F, 0.0F, 0.0F);
/* 132 */       this.field_78115_e.func_78792_a(this.vest);
/* 133 */       this.vest.field_78804_l.add(new ModelBox(this.vest, 16, 16, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.025F, false));
/*     */       
/* 135 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 136 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/*     */       
/* 138 */       this.shirtRightArm = new ModelRenderer((ModelBase)this);
/* 139 */       this.shirtRightArm.func_78793_a(0.0F, 0.0F, 0.0F);
/* 140 */       this.field_178723_h.func_78792_a(this.shirtRightArm);
/* 141 */       this.shirtRightArm.field_78804_l.add(new ModelBox(this.shirtRightArm, 48, 48, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.15F, false));
/*     */       
/* 143 */       this.rightArmVestLayer = new ModelRenderer((ModelBase)this);
/* 144 */       this.rightArmVestLayer.func_78793_a(0.0F, 0.0F, 0.0F);
/* 145 */       this.field_178723_h.func_78792_a(this.rightArmVestLayer);
/* 146 */       this.rightArmVestLayer.field_78804_l.add(new ModelBox(this.rightArmVestLayer, 40, 16, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.025F, false));
/*     */       
/* 148 */       this.field_178724_i = new ModelRenderer((ModelBase)this);
/* 149 */       this.field_178724_i.func_78793_a(5.0F, 2.0F, 0.0F);
/*     */       
/* 151 */       this.shirtLeftArm = new ModelRenderer((ModelBase)this);
/* 152 */       this.shirtLeftArm.func_78793_a(0.0F, 0.0F, 0.0F);
/* 153 */       this.field_178724_i.func_78792_a(this.shirtLeftArm);
/* 154 */       this.shirtLeftArm.field_78804_l.add(new ModelBox(this.shirtLeftArm, 48, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.15F, true));
/*     */       
/* 156 */       this.leftArmVestLayer = new ModelRenderer((ModelBase)this);
/* 157 */       this.leftArmVestLayer.func_78793_a(0.0F, 0.0F, 0.0F);
/* 158 */       this.field_178724_i.func_78792_a(this.leftArmVestLayer);
/* 159 */       this.leftArmVestLayer.field_78804_l.add(new ModelBox(this.leftArmVestLayer, 40, 16, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.025F, true));
/*     */       
/* 161 */       this.field_178721_j = new ModelRenderer((ModelBase)this);
/* 162 */       this.field_178721_j.func_78793_a(-1.9F, 12.0F, 0.0F);
/* 163 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.15F, false));
/* 164 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 0, 32, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.175F, false));
/* 165 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 0, 0, -3.0F, 1.0F, -1.0F, 1, 4, 2, 0.0F, false));
/*     */       
/* 167 */       this.field_178722_k = new ModelRenderer((ModelBase)this);
/* 168 */       this.field_178722_k.func_78793_a(1.9F, 12.0F, 0.0F);
/* 169 */       this.field_178722_k.field_78804_l.add(new ModelBox(this.field_178722_k, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.15F, true));
/*     */     }
/*     */     public final ModelRenderer rightArmVestLayer; public final ModelRenderer shirtLeftArm; public final ModelRenderer leftArmVestLayer;
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 173 */       modelRenderer.field_78795_f = x;
/* 174 */       modelRenderer.field_78796_g = y;
/* 175 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemArmorBorutoKid.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */