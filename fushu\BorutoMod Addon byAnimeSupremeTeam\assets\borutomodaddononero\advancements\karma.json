{"display": {"icon": {"item": "borutomodaddononero:icon_karma_advancement"}, "title": {"translate": "advancements.karma.title"}, "description": {"translate": "advancements.karma.descr"}, "frame": "task", "show_toast": true, "announce_to_chat": true, "hidden": false}, "criteria": {"karma": {"trigger": "minecraft:inventory_changed", "conditions": {"items": [{"item": "borutomodaddononero:nue_icon_tab", "count": {"min": 1, "max": 1}}]}}}, "rewards": {"experience": 100}, "parent": "borutomodaddononero:boro_immortality"}