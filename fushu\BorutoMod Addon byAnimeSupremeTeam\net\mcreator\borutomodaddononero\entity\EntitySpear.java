/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.block.Block;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.network.datasync.DataParameter;
/*     */ import net.minecraft.network.datasync.DataSerializers;
/*     */ import net.minecraft.network.datasync.EntityDataManager;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.math.AxisAlignedBB;
/*     */ import net.minecraft.util.math.BlockPos;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.util.math.RayTraceResult;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraft.world.IBlockAccess;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class EntitySpear extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 219;
/*     */   
/*     */   public EntitySpear(ElementsBorutomodaddononeroMod instance) {
/*  41 */     super(instance, 533);
/*     */   }
/*     */   public static final int ENTITYID_RANGED = 220;
/*     */   
/*     */   public void initElements() {
/*  46 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(Base.class).id(new ResourceLocation("borutomodaddononero", "spear"), 219).name("spear").tracker(64, 3, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  54 */     RenderingRegistry.registerEntityRenderingHandler(Base.class, renderManager -> new Renderer<>(renderManager));
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public static Base spawnSpear(World worldIn, int color) {
/*  59 */     Base entity = new Base(worldIn, color);
/*  60 */     return worldIn.func_72838_d(entity) ? entity : null;
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public static Base spawnSpear(EntityLivingBase entityIn, int color) {
/*  65 */     Base entity = new Base(entityIn, color);
/*  66 */     return entityIn.field_70170_p.func_72838_d(entity) ? entity : null;
/*     */   }
/*     */   
/*     */   public static class Base extends EntityScalableProjectile.Base {
/*  70 */     private static final DataParameter<Integer> COLOR = EntityDataManager.func_187226_a(Base.class, DataSerializers.field_187192_b);
/*  71 */     private Vec3d tipOffset = Vec3d.field_186680_a;
/*     */     
/*     */     public Base(World worldIn) {
/*  74 */       super(worldIn);
/*  75 */       setOGSize(0.5F, 1.5F);
/*     */     }
/*     */     
/*     */     public Base(EntityLivingBase userIn) {
/*  79 */       super(userIn);
/*  80 */       setOGSize(0.5F, 1.5F);
/*     */     }
/*     */     
/*     */     public Base(EntityLivingBase userIn, int color) {
/*  84 */       this(userIn);
/*  85 */       setColor(color);
/*     */     }
/*     */     
/*     */     public Base(World worldIn, int color) {
/*  89 */       this(worldIn);
/*  90 */       setColor(color);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70088_a() {
/*  95 */       super.func_70088_a();
/*  96 */       func_184212_Q().func_187214_a(COLOR, Integer.valueOf(-1));
/*     */     }
/*     */     
/*     */     protected int getColor() {
/* 100 */       return ((Integer)this.field_70180_af.func_187225_a(COLOR)).intValue();
/*     */     }
/*     */     
/*     */     protected void setColor(int color) {
/* 104 */       this.field_70180_af.func_187227_b(COLOR, Integer.valueOf(color));
/*     */     }
/*     */     
/*     */     protected void setTipOffset(Vec3d vec) {
/* 108 */       this.tipOffset = vec;
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70186_c(double x, double y, double z, float speed, float inaccuracy) {
/* 113 */       super.func_70186_c(x, y, z, speed, inaccuracy);
/* 114 */       this.field_70125_A = MathHelper.func_76142_g(this.field_70125_A + 90.0F);
/* 115 */       this.field_70127_C = this.field_70125_A;
/* 116 */       this.tipOffset = new Vec3d(0.0D, 1.82D, 0.0D);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void checkOnGround() {
/* 121 */       Vec3d vec = getTransformedTip();
/* 122 */       BlockPos pos = new BlockPos(vec);
/* 123 */       if (!this.field_70170_p.func_175623_d(pos)) {
/* 124 */         AxisAlignedBB aabb = this.field_70170_p.func_180495_p(pos).func_185890_d((IBlockAccess)this.field_70170_p, pos);
/* 125 */         if (aabb != Block.field_185506_k && aabb.func_186670_a(pos).func_72318_a(vec)) {
/* 126 */           this.field_70122_E = true;
/* 127 */           func_189654_d(false);
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     private Vec3d getTransformedTip() {
/* 133 */       return this.tipOffset.func_186678_a(getEntityScale()).func_178789_a(-this.field_70125_A * 0.017453292F)
/* 134 */         .func_178785_b(-this.field_70177_z * 0.017453292F).func_178787_e(func_174791_d());
/*     */     }
/*     */ 
/*     */     
/*     */     protected RayTraceResult forwardsRaycast(boolean includeEntities, boolean ignoreExcludedEntity, @Nullable Entity excludedEntity) {
/* 139 */       Vec3d vec1 = getTransformedTip();
/* 140 */       Vec3d vec2 = vec1.func_72441_c(this.field_70159_w, this.field_70181_x, this.field_70179_y);
/* 141 */       RayTraceResult raytraceresult = this.field_70170_p.func_147447_a(vec1, vec2, false, true, false);
/* 142 */       if (includeEntities) {
/* 143 */         if (raytraceresult != null) {
/* 144 */           vec2 = raytraceresult.field_72307_f;
/*     */         }
/* 146 */         Entity entity = null;
/* 147 */         AxisAlignedBB bigAABB = func_174813_aQ().func_72321_a(this.field_70159_w, this.field_70181_x, this.field_70179_y).func_186662_g(1.0D);
/* 148 */         double d0 = 0.0D;
/* 149 */         for (Entity entity1 : this.field_70170_p.func_72839_b(this, bigAABB)) {
/* 150 */           if (entity1.func_70067_L() && (ignoreExcludedEntity || !entity1.equals(excludedEntity)) && !entity1.field_70145_X) {
/* 151 */             RayTraceResult result = entity1.func_174813_aQ().func_72327_a(vec1, vec2);
/* 152 */             if (result != null) {
/* 153 */               double d = vec1.func_72438_d(result.field_72307_f);
/* 154 */               if (d < d0 || d0 == 0.0D) {
/* 155 */                 entity = entity1;
/* 156 */                 d0 = d;
/*     */               } 
/*     */             } 
/*     */           } 
/*     */         } 
/* 161 */         if (entity != null) {
/* 162 */           raytraceresult = new RayTraceResult(entity);
/*     */         }
/*     */       } 
/* 165 */       return raytraceresult;
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void renderParticles() {}
/*     */ 
/*     */     
/*     */     protected void onImpact(RayTraceResult result) {
/* 174 */       if (!this.field_70170_p.field_72995_K && result.field_72308_g instanceof EntityLivingBase && !result.field_72308_g.equals(this.shootingEntity)) {
/* 175 */         ((EntityLivingBase)result.field_72308_g).func_70690_d(new PotionEffect(MobEffects.field_76421_d, 200, 1));
/* 176 */         result.field_72308_g.field_70172_ad = 0;
/* 177 */         result.field_72308_g.func_70097_a(DamageSource.func_76356_a(this, (Entity)this.shootingEntity).func_76349_b(), 10.0F);
/* 178 */         func_70106_y();
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class Renderer<T extends Base> extends Render<T> {
/* 185 */     private static final ResourceLocation TEXTURE = new ResourceLocation("borutomodaddononero:textures/samidare_spear.png");
/*     */     protected final EntitySpear.ModelSpear model;
/*     */     
/*     */     public Renderer(RenderManager renderManagerIn) {
/* 189 */       super(renderManagerIn);
/* 190 */       this.model = new EntitySpear.ModelSpear();
/* 191 */       this.field_76989_e = 0.1F;
/*     */     }
/*     */ 
/*     */     
/*     */     public void doRender(T entity, double x, double y, double z, float entityYaw, float pt) {
/* 196 */       GlStateManager.func_179094_E();
/* 197 */       func_180548_c((Entity)entity);
/* 198 */       float scale = entity.getEntityScale();
/* 199 */       GlStateManager.func_179137_b(x, y, z);
/* 200 */       GlStateManager.func_179114_b(-((EntitySpear.Base)entity).field_70126_B - (((EntitySpear.Base)entity).field_70177_z - ((EntitySpear.Base)entity).field_70126_B) * pt, 0.0F, 1.0F, 0.0F);
/* 201 */       GlStateManager.func_179114_b(((EntitySpear.Base)entity).field_70127_C + (((EntitySpear.Base)entity).field_70125_A - ((EntitySpear.Base)entity).field_70127_C) * pt - 180.0F, 1.0F, 0.0F, 0.0F);
/* 202 */       GlStateManager.func_179152_a(scale, scale, scale);
/* 203 */       int color = entity.getColor();
/* 204 */       float alpha = (color >> 24 & 0xFF) / 255.0F;
/* 205 */       float red = (color >> 16 & 0xFF) / 255.0F;
/* 206 */       float green = (color >> 8 & 0xFF) / 255.0F;
/* 207 */       float blue = (color & 0xFF) / 255.0F;
/* 208 */       if (alpha < 1.0F) {
/* 209 */         GlStateManager.func_179129_p();
/* 210 */         GlStateManager.func_179141_d();
/* 211 */         GlStateManager.func_179147_l();
/* 212 */         GlStateManager.func_187401_a(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
/* 213 */         GlStateManager.func_179140_f();
/*     */       } 
/* 215 */       GlStateManager.func_179131_c(red, green, blue, alpha);
/* 216 */       this.model.func_78088_a((Entity)entity, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0625F);
/* 217 */       GlStateManager.func_179131_c(1.0F, 1.0F, 1.0F, 1.0F);
/* 218 */       if (alpha < 1.0F) {
/* 219 */         GlStateManager.func_179145_e();
/* 220 */         GlStateManager.func_179084_k();
/* 221 */         GlStateManager.func_179118_c();
/* 222 */         GlStateManager.func_179089_o();
/*     */       } 
/* 224 */       GlStateManager.func_179121_F();
/*     */     }
/*     */ 
/*     */     
/*     */     protected ResourceLocation getEntityTexture(T entity) {
/* 229 */       return TEXTURE;
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class ModelSpear
/*     */     extends ModelBase
/*     */   {
/*     */     private final ModelRenderer bb_main;
/*     */     private final ModelRenderer cube_r1;
/*     */     private final ModelRenderer cube_r2;
/*     */     private final ModelRenderer cube_r3;
/*     */     private final ModelRenderer cube_r4;
/*     */     private final ModelRenderer cube_r5;
/*     */     private final ModelRenderer cube_r6;
/*     */     private final ModelRenderer cube_r7;
/*     */     private final ModelRenderer cube_r8;
/*     */     
/*     */     public ModelSpear() {
/* 249 */       this.field_78090_t = 8;
/* 250 */       this.field_78089_u = 8;
/*     */       
/* 252 */       this.bb_main = new ModelRenderer(this);
/* 253 */       this.bb_main.func_78793_a(0.0F, 24.0F, 0.0F);
/* 254 */       this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 3, -1.0F, -43.0F, -1.0F, 1, 3, 1, 0.3F, false));
/* 255 */       this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 3, -1.0F, -37.0F, -1.0F, 1, 4, 1, 0.3F, false));
/* 256 */       this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 3, -1.0F, -47.0F, -1.0F, 1, 4, 1, 0.3F, false));
/* 257 */       this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 3, -1.0F, -40.0F, -1.0F, 1, 3, 1, 0.3F, false));
/*     */       
/* 259 */       this.cube_r1 = new ModelRenderer(this);
/* 260 */       this.cube_r1.func_78793_a(-0.4414F, -31.7471F, -0.5414F);
/* 261 */       this.bb_main.func_78792_a(this.cube_r1);
/* 262 */       setRotationAngle(this.cube_r1, 0.0F, 0.0F, -2.8362F);
/* 263 */       this.cube_r1.field_78804_l.add(new ModelBox(this.cube_r1, 0, 0, -0.1387F, -1.1686F, -0.4586F, 1, 2, 1, 0.3F, true));
/*     */       
/* 265 */       this.cube_r2 = new ModelRenderer(this);
/* 266 */       this.cube_r2.func_78793_a(-0.4414F, -31.7471F, -0.5414F);
/* 267 */       this.bb_main.func_78792_a(this.cube_r2);
/* 268 */       setRotationAngle(this.cube_r2, 0.0F, 0.0F, 2.7489F);
/* 269 */       this.cube_r2.field_78804_l.add(new ModelBox(this.cube_r2, 0, 0, -0.8851F, -1.103F, -0.4586F, 1, 2, 1, 0.3F, false));
/*     */       
/* 271 */       this.cube_r3 = new ModelRenderer(this);
/* 272 */       this.cube_r3.func_78793_a(-0.4414F, -31.7471F, -0.5414F);
/* 273 */       this.bb_main.func_78792_a(this.cube_r3);
/* 274 */       setRotationAngle(this.cube_r3, -1.5708F, -1.2654F, -1.5708F);
/* 275 */       this.cube_r3.field_78804_l.add(new ModelBox(this.cube_r3, 4, 0, -0.1387F, -1.1686F, -0.5414F, 1, 2, 1, 0.3F, true));
/*     */       
/* 277 */       this.cube_r4 = new ModelRenderer(this);
/* 278 */       this.cube_r4.func_78793_a(-0.4414F, -31.7471F, -0.5414F);
/* 279 */       this.bb_main.func_78792_a(this.cube_r4);
/* 280 */       setRotationAngle(this.cube_r4, 1.5708F, -1.1781F, 1.5708F);
/* 281 */       this.cube_r4.field_78804_l.add(new ModelBox(this.cube_r4, 4, 0, -0.8851F, -1.103F, -0.5414F, 1, 2, 1, 0.3F, false));
/*     */       
/* 283 */       this.cube_r5 = new ModelRenderer(this);
/* 284 */       this.cube_r5.func_78793_a(-0.5F, -48.1949F, -0.9366F);
/* 285 */       this.bb_main.func_78792_a(this.cube_r5);
/* 286 */       setRotationAngle(this.cube_r5, 1.5708F, -1.1781F, -1.5708F);
/* 287 */       this.cube_r5.field_78804_l.add(new ModelBox(this.cube_r5, 4, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, false));
/*     */       
/* 289 */       this.cube_r6 = new ModelRenderer(this);
/* 290 */       this.cube_r6.func_78793_a(-0.5F, -48.2993F, -0.1461F);
/* 291 */       this.bb_main.func_78792_a(this.cube_r6);
/* 292 */       setRotationAngle(this.cube_r6, -1.5708F, -1.2654F, 1.5708F);
/* 293 */       this.cube_r6.field_78804_l.add(new ModelBox(this.cube_r6, 4, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, true));
/*     */       
/* 295 */       this.cube_r7 = new ModelRenderer(this);
/* 296 */       this.cube_r7.func_78793_a(-0.4747F, -48.3862F, -0.5F);
/* 297 */       this.bb_main.func_78792_a(this.cube_r7);
/* 298 */       setRotationAngle(this.cube_r7, 0.0F, 0.0F, -0.3927F);
/* 299 */       this.cube_r7.field_78804_l.add(new ModelBox(this.cube_r7, 0, 0, -1.0F, -1.0F, -0.5F, 1, 2, 1, 0.3F, false));
/*     */       
/* 301 */       this.cube_r8 = new ModelRenderer(this);
/* 302 */       this.cube_r8.func_78793_a(-0.1461F, -48.2993F, -0.5F);
/* 303 */       this.bb_main.func_78792_a(this.cube_r8);
/* 304 */       setRotationAngle(this.cube_r8, 0.0F, 0.0F, 0.3054F);
/* 305 */       this.cube_r8.field_78804_l.add(new ModelBox(this.cube_r8, 0, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, true));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 310 */       this.bb_main.func_78785_a(f5);
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 314 */       modelRenderer.field_78795_f = x;
/* 315 */       modelRenderer.field_78796_g = y;
/* 316 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntitySpear.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */