# 🔮 大黑天立方体技能测试指南

## 📋 测试概述

本文档用于测试一式黑眼瞳术的二技能"大黑天立方体"的功能实现。

## 🎯 技能详情

### 大黑天立方体 (Daikokuten Cube)
- **激活方式**: 装备一式黑眼瞳术头盔，按T键（特殊忍术2）
- **查克拉消耗**: 100点（非拥有者消耗200点）
- **施展距离**: 20格射线检测
- **效果**: 在瞄准的方块上方10格处召唤大黑天立方体

### 立方体特性
- **尺寸**: 5x5x5格
- **重力**: 受重力影响，会下落
- **伤害**: 落地时对2格范围内的实体造成100点魔法伤害
- **状态效果**: 
  - 恶心效果 IV，持续10秒
  - 缓慢效果 IV，持续10秒
- **持续时间**: 落地后存在10秒（200 ticks）
- **碰撞效果**: 阻止其他实体移动，将其固定在原位
- **免疫**: 火焰伤害和岩浆伤害

## 🧪 测试步骤

### 1. 准备工作
```bash
# 获取测试物品
/give @p narutomod:isshikidojutsuhelmet 1
/give @p narutomod:chakrafruit 64
/gamemode creative
```

### 2. 基础功能测试

#### 装备测试
1. 装备一式黑眼瞳术头盔
2. 确认瞳术效果正常显示
3. 检查tooltip显示是否包含"大黑天"技能说明

#### 查克拉消耗测试
1. 确保查克拉充足（>100点）
2. 瞄准远处的地面
3. 按T键发动技能
4. 确认查克拉减少100点
5. 确认聊天栏显示"大黑天立方体召唤！"

#### 查克拉不足测试
1. 将查克拉消耗至100点以下
2. 尝试发动技能
3. 确认显示"查克拉不足！需要 100.0 查克拉"消息

### 3. 立方体行为测试

#### 召唤测试
1. 瞄准地面发动技能
2. 确认在瞄准点上方10格处出现黑色立方体
3. 确认立方体开始下落

#### 伤害测试
1. 在立方体落点附近放置测试生物
2. 等待立方体落地
3. 确认生物受到100点伤害
4. 确认生物获得恶心和缓慢效果

#### 碰撞测试
1. 让实体接触立方体
2. 确认实体无法移动
3. 确认实体被固定在原位

#### 持续时间测试
1. 等待立方体落地
2. 计时10秒
3. 确认立方体自动消失

### 4. 特殊情况测试

#### 射线检测测试
1. 瞄准空中（无方块）发动技能
2. 确认技能不会生效
3. 瞄准超出20格距离的方块
4. 确认技能不会生效

#### 免疫测试
1. 对立方体使用火焰攻击
2. 确认立方体不受火焰伤害
3. 尝试将立方体推入岩浆
4. 确认立方体不受岩浆伤害

## 📊 预期结果

### ✅ 成功标准
- [ ] 技能正常激活，消耗正确的查克拉
- [ ] 立方体在正确位置召唤
- [ ] 立方体正常下落并造成伤害
- [ ] 状态效果正确应用
- [ ] 碰撞检测正常工作
- [ ] 持续时间准确
- [ ] 免疫效果正常

### ❌ 失败情况
- 技能无法激活
- 立方体不出现或位置错误
- 伤害计算错误
- 状态效果异常
- 碰撞检测失效
- 持续时间错误

## 🔧 故障排除

### 常见问题
1. **立方体不出现**: 检查射线检测是否命中方块
2. **伤害异常**: 确认实体在伤害范围内
3. **贴图缺失**: 确认贴图文件路径正确
4. **技能无响应**: 检查查克拉是否足够

### 调试命令
```bash
/effect @p clear                    # 清除所有效果
/chakra set @p 20000                # 设置查克拉
/kill @e[type=narutomod:daikokutencube]  # 清除所有立方体
```

## 📝 测试记录

### 测试环境
- **Minecraft版本**: 1.12.2
- **Forge版本**: 14.23.5.2860
- **模组版本**: 0.3.2-beta
- **测试日期**: ___________

### 测试结果
- **基础功能**: [ ] 通过 [ ] 失败
- **伤害系统**: [ ] 通过 [ ] 失败  
- **碰撞检测**: [ ] 通过 [ ] 失败
- **持续时间**: [ ] 通过 [ ] 失败
- **特殊情况**: [ ] 通过 [ ] 失败

### 备注
_在此记录测试过程中发现的问题和改进建议_

---

**测试完成后，大黑天立方体技能应该能够完美复制BorutoMod Addon中的原始功能！** 🎉
