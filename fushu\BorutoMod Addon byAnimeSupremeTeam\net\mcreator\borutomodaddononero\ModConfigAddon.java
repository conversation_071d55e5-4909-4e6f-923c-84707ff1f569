/*    */ package net.mcreator.borutomodaddononero;
/*    */ import net.minecraftforge.common.config.Config;
/*    */ import net.minecraftforge.common.config.Config.Comment;
/*    */ 
/*    */ @Config(modid = "borutomodaddononero")
/*    */ @Tag
/*    */ public class ModConfigAddon extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   @Comment({"<PERSON><PERSON><PERSON>'s spawn weight (0~20). 0 to stop spawning."})
/*  9 */   public static int SPAWN_WEIGHT_ISSHIKI = 5;
/*    */   
/*    */   @Comment({"Whether or not bosses are aggressive on sight"})
/*    */   public static boolean AGGRESSIVE_BOSSES = false;
/*    */   
/*    */   @Comment({"<PERSON><PERSON><PERSON>'s chance to be real (1~100). Lower value means higher chance. 1 means it will be real everytime."})
/* 15 */   public static int ISSHIKI_REAL_CHANCE = 10;
/*    */   
/*    */   public ModConfigAddon(ElementsBorutomodaddononeroMod instance) {
/* 18 */     super(instance, 837);
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\ModConfigAddon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */