# 🔥 一式黑眼技能集成测试指南

## 📋 测试概述

本文档提供了完整的一式黑眼技能测试指南，包括从BorutoMod Addon完整复制的三个核心技能。

---

## 🎯 集成内容总结

### ✅ 已完成的集成

1. **少名毘古那技能 (R键)**
   - 完整复制了BorutoMod的缩小能力实现
   - 包含EntitySukunahikona实体类
   - 支持玩家缩小和特殊移动

2. **大黑天立方体技能 (T键)**
   - 完整复制了空间储存技能
   - 包含EntityDaikokutenCube实体类
   - 立方体下落攻击和持续伤害

3. **雨五月长矛技能 (Y键)**
   - 完整复制了长矛攻击技能
   - 包含EntitySamidareSpear实体类
   - 多重长矛投射攻击

4. **资源文件**
   - ✅ 贴图文件：isshiki_dojutsu_enhanced.png
   - ✅ 实体贴图：daikokuten_cube.png, samidare_spear.png
   - ✅ 音效文件：sukunahikona_isshiki.ogg, samidare_spear_isshiki.ogg
   - ✅ 语言文件：英文本地化条目

5. **技能参数 (与BorutoMod保持一致)**
   - 少名毘古那：450查克拉消耗
   - 大黑天立方体：250查克拉消耗
   - 雨五月长矛：150查克拉消耗

---

## 🧪 测试准备

### 获取测试物品
```bash
# 给予一式黑眼
/give @p narutomod:isshikidojutsuhelmet 1

# 给予查克拉果实（用于恢复查克拉）
/give @p narutomod:chakrafruit 64

# 切换到创造模式（可选）
/gamemode creative
```

### 基础检查
1. **装备检查**：装备一式黑眼后应该看到瞳术效果
2. **飞行能力**：装备后应该获得飞行能力
3. **查克拉消耗**：每秒消耗2点疲劳值

---

## 🔥 技能测试

### 1. 少名毘古那技能测试 (R键)

#### 基础功能测试
1. **激活测试**
   - 按R键激活少名毘古那
   - 应该听到sukunahikona_isshiki音效
   - 玩家应该被缩小并获得特殊移动能力

2. **移动测试**
   - 缩小状态下测试移动
   - 测试跳跃功能
   - 验证碰撞检测

3. **解除测试**
   - 再次按R键解除少名毘古那
   - 玩家应该恢复正常大小
   - 根据使用时间消耗饥饿值

#### 查克拉消耗验证
- 确认消耗450点查克拉
- 查克拉不足时无法激活

### 2. 大黑天立方体技能测试 (T键)

#### 基础功能测试
1. **召唤测试**
   - 瞄准地面按T键
   - 应该在目标位置上方10格生成立方体
   - 立方体应该开始下落

2. **攻击测试**
   - 让立方体砸中生物
   - 验证10点伤害
   - 验证缓慢效果（2级，5秒）

3. **持续时间测试**
   - 立方体着地后应该持续10秒
   - 10秒后自动消失

#### 查克拉消耗验证
- 确认消耗250点查克拉
- 查克拉不足时无法激活

### 3. 雨五月长矛技能测试 (Y键)

#### 基础功能测试
1. **发射测试**
   - 按Y键发射长矛
   - 应该听到samidare_spear_isshiki音效
   - 应该发射多根长矛（基于威力）

2. **攻击测试**
   - 让长矛击中目标
   - 验证10点伤害
   - 验证恶心效果（1级，10秒）

3. **视觉效果测试**
   - 长矛应该有随机旋转
   - 长矛应该有正确的贴图

#### 查克拉消耗验证
- 确认消耗150点查克拉
- 查克拉不足时无法激活

---

## 🎮 综合测试场景

### 场景1：连续技能测试
1. 装备一式黑眼
2. 依次使用R、T、Y技能
3. 验证所有技能都能正常工作
4. 检查查克拉消耗是否正确

### 场景2：战斗测试
1. 生成一些敌对生物
2. 使用大黑天立方体攻击
3. 使用雨五月长矛攻击
4. 使用少名毘古那躲避攻击

### 场景3：资源消耗测试
1. 在查克拉较低时测试技能
2. 验证查克拉不足的提示
3. 测试疲劳值消耗

---

## 🔍 故障排除

### 常见问题

1. **技能无法激活**
   - 检查是否装备了一式黑眼
   - 检查查克拉是否足够
   - 确认不在冷却时间内

2. **音效无法播放**
   - 检查音效文件是否正确复制
   - 确认sounds.json配置正确

3. **实体无法生成**
   - 检查实体类是否正确注册
   - 确认没有编译错误

4. **贴图显示异常**
   - 检查贴图文件路径
   - 确认贴图文件格式正确

### 调试命令
```bash
# 检查玩家数据
/data get entity @p

# 清除所有效果
/effect @p clear

# 恢复查克拉
/give @p narutomod:chakrafruit 64

# 重置游戏模式
/gamemode survival
```

---

## 📊 测试结果记录

### 功能完整性检查表
- [ ] 少名毘古那技能正常工作
- [ ] 大黑天立方体技能正常工作
- [ ] 雨五月长矛技能正常工作
- [ ] 所有音效正常播放
- [ ] 所有贴图正确显示
- [ ] 查克拉消耗正确
- [ ] 飞行能力正常
- [ ] 疲劳值消耗正常

### 性能检查表
- [ ] 无明显卡顿
- [ ] 无内存泄漏
- [ ] 无异常错误日志
- [ ] 多人游戏兼容

---

## 🎉 集成成功标准

当以下所有条件都满足时，可以认为集成成功：

1. ✅ **所有三个技能都能正常激活和使用**
2. ✅ **查克拉消耗与BorutoMod保持一致**
3. ✅ **所有音效和贴图正确显示**
4. ✅ **实体行为与原版BorutoMod一致**
5. ✅ **无编译错误和运行时异常**
6. ✅ **与现有系统良好集成**

---

## 📝 后续改进建议

1. **平衡性调整**：根据测试反馈调整查克拉消耗和伤害值
2. **视觉效果增强**：添加更多粒子效果和动画
3. **音效优化**：调整音效音量和播放时机
4. **性能优化**：优化实体更新逻辑，减少服务器负载

---

**测试完成时间**: ___________  
**测试人员**: ___________  
**测试结果**: ___________  
**发现问题**: ___________
