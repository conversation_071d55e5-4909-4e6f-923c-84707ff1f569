/*    */ package net.mcreator.borutomodaddononero.creativetab;
/*    */ 
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemNueIconTab;
/*    */ import net.minecraft.creativetab.CreativeTabs;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ @Tag
/*    */ public class TabBorutoExpansion extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public static CreativeTabs tab;
/*    */   
/*    */   public TabBorutoExpansion(ElementsBorutomodaddononeroMod instance) {
/* 16 */     super(instance, 1);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 21 */     tab = new CreativeTabs("tabboruto_expansion")
/*    */       {
/*    */         @SideOnly(Side.CLIENT)
/*    */         public ItemStack func_78016_d() {
/* 25 */           return new ItemStack(ItemNueIconTab.block, 1);
/*    */         }
/*    */         
/*    */         @SideOnly(Side.CLIENT)
/*    */         public boolean hasSearchBar() {
/* 30 */           return false;
/*    */         }
/*    */       };
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\creativetab\TabBorutoExpansion.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */