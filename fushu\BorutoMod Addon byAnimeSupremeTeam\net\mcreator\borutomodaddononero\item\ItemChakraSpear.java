/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import com.google.common.collect.Multimap;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.minecraft.block.state.IBlockState;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.entity.SharedMonsterAttributes;
/*    */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.util.math.BlockPos;
/*    */ import net.minecraft.world.World;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ 
/*    */ @Tag
/*    */ public class ItemChakraSpear
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   @ObjectHolder("borutomodaddononero:chakra_spear")
/* 29 */   public static final Item block = null;
/*    */   public ItemChakraSpear(ElementsBorutomodaddononeroMod instance) {
/* 31 */     super(instance, 17);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 36 */     this.elements.items.add(() -> ((Item)(new ItemToolCustom() {
/*    */         
/*    */         }).func_77655_b("chakra_spear").setRegistryName("chakra_spear")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 43 */     ModelLoader.setCustomModelResourceLocation(block, 0, new ModelResourceLocation("borutomodaddononero:chakra_spear", "inventory"));
/*    */   }
/*    */   
/*    */   private static class ItemToolCustom extends Item { protected ItemToolCustom() {
/* 47 */       func_77656_e(15);
/* 48 */       func_77625_d(1);
/*    */     }
/*    */ 
/*    */     
/*    */     public Multimap<String, AttributeModifier> func_111205_h(EntityEquipmentSlot equipmentSlot) {
/* 53 */       Multimap<String, AttributeModifier> multimap = super.func_111205_h(equipmentSlot);
/* 54 */       if (equipmentSlot == EntityEquipmentSlot.MAINHAND) {
/* 55 */         multimap.put(SharedMonsterAttributes.field_111264_e.func_111108_a(), new AttributeModifier(field_111210_e, "Tool modifier", 11.0D, 0));
/* 56 */         multimap.put(SharedMonsterAttributes.field_188790_f.func_111108_a(), new AttributeModifier(field_185050_h, "Tool modifier", -2.4D, 0));
/*    */       } 
/* 58 */       return multimap;
/*    */     }
/*    */ 
/*    */ 
/*    */     
/*    */     public float func_150893_a(ItemStack par1ItemStack, IBlockState par2Block) {
/* 64 */       return 0.0F;
/*    */     }
/*    */ 
/*    */     
/*    */     public boolean func_179218_a(ItemStack stack, World worldIn, IBlockState state, BlockPos pos, EntityLivingBase entityLiving) {
/* 69 */       stack.func_77972_a(1, entityLiving);
/* 70 */       return true;
/*    */     }
/*    */ 
/*    */     
/*    */     public boolean func_77644_a(ItemStack stack, EntityLivingBase target, EntityLivingBase attacker) {
/* 75 */       stack.func_77972_a(2, attacker);
/* 76 */       return true;
/*    */     }
/*    */ 
/*    */     
/*    */     public boolean func_77662_d() {
/* 81 */       return true;
/*    */     }
/*    */ 
/*    */     
/*    */     public int func_77619_b() {
/* 86 */       return 0;
/*    */     } }
/*    */ 
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemChakraSpear.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */