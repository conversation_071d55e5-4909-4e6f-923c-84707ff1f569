package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntityDaikokutenCube;
import net.narutomod.Chakra;
import net.narutomod.item.ItemIsshikiDojutsu;

import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureDaikokutenCube extends ElementsNarutomodMod.ModElement {
    
    public ProcedureDaikokutenCube(ElementsNarutomodMod instance) {
        super(instance, 605);
    }
    
    public static void executeProcedure(Map<String, Object> dependencies) {
        if (dependencies.get("is_pressed") == null) {
            System.err.println("Failed to load dependency is_pressed for procedure DaikokutenCube!");
            return;
        }
        if (dependencies.get("entity") == null) {
            System.err.println("Failed to load dependency entity for procedure DaikokutenCube!");
            return;
        }
        if (dependencies.get("world") == null) {
            System.err.println("Failed to load dependency world for procedure DaikokutenCube!");
            return;
        }
        
        boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
        Entity entity = (Entity)dependencies.get("entity");
        World world = (World)dependencies.get("world");
        
        if (!is_pressed) {
            return;
        }
        
        if (!(entity instanceof EntityPlayer)) {
            return;
        }
        
        EntityPlayer player = (EntityPlayer)entity;
        
        // 检查查克拉是否足够
        double requiredChakra = ItemIsshikiDojutsu.getDaikokutenChakraUsage(player);
        if (Chakra.pathway(player).getAmount() >= requiredChakra) {
            // 消耗查克拉
            Chakra.pathway(player).consume(requiredChakra);
            
            // 获取玩家视线方向
            Vec3d look = player.getLookVec();
            Vec3d start = player.getPositionEyes(1.0F);
            Vec3d end = start.add(look.scale(20.0D));
            
            // 进行射线追踪
            RayTraceResult result = player.world.rayTraceBlocks(start, end);
            
            if (result != null && result.typeOfHit == RayTraceResult.Type.BLOCK) {
                int x = result.getBlockPos().getX();
                int y = result.getBlockPos().getY();
                int z = result.getBlockPos().getZ();
                
                // 在目标位置上方10格创建大黑天立方体
                EntityDaikokutenCube.DaikokutenCube cubeEntity = new EntityDaikokutenCube.DaikokutenCube(world, x, y + 10, z);
                cubeEntity.setOwner(player);
                world.spawnEntity(cubeEntity);
            }
        }
    }
}
