package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntityDaikokutenCube;
import net.narutomod.Chakra;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureDaikokutenCube extends ElementsNarutomodMod.ModElement {
	public ProcedureDaikokutenCube(ElementsNarutomodMod instance) {
		super(instance, 663);
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure DaikokutenCube!");
			return;
		}
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure Daikoku<PERSON>Cube!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure Daikoku<PERSON>Cube!");
			return;
		}

		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");

		if (is_pressed) {
			if (!(entity instanceof EntityPlayer)) {
				return;
			}

			EntityPlayer player = (EntityPlayer) entity;

			if (Chakra.pathway(player).getAmount() >= 250.0D) {
				// 服务器端执行
				if (!world.isRemote) {
					Chakra.pathway(player).consume(250.0D);

					Vec3d look = player.getLookVec();
					Vec3d start = player.getPositionEyes(1.0F);
					Vec3d end = start.add(look.scale(20.0D));
					RayTraceResult result = world.rayTraceBlocks(start, end);

					if (result != null && result.typeOfHit == RayTraceResult.Type.BLOCK) {
						int x = result.getBlockPos().getX();
						int y = result.getBlockPos().getY();
						int z = result.getBlockPos().getZ();

						try {
							EntityDaikokutenCube.EntityCustom cubeEntity = new EntityDaikokutenCube.EntityCustom(world, x, (y + 10), z);
							cubeEntity.setOwner(player);
							world.spawnEntity(cubeEntity);
						} catch (Exception e) {
							System.err.println("Failed to spawn Daikokuten Cube: " + e.getMessage());
						}
					}
				}
			}
		}
	}
}
