package net.narutomod.procedure;

import java.util.Map;
import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntityDaikokutenCube;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.narutomod.Chakra;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureDaikokutenCube extends ElementsNarutomodMod.ModElement {
    public ProcedureDaikokutenCube(ElementsNarutomodMod instance) {
        super(instance, 264);
    }
    
    public static void executeProcedure(Map<String, Object> dependencies) {
        if (dependencies.get("is_pressed") == null) {
            System.err.println("Failed to load dependency is_pressed for procedure DaikokutenCube!");
            return;
        }
        if (dependencies.get("entity") == null) {
            System.err.println("Failed to load dependency entity for procedure Daikoku<PERSON>Cube!");
            return;
        }
        if (dependencies.get("world") == null) {
            System.err.println("Failed to load dependency world for procedure Daikoku<PERSON>Cube!");
            return;
        }
        boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
        Entity entity = (Entity)dependencies.get("entity");
        World world = (World)dependencies.get("world");
        
        if (is_pressed) {
            if (!(entity instanceof EntityPlayer)) {
                return;
            }
            if (Chakra.pathway((EntityPlayer)entity).getAmount() >= 250.0D) {
                Chakra.pathway((EntityPlayer)entity).consume(250.0D);
                EntityPlayer player = (EntityPlayer)entity;
                Vec3d look = player.getLookVec();
                Vec3d start = player.getPositionEyes(1.0F);
                Vec3d end = start.add(look.scale(20.0D));
                RayTraceResult result = player.world.rayTraceBlocks(start, end);
                if (result != null && result.typeOfHit == RayTraceResult.Type.BLOCK) {
                    int x = result.getBlockPos().getX();
                    int y = result.getBlockPos().getY();
                    int z = result.getBlockPos().getZ();
                    EntityDaikokutenCube.DaikokutenCube cubeEntity = new EntityDaikokutenCube.DaikokutenCube(world, x, (y + 10), z);
                    world.spawnEntity(cubeEntity);
                }
            }
        }
    }
}
