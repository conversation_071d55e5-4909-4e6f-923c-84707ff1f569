/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.List;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.entity.EntitySpear;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.potion.PotionEffect;
/*    */ import net.minecraft.util.DamageSource;
/*    */ import net.minecraft.util.EnumFacing;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundCategory;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraft.util.math.AxisAlignedBB;
/*    */ import net.minecraft.util.math.BlockPos;
/*    */ import net.minecraft.util.math.RayTraceResult;
/*    */ import net.minecraft.util.math.Vec3d;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.Chakra;
/*    */ import net.narutomod.potion.PotionParalysis;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSpear
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSpear(ElementsBorutomodaddononeroMod instance) {
/* 29 */     super(instance, 265);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 33 */     if (dependencies.get("is_pressed") == null) {
/* 34 */       System.err.println("Failed to load dependency is_pressed for procedure Spear!");
/*    */       return;
/*    */     } 
/* 37 */     if (dependencies.get("entity") == null) {
/* 38 */       System.err.println("Failed to load dependency entity for procedure Spear!");
/*    */       return;
/*    */     } 
/* 41 */     if (dependencies.get("world") == null) {
/* 42 */       System.err.println("Failed to load dependency world for procedure Spear!");
/*    */       
/*    */       return;
/*    */     } 
/* 46 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 47 */     Entity entity = (Entity)dependencies.get("entity");
/* 48 */     World world = (World)dependencies.get("world");
/*    */     
/* 50 */     if (is_pressed && entity instanceof EntityPlayer) {
/* 51 */       EntityPlayer player = (EntityPlayer)entity;
/* 52 */       if (Chakra.pathway(player).getAmount() >= 150.0D) {
/* 53 */         Chakra.pathway(player).consume(150.0D);
/* 54 */         world.func_184148_a((EntityPlayer)null, entity.field_70165_t, entity.field_70163_u, entity.field_70161_v, (SoundEvent)SoundEvent.field_187505_a
/*    */             
/* 56 */             .func_82594_a(new ResourceLocation("borutomodaddononero:samidare_spear")), SoundCategory.NEUTRAL, 50.0F, 1.0F);
/*    */ 
/*    */         
/* 59 */         Vec3d vec3d = player.func_174824_e(1.0F);
/* 60 */         Vec3d vec3d2 = vec3d.func_178787_e(player.func_70040_Z().func_186678_a(30.0D));
/* 61 */         RayTraceResult res = world.func_147447_a(vec3d, vec3d2, false, true, true);
/* 62 */         if (res != null && res.field_72313_a == RayTraceResult.Type.BLOCK && res.field_178784_b == EnumFacing.UP) {
/* 63 */           BlockPos hitPos = new BlockPos(res.field_72307_f);
/*    */           
/* 65 */           for (int i = 0; i < 5; i++) {
/* 66 */             EntitySpear.Base spear = new EntitySpear.Base(world);
/* 67 */             Vec3d vec = res.field_72307_f.func_72441_c((player.func_70681_au().nextDouble() - 0.5D) * 3.0D, 0.0D, (player.func_70681_au().nextDouble() - 0.5D) * 3.0D);
/*    */             
/* 69 */             for (; !world.func_180495_p(new BlockPos(vec)).func_185896_q(); vec = vec.func_178786_a(0.0D, 1.0D, 0.0D));
/* 70 */             for (; world.func_180495_p((new BlockPos(vec)).func_177984_a()).func_185896_q(); vec = vec.func_72441_c(0.0D, 1.0D, 0.0D));
/*    */             
/* 72 */             spear.func_70012_b(vec.field_72450_a, vec.field_72448_b + 0.5D, vec.field_72449_c, player.func_70681_au().nextFloat() * 360.0F, (player.func_70681_au().nextFloat() - 0.5F) * 60.0F);
/* 73 */             world.func_72838_d((Entity)spear);
/*    */           } 
/*    */           
/* 76 */           double radius = 1.7D;
/* 77 */           List<Entity> entitiesInArea = world.func_72872_a(Entity.class, new AxisAlignedBB(hitPos
/* 78 */                 .func_177963_a(-radius, -radius, -radius), hitPos
/* 79 */                 .func_177963_a(radius, radius, radius)));
/*    */           
/* 81 */           for (Entity target : entitiesInArea) {
/* 82 */             if (target instanceof EntityLivingBase) {
/* 83 */               EntityLivingBase livingTarget = (EntityLivingBase)target;
/* 84 */               livingTarget.func_70097_a(DamageSource.func_76365_a(player), 10.0F);
/* 85 */               livingTarget.func_70690_d(new PotionEffect(PotionParalysis.potion, 300, 1, false, false));
/*    */             } 
/*    */           } 
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSpear.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */