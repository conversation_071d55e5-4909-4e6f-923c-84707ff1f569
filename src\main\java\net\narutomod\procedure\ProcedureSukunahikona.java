package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntitySukunahikona;
import net.narutomod.Chakra;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSukunahikona extends ElementsNarutomodMod.ModElement {
	public ProcedureSukunahikona(ElementsNarutomodMod instance) {
		super(instance, 662);
	}



	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure Sukunahikona!");
			return;
		}
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure Sukunahikona!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure <PERSON><PERSON><PERSON><PERSON>na!");
			return;
		}

		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");

		if (is_pressed) {
			if (!(entity.getRidingEntity() instanceof EntitySukunahikona.EntityCustom) &&
				Chakra.pathway((EntityPlayer) entity).getAmount() >= 450.0D) {
				Chakra.pathway((EntityPlayer) entity).consume(450.0D);
				SoundEvent soundEvent = SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:sukunahikona"));
				if (soundEvent != null) {
					world.playSound(null, entity.posX, entity.posY, entity.posZ, soundEvent, SoundCategory.NEUTRAL, 3.0F, 1.0F);
				}

				EntitySukunahikona.EntityCustom entitySmallerMe = new EntitySukunahikona.EntityCustom((EntityLivingBase) entity, 0.1F);
				entitySmallerMe.setPosition(entity.posX, entity.posY, entity.posZ);
				entity.world.spawnEntity(entitySmallerMe);
			}
		} else {
			Entity ridingEntity = entity.getRidingEntity();
			if (ridingEntity instanceof EntitySukunahikona.EntityCustom) {
				double ticksExisted = ridingEntity.ticksExisted;
				entity.dismountRidingEntity();
				ridingEntity.setDead();

				if (entity instanceof EntityPlayer) {
					((EntityPlayer) entity).getFoodStats().setFoodLevel(
						((EntityPlayer) entity).getFoodStats().getFoodLevel() - (int) (ticksExisted / 60.0D + 1.0D));
				}
			}
		}
	}
}
