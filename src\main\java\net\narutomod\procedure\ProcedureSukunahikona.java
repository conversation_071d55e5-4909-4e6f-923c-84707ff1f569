package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntitySukunahikona;
import net.narutomod.Chakra;
import net.narutomod.item.ItemIsshikiDojutsu;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSukunahikona extends ElementsNarutomodMod.ModElement {
    
    public ProcedureSukunahikona(ElementsNarutomodMod instance) {
        super(instance, 603);
    }
    
    public static void executeProcedure(Map<String, Object> dependencies) {
        if (dependencies.get("is_pressed") == null) {
            System.err.println("Failed to load dependency is_pressed for procedure <PERSON><PERSON>nahiko<PERSON>!");
            return;
        }
        if (dependencies.get("entity") == null) {
            System.err.println("Failed to load dependency entity for procedure Sukunahikona!");
            return;
        }
        if (dependencies.get("world") == null) {
            System.err.println("Failed to load dependency world for procedure Sukunahikona!");
            return;
        }
        
        boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
        Entity entity = (Entity)dependencies.get("entity");
        World world = (World)dependencies.get("world");
        
        if (!(entity instanceof EntityPlayer)) {
            return;
        }
        
        EntityPlayer player = (EntityPlayer)entity;
        
        if (is_pressed) {
            // 检查是否已经在使用少名毘古那
            if (!(entity.getRidingEntity() instanceof EntitySukunahikona.Sukunahikona)) {
                // 检查查克拉是否足够
                double requiredChakra = ItemIsshikiDojutsu.getSukunahikonaChakraUsage(player);
                if (Chakra.pathway(player).getAmount() >= requiredChakra) {
                    // 消耗查克拉
                    Chakra.pathway(player).consume(requiredChakra);
                    
                    // 播放音效
                    world.playSound((EntityPlayer)null, entity.posX, entity.posY, entity.posZ, 
                        SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:sukunahikona_isshiki")), 
                        SoundCategory.NEUTRAL, 3.0F, 1.0F);
                    
                    // 创建少名毘古那实体
                    EntitySukunahikona.Sukunahikona entitySmallerMe = new EntitySukunahikona.Sukunahikona((EntityLivingBase)entity, 0.1F);
                    entitySmallerMe.setPosition(entity.posX, entity.posY, entity.posZ);
                    entity.world.spawnEntity(entitySmallerMe);
                }
            }
        } else {
            // 解除少名毘古那状态
            Entity ridingEntity = entity.getRidingEntity();
            if (ridingEntity instanceof EntitySukunahikona.Sukunahikona) {
                double ticksExisted = ridingEntity.ticksExisted;
                entity.dismountRidingEntity();
                ridingEntity.setDead();
                
                // 根据使用时间消耗饥饿值
                if (entity instanceof EntityPlayer) {
                    ((EntityPlayer)entity).getFoodStats().addExhaustion(
                        ((EntityPlayer)entity).getFoodStats().getFoodLevel() - (int)(ticksExisted / 60.0D + 1.0D));
                }
            }
        }
    }
}
