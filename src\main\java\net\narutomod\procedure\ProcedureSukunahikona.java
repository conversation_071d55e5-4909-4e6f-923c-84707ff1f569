package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.Chakra;
import net.minecraft.entity.Entity;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.world.World;

import java.lang.reflect.Method;
import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSukunahikona extends ElementsNarutomodMod.ModElement {
	public ProcedureSukunahikona(ElementsNarutomodMod instance) {
		super(instance, 662);
	}

	// 使用反射调用setSize方法
	private static void setPlayerSize(EntityPlayer player, float width, float height) {
		try {
			Method setSizeMethod = Entity.class.getDeclaredMethod("setSize", float.class, float.class);
			setSizeMethod.setAccessible(true);
			setSizeMethod.invoke(player, width, height);
		} catch (Exception e) {
			System.err.println("Failed to set player size: " + e.getMessage());
		}
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure Sukunahikona!");
			return;
		}
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure Sukunahikona!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure Sukunahikona!");
			return;
		}

		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");

		// 服务器端安全检查
		if (!(entity instanceof EntityPlayer)) {
			return;
		}

		EntityPlayer player = (EntityPlayer) entity;

		if (is_pressed) {
			// 检查是否已经缩小
			boolean isSmall = player.getEntityData().getBoolean("sukunahikona_active");

			if (!isSmall && Chakra.pathway(player).getAmount() >= 450.0D) {
				// 服务器端执行
				if (!world.isRemote) {
					Chakra.pathway(player).consume(450.0D);

					// 播放音效
					try {
						SoundEvent soundEvent = SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:sukunahikona"));
						if (soundEvent != null) {
							world.playSound(null, entity.posX, entity.posY, entity.posZ, soundEvent, SoundCategory.NEUTRAL, 3.0F, 1.0F);
						}
					} catch (Exception e) {
						System.err.println("Failed to play sukunahikona sound: " + e.getMessage());
					}

					// 直接缩小玩家
					setPlayerSize(player, 0.6F * 0.1F, 1.8F * 0.1F);

					// 添加隐身效果
					player.addPotionEffect(new PotionEffect(MobEffects.INVISIBILITY, 999999, 0, false, false));

					// 标记状态
					player.getEntityData().setBoolean("sukunahikona_active", true);
					player.getEntityData().setLong("sukunahikona_start_time", world.getTotalWorldTime());
				}
			}
		} else {
			// 检查是否处于缩小状态
			boolean isSmall = player.getEntityData().getBoolean("sukunahikona_active");

			if (isSmall) {
				// 服务器端执行
				if (!world.isRemote) {
					// 恢复玩家大小
					setPlayerSize(player, 0.6F, 1.8F);

					// 移除隐身效果
					player.removePotionEffect(MobEffects.INVISIBILITY);

					// 计算持续时间并消耗饥饿值
					long startTime = player.getEntityData().getLong("sukunahikona_start_time");
					long duration = world.getTotalWorldTime() - startTime;
					int hungerCost = (int) (duration / 60.0D + 1.0D);

					player.getFoodStats().setFoodLevel(
						Math.max(0, player.getFoodStats().getFoodLevel() - hungerCost));

					// 清除状态
					player.getEntityData().setBoolean("sukunahikona_active", false);
					player.getEntityData().removeTag("sukunahikona_start_time");
				}
			}
		}
	}
}
