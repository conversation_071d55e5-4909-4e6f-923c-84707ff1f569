/*     */ package net.mcreator.borutomodaddononero;
/*     */ 
/*     */ import java.lang.annotation.Retention;
/*     */ import java.lang.annotation.RetentionPolicy;
/*     */ import java.util.ArrayList;
/*     */ import java.util.Collections;
/*     */ import java.util.HashMap;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Random;
/*     */ import java.util.function.Supplier;
/*     */ import net.minecraft.block.Block;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.entity.player.EntityPlayerMP;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.potion.Potion;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraft.world.biome.Biome;
/*     */ import net.minecraft.world.chunk.IChunkProvider;
/*     */ import net.minecraft.world.gen.IChunkGenerator;
/*     */ import net.minecraft.world.storage.WorldSavedData;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.event.RegistryEvent;
/*     */ import net.minecraftforge.fml.common.IFuelHandler;
/*     */ import net.minecraftforge.fml.common.IWorldGenerator;
/*     */ import net.minecraftforge.fml.common.discovery.ASMDataTable;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
/*     */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*     */ import net.minecraftforge.fml.common.gameevent.PlayerEvent;
/*     */ import net.minecraftforge.fml.common.network.IGuiHandler;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ public class ElementsBorutomodaddononeroMod
/*     */   implements IFuelHandler, IWorldGenerator
/*     */ {
/*  49 */   public final List<ModElement> elements = new ArrayList<>();
/*  50 */   public final List<Supplier<Block>> blocks = new ArrayList<>();
/*  51 */   public final List<Supplier<Item>> items = new ArrayList<>();
/*  52 */   public final List<Supplier<Biome>> biomes = new ArrayList<>();
/*  53 */   public final List<Supplier<EntityEntry>> entities = new ArrayList<>();
/*  54 */   public final List<Supplier<Potion>> potions = new ArrayList<>();
/*  55 */   public static Map<ResourceLocation, SoundEvent> sounds = new HashMap<>();
/*     */ 
/*     */ 
/*     */   
/*     */   private int messageID;
/*     */ 
/*     */ 
/*     */   
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*     */     try {
/*  65 */       for (ASMDataTable.ASMData asmData : event.getAsmData().getAll(ModElement.Tag.class.getName())) {
/*  66 */         Class<?> clazz = Class.forName(asmData.getClassName());
/*  67 */         if (clazz.getSuperclass() == ModElement.class)
/*  68 */           this.elements.add(clazz.getConstructor(new Class[] { getClass() }).newInstance(new Object[] { this })); 
/*     */       } 
/*  70 */     } catch (Exception e) {
/*  71 */       e.printStackTrace();
/*     */     } 
/*  73 */     Collections.sort(this.elements);
/*  74 */     this.elements.forEach(ModElement::initElements);
/*  75 */     addNetworkMessage((Class)BorutomodaddononeroModVariables.WorldSavedDataSyncMessageHandler.class, BorutomodaddononeroModVariables.WorldSavedDataSyncMessage.class, new Side[] { Side.SERVER, Side.CLIENT });
/*     */   }
/*     */ 
/*     */   
/*     */   public void registerSounds(RegistryEvent.Register<SoundEvent> event) {
/*  80 */     for (Map.Entry<ResourceLocation, SoundEvent> sound : sounds.entrySet()) {
/*  81 */       event.getRegistry().register(((SoundEvent)sound.getValue()).setRegistryName(sound.getKey()));
/*     */     }
/*     */   }
/*     */   
/*     */   public void generate(Random random, int chunkX, int chunkZ, World world, IChunkGenerator cg, IChunkProvider cp) {
/*  86 */     this.elements.forEach(element -> element.generateWorld(random, chunkX * 16, chunkZ * 16, world, world.field_73011_w.getDimension(), cg, cp));
/*     */   }
/*     */ 
/*     */   
/*     */   public int getBurnTime(ItemStack fuel) {
/*  91 */     for (ModElement element : this.elements) {
/*  92 */       int ret = element.addFuel(fuel);
/*  93 */       if (ret != 0)
/*  94 */         return ret; 
/*     */     } 
/*  96 */     return 0;
/*     */   }
/*     */   
/*     */   @SubscribeEvent
/*     */   public void onPlayerLoggedIn(PlayerEvent.PlayerLoggedInEvent event) {
/* 101 */     if (!event.player.field_70170_p.field_72995_K) {
/* 102 */       WorldSavedData mapdata = BorutomodaddononeroModVariables.MapVariables.get(event.player.field_70170_p);
/* 103 */       WorldSavedData worlddata = BorutomodaddononeroModVariables.WorldVariables.get(event.player.field_70170_p);
/* 104 */       if (mapdata != null) {
/* 105 */         BorutomodaddononeroMod.PACKET_HANDLER.sendTo(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(0, mapdata), (EntityPlayerMP)event.player);
/*     */       }
/* 107 */       if (worlddata != null) {
/* 108 */         BorutomodaddononeroMod.PACKET_HANDLER.sendTo(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(1, worlddata), (EntityPlayerMP)event.player);
/*     */       }
/*     */     } 
/*     */   }
/*     */   
/*     */   @SubscribeEvent
/*     */   public void onPlayerChangedDimension(PlayerEvent.PlayerChangedDimensionEvent event) {
/* 115 */     if (!event.player.field_70170_p.field_72995_K) {
/* 116 */       WorldSavedData worlddata = BorutomodaddononeroModVariables.WorldVariables.get(event.player.field_70170_p);
/* 117 */       if (worlddata != null)
/* 118 */         BorutomodaddononeroMod.PACKET_HANDLER.sendTo(new BorutomodaddononeroModVariables.WorldSavedDataSyncMessage(1, worlddata), (EntityPlayerMP)event.player); 
/*     */     } 
/*     */   }
/*     */   
/* 122 */   public ElementsBorutomodaddononeroMod() { this.messageID = 0;
/*     */     sounds.put(new ResourceLocation("borutomodaddononero", "sukunahikona"), new SoundEvent(new ResourceLocation("borutomodaddononero", "sukunahikona")));
/*     */     sounds.put(new ResourceLocation("borutomodaddononero", "samidare_spear"), new SoundEvent(new ResourceLocation("borutomodaddononero", "samidare_spear"))); } public <T extends net.minecraftforge.fml.common.network.simpleimpl.IMessage, V extends net.minecraftforge.fml.common.network.simpleimpl.IMessage> void addNetworkMessage(Class<? extends IMessageHandler<T, V>> handler, Class<T> messageClass, Side... sides) {
/* 125 */     for (Side side : sides)
/* 126 */       BorutomodaddononeroMod.PACKET_HANDLER.registerMessage(handler, messageClass, this.messageID, side); 
/* 127 */     this.messageID++;
/*     */   }
/*     */   @Retention(RetentionPolicy.RUNTIME)
/*     */   public static @interface Tag {}
/*     */   public static class GuiHandler implements IGuiHandler { public Object getServerGuiElement(int id, EntityPlayer player, World world, int x, int y, int z) {
/* 132 */       return null;
/*     */     }
/*     */ 
/*     */     
/*     */     public Object getClientGuiElement(int id, EntityPlayer player, World world, int x, int y, int z) {
/* 137 */       return null;
/*     */     } }
/*     */   
/*     */   public List<ModElement> getElements() {
/* 141 */     return this.elements;
/*     */   }
/*     */   
/*     */   public List<Supplier<Block>> getBlocks() {
/* 145 */     return this.blocks;
/*     */   }
/*     */   
/*     */   public List<Supplier<Item>> getItems() {
/* 149 */     return this.items;
/*     */   }
/*     */   
/*     */   public List<Supplier<Biome>> getBiomes() {
/* 153 */     return this.biomes;
/*     */   }
/*     */   
/*     */   public List<Supplier<EntityEntry>> getEntities() {
/* 157 */     return this.entities;
/*     */   }
/*     */   
/*     */   public List<Supplier<Potion>> getPotions() {
/* 161 */     return this.potions;
/*     */   }
/*     */   
/*     */   public static class ModElement
/*     */     implements Comparable<ModElement> {
/*     */     protected final ElementsBorutomodaddononeroMod elements;
/*     */     protected final int sortid;
/*     */     
/*     */     public ModElement(ElementsBorutomodaddononeroMod elements, int sortid) {
/* 170 */       this.elements = elements;
/* 171 */       this.sortid = sortid;
/*     */     }
/*     */ 
/*     */     
/*     */     public void initElements() {}
/*     */ 
/*     */     
/*     */     public void init(FMLInitializationEvent event) {}
/*     */ 
/*     */     
/*     */     public void preInit(FMLPreInitializationEvent event) {}
/*     */ 
/*     */     
/*     */     public void generateWorld(Random random, int posX, int posZ, World world, int dimID, IChunkGenerator cg, IChunkProvider cp) {}
/*     */ 
/*     */     
/*     */     public void serverLoad(FMLServerStartingEvent event) {}
/*     */ 
/*     */     
/*     */     public void registerModels(ModelRegistryEvent event) {}
/*     */     
/*     */     public int addFuel(ItemStack fuel) {
/* 193 */       return 0;
/*     */     }
/*     */ 
/*     */     
/*     */     public int compareTo(ModElement other) {
/* 198 */       return this.sortid - other.sortid;
/*     */     }
/*     */     
/*     */     @Retention(RetentionPolicy.RUNTIME)
/*     */     public static @interface Tag {}
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\ElementsBorutomodaddononeroMod.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */