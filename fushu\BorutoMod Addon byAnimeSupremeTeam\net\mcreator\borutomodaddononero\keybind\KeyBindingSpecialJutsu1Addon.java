/*     */ package net.mcreator.borutomodaddononero.keybind;
/*     */ 
/*     */ import io.netty.buffer.ByteBuf;
/*     */ import java.util.HashMap;
/*     */ import net.mcreator.borutomodaddononero.BorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.procedure.ProcedureSpecialJutsu1OnKeyPressedAddon;
/*     */ import net.minecraft.client.Minecraft;
/*     */ import net.minecraft.client.settings.KeyBinding;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.entity.player.EntityPlayerMP;
/*     */ import net.minecraft.util.math.BlockPos;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.common.MinecraftForge;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*     */ import net.minecraftforge.fml.common.gameevent.TickEvent;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class KeyBindingSpecialJutsu1Addon
/*     */   extends ElementsBorutomodaddononeroMod.ModElement
/*     */ {
/*     */   private KeyBinding keys;
/*     */   private boolean wasKeyPressed = false;
/*     */   
/*     */   public KeyBindingSpecialJutsu1Addon(ElementsBorutomodaddononeroMod instance) {
/*  34 */     super(instance, 105);
/*     */   }
/*     */ 
/*     */   
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  39 */     this.elements.addNetworkMessage(KeyBindingPressedMessageHandler.class, KeyBindingPressedMessage.class, new Side[] { Side.SERVER });
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  45 */     for (KeyBinding key : (Minecraft.func_71410_x()).field_71474_y.field_74324_K) {
/*  46 */       if (key.func_151464_g().equals("key.mcreator.specialjutsu1")) {
/*  47 */         this.keys = key;
/*     */         break;
/*     */       } 
/*     */     } 
/*  51 */     MinecraftForge.EVENT_BUS.register(this);
/*     */   }
/*     */   
/*     */   @SubscribeEvent
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void onClientTickEvent(TickEvent.ClientTickEvent event) {
/*  57 */     if (event.phase == TickEvent.Phase.END) {
/*  58 */       if (this.keys == null) {
/*  59 */         for (KeyBinding key : (Minecraft.func_71410_x()).field_71474_y.field_74324_K) {
/*  60 */           if (key.func_151464_g().equals("key.mcreator.specialjutsu1")) {
/*  61 */             this.keys = key;
/*     */             break;
/*     */           } 
/*     */         } 
/*  65 */         if (this.keys == null) {
/*     */           return;
/*     */         }
/*     */       } 
/*  69 */       if ((Minecraft.func_71410_x()).field_71462_r == null)
/*  70 */         if (this.keys.func_151470_d()) {
/*  71 */           if (!this.wasKeyPressed) {
/*  72 */             BorutomodaddononeroMod.PACKET_HANDLER.sendToServer(new KeyBindingPressedMessage(true));
/*  73 */             pressAction((EntityPlayer)(Minecraft.func_71410_x()).field_71439_g, true);
/*  74 */             this.wasKeyPressed = true;
/*     */           } 
/*     */         } else {
/*  77 */           this.wasKeyPressed = false;
/*     */         }  
/*     */     } 
/*     */   }
/*     */   
/*     */   public static class KeyBindingPressedMessageHandler
/*     */     implements IMessageHandler<KeyBindingPressedMessage, IMessage>
/*     */   {
/*     */     public IMessage onMessage(KeyBindingSpecialJutsu1Addon.KeyBindingPressedMessage message, MessageContext context) {
/*  86 */       EntityPlayerMP entity = (context.getServerHandler()).field_147369_b;
/*  87 */       entity.func_71121_q().func_152344_a(() -> KeyBindingSpecialJutsu1Addon.pressAction((EntityPlayer)entity, message.is_pressed));
/*     */ 
/*     */       
/*  90 */       return null;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class KeyBindingPressedMessage implements IMessage {
/*     */     boolean is_pressed;
/*     */     
/*     */     public KeyBindingPressedMessage() {}
/*     */     
/*     */     public KeyBindingPressedMessage(boolean is_pressed) {
/* 100 */       this.is_pressed = is_pressed;
/*     */     }
/*     */     
/*     */     public void toBytes(ByteBuf buf) {
/* 104 */       buf.writeBoolean(this.is_pressed);
/*     */     }
/*     */     
/*     */     public void fromBytes(ByteBuf buf) {
/* 108 */       this.is_pressed = buf.readBoolean();
/*     */     } }
/*     */   
/*     */   private static void pressAction(EntityPlayer entity, boolean is_pressed) {
/* 112 */     World world = entity.field_70170_p;
/* 113 */     int x = (int)entity.field_70165_t;
/* 114 */     int y = (int)entity.field_70163_u;
/* 115 */     int z = (int)entity.field_70161_v;
/*     */     
/* 117 */     if (!world.func_175667_e(new BlockPos(x, y, z))) {
/*     */       return;
/*     */     }
/* 120 */     HashMap<String, Object> $_dependencies = new HashMap<>();
/* 121 */     $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 122 */     $_dependencies.put("entity", entity);
/* 123 */     $_dependencies.put("x", Integer.valueOf(x));
/* 124 */     $_dependencies.put("y", Integer.valueOf(y));
/* 125 */     $_dependencies.put("z", Integer.valueOf(z));
/* 126 */     $_dependencies.put("world", world);
/* 127 */     ProcedureSpecialJutsu1OnKeyPressedAddon.executeProcedure($_dependencies);
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\keybind\KeyBindingSpecialJutsu1Addon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */