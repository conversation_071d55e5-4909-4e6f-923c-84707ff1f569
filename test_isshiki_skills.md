# 🧪 一式黑眼技能快速测试

## 测试命令序列

### 准备工作
```bash
# 切换到创造模式
/gamemode creative

# 给予一式黑眼瞳术
/give @p narutomod:isshikidojutsuhelmet 1

# 给予查克拉果实
/give @p narutomod:chakrafruit 64

# 清除所有效果
/effect @p clear
```

### 一技能测试 (少名毘古那)
```bash
# 1. 装备黑眼头盔
# 2. 按R键激活
# 预期结果：
#   - 听到音效
#   - 玩家缩小到0.1倍大小
#   - 获得隐身效果
#   - 缩放过程平滑（20tick渐变）

# 3. 再次按R键解除
# 预期结果：
#   - 玩家恢复正常大小
#   - 失去隐身效果
#   - 消耗饥饿值（基于持续时间）
```

### 二技能测试 (大黑天立方体)
```bash
# 1. 瞄准远处地面
# 2. 按T键发动
# 预期结果：
#   - 在瞄准位置上方生成立方体
#   - 立方体从高空落下
#   - 落地时对范围内敌人造成伤害
#   - 给予恶心和缓慢效果
#   - 10秒后自动消失
```

### 三技能测试 (五月雨长矛)
```bash
# 1. 瞄准目标方向
# 2. 按Y键发动
# 预期结果：
#   - 听到长矛发射音效
#   - 发射多支长矛（基于威力）
#   - 长矛有随机旋转角度
#   - 命中敌人造成伤害和恶心效果
#   - 长矛模型正确显示（细长形状）
```

## 🔍 重点检查项目

### 一技能修复验证
- [ ] **缩放效果**: 是否正确缩小到0.1倍？
- [ ] **持续性**: 缩放是否保持固定不变？
- [ ] **音效**: 激活时是否有音效？
- [ ] **解除**: 按R键是否正确解除？

### 三技能修复验证  
- [ ] **模型**: 长矛是否显示为细长形状？
- [ ] **贴图**: 长矛颜色是否正确？
- [ ] **数量**: 是否发射多支长矛？
- [ ] **旋转**: 长矛是否有随机旋转？

## 🚨 已知问题状态

### ✅ 已修复
- 一技能缩放机制
- 一技能音效播放
- 三技能模型渲染
- 三技能贴图显示

### ✅ 正常工作
- 二技能立方体召唤
- 查克拉消耗系统
- 按键绑定系统
- 服务器兼容性

## 📊 测试结果记录

### 一技能 (少名毘古那)
- 激活测试: [ ]
- 缩放效果: [ ]
- 音效播放: [ ]
- 解除功能: [ ]

### 二技能 (大黑天立方体)  
- 召唤测试: [ ]
- 攻击效果: [ ]
- 持续时间: [ ]
- 状态效果: [ ]

### 三技能 (五月雨长矛)
- 发射测试: [ ]
- 模型显示: [ ]
- 音效播放: [ ]
- 伤害计算: [ ]

---

**测试完成后请在上方勾选对应项目**  
**如发现问题请详细描述现象**
