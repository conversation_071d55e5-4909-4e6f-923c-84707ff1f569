/*     */ package net.mcreator.borutomodaddononero.keybind;
/*     */ 
/*     */ import io.netty.buffer.ByteBuf;
/*     */ import java.util.HashMap;
/*     */ import net.mcreator.borutomodaddononero.BorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.procedure.ProcedureSpecialJutsu2OnKeyPressedAddon;
/*     */ import net.minecraft.client.Minecraft;
/*     */ import net.minecraft.client.settings.KeyBinding;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.entity.player.EntityPlayerMP;
/*     */ import net.minecraft.util.math.BlockPos;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.common.MinecraftForge;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*     */ import net.minecraftforge.fml.common.gameevent.TickEvent;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessage;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.IMessageHandler;
/*     */ import net.minecraftforge.fml.common.network.simpleimpl.MessageContext;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class KeyBindingSpecialJutsu2Addon
/*     */   extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   private KeyBinding keys;
/*     */   private boolean wasKeyPressed = false;
/*     */   
/*     */   public KeyBindingSpecialJutsu2Addon(ElementsBorutomodaddononeroMod instance) {
/*  33 */     super(instance, 106);
/*     */   }
/*     */ 
/*     */   
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  38 */     this.elements.addNetworkMessage(KeyBindingPressedMessageHandler.class, KeyBindingPressedMessage.class, new Side[] { Side.SERVER });
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  44 */     for (KeyBinding key : (Minecraft.func_71410_x()).field_71474_y.field_74324_K) {
/*  45 */       if (key.func_151464_g().equals("key.mcreator.specialjutsu2")) {
/*  46 */         this.keys = key;
/*     */         break;
/*     */       } 
/*     */     } 
/*  50 */     MinecraftForge.EVENT_BUS.register(this);
/*     */   }
/*     */   
/*     */   @SubscribeEvent
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void onClientTickEvent(TickEvent.ClientTickEvent event) {
/*  56 */     if (event.phase == TickEvent.Phase.END) {
/*  57 */       if (this.keys == null) {
/*  58 */         for (KeyBinding key : (Minecraft.func_71410_x()).field_71474_y.field_74324_K) {
/*  59 */           if (key.func_151464_g().equals("key.mcreator.specialjutsu2")) {
/*  60 */             this.keys = key;
/*     */             break;
/*     */           } 
/*     */         } 
/*  64 */         if (this.keys == null) {
/*     */           return;
/*     */         }
/*     */       } 
/*  68 */       if ((Minecraft.func_71410_x()).field_71462_r == null)
/*  69 */         if (this.keys.func_151470_d()) {
/*  70 */           if (!this.wasKeyPressed) {
/*  71 */             BorutomodaddononeroMod.PACKET_HANDLER.sendToServer(new KeyBindingPressedMessage(true));
/*  72 */             pressAction((EntityPlayer)(Minecraft.func_71410_x()).field_71439_g, true);
/*  73 */             this.wasKeyPressed = true;
/*     */           } 
/*     */         } else {
/*  76 */           this.wasKeyPressed = false;
/*     */         }  
/*     */     } 
/*     */   }
/*     */   
/*     */   public static class KeyBindingPressedMessageHandler
/*     */     implements IMessageHandler<KeyBindingPressedMessage, IMessage>
/*     */   {
/*     */     public IMessage onMessage(KeyBindingSpecialJutsu2Addon.KeyBindingPressedMessage message, MessageContext context) {
/*  85 */       EntityPlayerMP entity = (context.getServerHandler()).field_147369_b;
/*  86 */       entity.func_71121_q().func_152344_a(() -> KeyBindingSpecialJutsu2Addon.pressAction((EntityPlayer)entity, message.is_pressed));
/*     */ 
/*     */       
/*  89 */       return null;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class KeyBindingPressedMessage implements IMessage {
/*     */     boolean is_pressed;
/*     */     
/*     */     public KeyBindingPressedMessage() {}
/*     */     
/*     */     public KeyBindingPressedMessage(boolean is_pressed) {
/*  99 */       this.is_pressed = is_pressed;
/*     */     }
/*     */ 
/*     */     
/*     */     public void toBytes(ByteBuf buf) {
/* 104 */       buf.writeBoolean(this.is_pressed);
/*     */     }
/*     */ 
/*     */     
/*     */     public void fromBytes(ByteBuf buf) {
/* 109 */       this.is_pressed = buf.readBoolean();
/*     */     }
/*     */   }
/*     */   
/*     */   private static void pressAction(EntityPlayer entity, boolean is_pressed) {
/* 114 */     World world = entity.field_70170_p;
/* 115 */     int x = (int)entity.field_70165_t;
/* 116 */     int y = (int)entity.field_70163_u;
/* 117 */     int z = (int)entity.field_70161_v;
/*     */     
/* 119 */     if (!world.func_175667_e(new BlockPos(x, y, z))) {
/*     */       return;
/*     */     }
/* 122 */     HashMap<String, Object> $_dependencies = new HashMap<>();
/* 123 */     $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 124 */     $_dependencies.put("entity", entity);
/* 125 */     $_dependencies.put("x", Integer.valueOf(x));
/* 126 */     $_dependencies.put("y", Integer.valueOf(y));
/* 127 */     $_dependencies.put("z", Integer.valueOf(z));
/* 128 */     $_dependencies.put("world", world);
/*     */     
/* 130 */     ProcedureSpecialJutsu2OnKeyPressedAddon.executeProcedure($_dependencies);
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\keybind\KeyBindingSpecialJutsu2Addon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */