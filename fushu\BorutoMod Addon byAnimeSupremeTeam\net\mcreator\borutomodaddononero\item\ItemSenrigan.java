/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.minecraft.client.model.ModelBase;
/*    */ import net.minecraft.client.model.ModelBiped;
/*    */ import net.minecraft.client.model.ModelBox;
/*    */ import net.minecraft.client.model.ModelRenderer;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemArmor;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.common.util.EnumHelper;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ @Tag
/*    */ public class ItemSenrigan
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   @ObjectHolder("borutomodaddononero:senriganhelmet")
/* 30 */   public static final Item helmet = null;
/*    */   @ObjectHolder("borutomodaddononero:senriganbody")
/* 32 */   public static final Item body = null;
/*    */   @ObjectHolder("borutomodaddononero:senriganlegs")
/* 34 */   public static final Item legs = null;
/*    */   @ObjectHolder("borutomodaddononero:senriganboots")
/* 36 */   public static final Item boots = null;
/*    */   public ItemSenrigan(ElementsBorutomodaddononeroMod instance) {
/* 38 */     super(instance, 5);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 43 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("SENRIGAN", "borutomodaddononero:testing", 25, new int[] { 2, 5, 6, 2 }, 9, (SoundEvent)SoundEvent.field_187505_a
/* 44 */         .func_82594_a(new ResourceLocation("")), 0.0F);
/* 45 */     this.elements.items.add(() -> ((Item)(new ItemArmor(enuma, 0, EntityEquipmentSlot.HEAD)
/*    */         {
/*    */           @SideOnly(Side.CLIENT)
/*    */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/* 49 */             ModelBiped armorModel = new ModelBiped();
/* 50 */             armorModel.field_78116_c = (new ItemSenrigan.ModelSenrigan()).HatLayer;
/* 51 */             armorModel.field_78117_n = living.func_70093_af();
/* 52 */             armorModel.field_78093_q = living.func_184218_aH();
/* 53 */             armorModel.field_78091_s = living.func_70631_g_();
/* 54 */             return armorModel;
/*    */           }
/*    */ 
/*    */           
/*    */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/* 59 */             return "borutomodaddononero:textures/senrigan_texture.png";
/*    */           }
/*    */         }).func_77655_b("senriganhelmet").setRegistryName("senriganhelmet")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 67 */     ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("borutomodaddononero:senriganhelmet", "inventory"));
/*    */   }
/*    */   
/*    */   public static class ModelSenrigan
/*    */     extends ModelBase {
/*    */     private final ModelRenderer HatLayer;
/*    */     
/*    */     public ModelSenrigan() {
/* 75 */       this.field_78090_t = 64;
/* 76 */       this.field_78089_u = 16;
/* 77 */       this.HatLayer = new ModelRenderer(this);
/* 78 */       this.HatLayer.func_78793_a(0.0F, 0.0F, 0.0F);
/* 79 */       this.HatLayer.field_78804_l.add(new ModelBox(this.HatLayer, 0, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.025F, false));
/*    */     }
/*    */ 
/*    */     
/*    */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 84 */       this.HatLayer.func_78785_a(f5);
/*    */     }
/*    */     
/*    */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 88 */       modelRenderer.field_78795_f = x;
/* 89 */       modelRenderer.field_78796_g = y;
/* 90 */       modelRenderer.field_78808_h = z;
/*    */     }
/*    */     
/*    */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity e) {
/* 94 */       super.func_78087_a(f, f1, f2, f3, f4, f5, e);
/*    */     }
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemSenrigan.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */