# 🔥 一式黑眼瞳术测试指南

## 📋 测试概述

本指南用于测试从BorutoMod Addon完整复制的一式黑眼瞳术功能，确保与原版完全一致。

---

## 🎯 测试准备

### 获取测试物品
```bash
# 获取一式黑眼瞳术
/give @p narutomod:isshikidojutsuhelmet 1

# 获取查克拉果实（用于补充查克拉）
/give @p narutomod:chakrafruit 64

# 切换到创造模式
/gamemode creative
```

### 基础检查
1. **装备检查**: 装备一式黑眼瞳术头盔
2. **查克拉检查**: 确保查克拉充足（建议10000+）
3. **按键检查**: 确认R、T、Y键可以正常使用

---

## 🔬 技能测试

### 1. 少名毘古那技能 (R键)

#### 功能描述
- **技能名称**: 少名毘古那 (Sukunahikona)
- **按键**: R键
- **查克拉消耗**: 450点
- **效果**: 缩小自身，获得隐身效果和特殊能力

#### 测试步骤
1. **激活测试**:
   - 按R键激活少名毘古那
   - 检查是否播放音效
   - 确认玩家被缩小并获得隐身效果

2. **功能验证**:
   - 验证玩家变小（0.1倍大小）
   - 确认获得隐身药水效果
   - 检查移动速度和跳跃能力
   - 验证特殊攻击能力

3. **解除测试**:
   - 再次按R键解除技能
   - 确认玩家恢复正常大小
   - 检查饥饿值消耗（基于持续时间）

#### 预期结果
- ✅ 成功缩小并隐身
- ✅ 音效正常播放
- ✅ 可以正常解除
- ✅ 查克拉正确消耗

### 2. 大黑天立方体技能 (T键)

#### 功能描述
- **技能名称**: 大黑天立方体 (Daikokuten Cube)
- **按键**: T键
- **查克拉消耗**: 250点
- **效果**: 在瞄准位置召唤巨大立方体攻击敌人

#### 测试步骤
1. **召唤测试**:
   - 瞄准远处的地面或方块
   - 按T键发动技能
   - 观察立方体是否在目标位置上方生成

2. **攻击效果**:
   - 立方体应该从高空落下
   - 落地时对范围内敌人造成伤害
   - 给予敌人恶心和缓慢效果

3. **持续时间**:
   - 立方体应该在地面停留约10秒
   - 10秒后自动消失

#### 预期结果
- ✅ 立方体正确生成
- ✅ 攻击效果正常
- ✅ 状态效果正确施加
- ✅ 自动消失机制正常

### 3. 五月雨长矛技能 (Y键)

#### 功能描述
- **技能名称**: 五月雨长矛 (Samidare Spear)
- **按键**: Y键
- **查克拉消耗**: 150点
- **效果**: 发射多支查克拉长矛攻击敌人

#### 测试步骤
1. **发射测试**:
   - 瞄准目标方向
   - 按Y键发动技能
   - 观察是否发射多支长矛

2. **长矛特性**:
   - 长矛应该有随机的旋转角度
   - 飞行轨迹略有不同
   - 命中敌人时造成伤害和恶心效果

3. **音效验证**:
   - 确认播放长矛发射音效
   - 音效应该清晰可听

#### 预期结果
- ✅ 多支长矛正确发射
- ✅ 随机旋转效果正常
- ✅ 伤害和状态效果正确
- ✅ 音效正常播放

---

## 🔧 服务器兼容性测试

### 单人游戏测试
1. 在单人世界中测试所有技能
2. 确认没有崩溃或错误
3. 验证所有效果正常显示

### 多人服务器测试
1. 在多人服务器中测试
2. 确认客户端-服务端同步正常
3. 验证其他玩家可以看到效果
4. 检查服务器日志无错误

---

## 🎮 性能测试

### 查克拉消耗验证
- **少名毘古那**: 450点查克拉
- **大黑天立方体**: 250点查克拉
- **五月雨长矛**: 150点查克拉

### 冷却时间检查
- 确认技能有适当的冷却时间
- 验证查克拉不足时的提示信息

### 疲劳值系统
- 装备黑眼时每秒消耗2点疲劳值
- 比万花筒写轮眼消耗更大

---

## 🐛 故障排除

### 常见问题
1. **技能无法发动**:
   - 检查查克拉是否足够
   - 确认装备了正确的头盔
   - 验证按键绑定是否正确

2. **音效无法播放**:
   - 检查游戏音量设置
   - 确认音效文件存在
   - 重新启动游戏

3. **视觉效果异常**:
   - 检查显卡驱动
   - 降低游戏画质设置
   - 重新加载资源包

### 调试命令
```bash
# 补充查克拉
/give @p narutomod:chakrafruit 64

# 清除所有效果
/effect @p clear

# 检查玩家状态
/effect @p

# 重新给予物品
/clear @p
/give @p narutomod:isshikidojutsuhelmet 1
```

---

## ✅ 测试检查清单

### 基础功能
- [ ] 装备正常显示
- [ ] 按键响应正确
- [ ] 查克拉消耗准确
- [ ] 疲劳值消耗正常

### 少名毘古那
- [ ] 缩小效果正常
- [ ] 隐身效果正确
- [ ] 音效播放正常
- [ ] 解除功能正常
- [ ] 饥饿值消耗正确

### 大黑天立方体
- [ ] 立方体生成正确
- [ ] 攻击范围准确
- [ ] 伤害数值正确
- [ ] 状态效果正常
- [ ] 持续时间准确

### 五月雨长矛
- [ ] 长矛发射正常
- [ ] 数量正确（基于威力）
- [ ] 随机旋转效果
- [ ] 伤害计算正确
- [ ] 音效播放正常

### 服务器兼容性
- [ ] 单人游戏正常
- [ ] 多人服务器正常
- [ ] 客户端同步正确
- [ ] 服务器无错误日志

---

## 🎉 测试完成

如果所有测试项目都通过，说明一式黑眼瞳术功能已经完全复制成功，与原版BorutoMod Addon完全一致！

**测试状态**: 待验证  
**最后更新**: 2025-07-29  
**版本**: NarutoMod 0.3.2-beta
