/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.entity.EntityFistOfPush;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundCategory;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraft.util.math.Vec3d;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.Chakra;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureFistOfPush
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureFistOfPush(ElementsBorutomodaddononeroMod instance) {
/* 20 */     super(instance, 264);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 24 */     if (dependencies.get("is_pressed") == null) {
/* 25 */       System.err.println("Failed to load dependency is_pressed for procedure FistOfPush!");
/*    */       return;
/*    */     } 
/* 28 */     if (dependencies.get("entity") == null) {
/* 29 */       System.err.println("Failed to load dependency entity for procedure FistOfPush!");
/*    */       return;
/*    */     } 
/* 32 */     if (dependencies.get("world") == null) {
/* 33 */       System.err.println("Failed to load dependency world for procedure FistOfPush!");
/*    */       return;
/*    */     } 
/* 36 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 37 */     Entity entity = (Entity)dependencies.get("entity");
/* 38 */     World world = (World)dependencies.get("world");
/* 39 */     if (is_pressed) {
/* 40 */       if (!(entity instanceof EntityLivingBase)) {
/*    */         return;
/*    */       }
/* 43 */       EntityLivingBase livingEntity = (EntityLivingBase)entity;
/* 44 */       if (Chakra.pathway(livingEntity).getAmount() >= 250.0D) {
/* 45 */         Chakra.pathway(livingEntity).consume(250.0D);
/* 46 */         SoundEvent sound = (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("narutomod:throwpunch"));
/* 47 */         if (sound != null) {
/* 48 */           world.func_184148_a(null, livingEntity.field_70165_t, livingEntity.field_70163_u, livingEntity.field_70161_v, sound, SoundCategory.NEUTRAL, 1.0F, livingEntity.func_70681_au().nextFloat() * 0.6F + 0.6F);
/*    */         }
/* 50 */         Vec3d lookVec = livingEntity.func_70040_Z();
/* 51 */         EntityFistOfPush.FistOfPush fistEntity = new EntityFistOfPush.FistOfPush(livingEntity);
/* 52 */         world.func_72838_d((Entity)fistEntity);
/* 53 */         fistEntity.func_70186_c(lookVec.field_72450_a, lookVec.field_72448_b, lookVec.field_72449_c, 1.25F, 0.1F);
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureFistOfPush.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */