/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.init.MobEffects;
/*    */ import net.minecraft.potion.PotionEffect;
/*    */ import net.narutomod.Chakra;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureBoroCoreRegeneration
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureBoroCoreRegeneration(ElementsBorutomodaddononeroMod instance) {
/* 16 */     super(instance, 7);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 20 */     if (dependencies.get("entity") == null) {
/* 21 */       System.err.println("Failed to load dependency entity for procedure BoroCoreRegeneration!");
/*    */       return;
/*    */     } 
/* 24 */     Entity entity = (Entity)dependencies.get("entity");
/* 25 */     if (entity instanceof EntityLivingBase) {
/* 26 */       EntityLivingBase living = (EntityLivingBase)entity;
/* 27 */       Chakra.Pathway chakra = Chakra.pathway(living);
/* 28 */       if (chakra.consume(0.1D)) {
/* 29 */         ((EntityLivingBase)entity).func_70690_d(new PotionEffect(MobEffects.field_76432_h, 20, 1000, false, false));
/* 30 */         ((EntityLivingBase)entity).func_70690_d(new PotionEffect(MobEffects.field_76428_l, 20, 1000, false, false));
/* 31 */         ((EntityLivingBase)entity).func_70690_d(new PotionEffect(MobEffects.field_76443_y, 20, 1000, false, false));
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureBoroCoreRegeneration.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */