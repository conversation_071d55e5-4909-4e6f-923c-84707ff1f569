/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ import java.util.Random;
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.minecraft.block.Block;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.IProjectile;
/*     */ import net.minecraft.nbt.NBTBase;
/*     */ import net.minecraft.nbt.NBTTagCompound;
/*     */ import net.minecraft.nbt.NBTTagList;
/*     */ import net.minecraft.network.datasync.DataParameter;
/*     */ import net.minecraft.network.datasync.EntityDataManager;
/*     */ import net.minecraft.util.EnumFacing;
/*     */ import net.minecraft.util.EnumParticleTypes;
/*     */ import net.minecraft.util.math.AxisAlignedBB;
/*     */ import net.minecraft.util.math.BlockPos;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.util.math.RayTraceResult;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraft.world.WorldServer;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ @Tag
/*     */ public class EntityScalableProjectile extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public EntityScalableProjectile(ElementsBorutomodaddononeroMod instance) {
/*  28 */     super(instance, 520);
/*     */   }
/*     */   
/*     */   public static abstract class Base extends Entity implements IProjectile {
/*  32 */     private static final DataParameter<Float> MODEL_SCALE = EntityDataManager.func_187226_a(Base.class, DataSerializers.field_187193_c);
/*     */     private float ogWidth;
/*     */     private float ogHeight;
/*     */     public EntityLivingBase shootingEntity;
/*     */     private double accelerationX;
/*     */     private double accelerationY;
/*     */     private double accelerationZ;
/*     */     protected int ticksAlive;
/*     */     protected int ticksInAir;
/*     */     protected int ticksInGround;
/*  42 */     protected int maxInGroundTime = 1200;
/*     */     private float motionFactor;
/*  44 */     private float waterSlowdown = 0.8F;
/*     */     
/*     */     public Base(World world) {
/*  47 */       super(world);
/*  48 */       this.field_70178_ae = false;
/*     */     }
/*     */     
/*     */     public Base(EntityLivingBase shooter) {
/*  52 */       this(shooter.field_70170_p);
/*  53 */       this.shootingEntity = shooter;
/*  54 */       func_189654_d(true);
/*  55 */       func_174805_g(false);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70088_a() {
/*  60 */       func_184212_Q().func_187214_a(MODEL_SCALE, Float.valueOf(1.0F));
/*     */     }
/*     */     
/*     */     protected void setOGSize(float width, float height) {
/*  64 */       this.ogWidth = width;
/*  65 */       this.ogHeight = height;
/*  66 */       if (this.field_70148_d) {
/*  67 */         func_70105_a(width, height);
/*     */       }
/*     */     }
/*     */     
/*     */     public float getEntityScale() {
/*  72 */       return ((Float)func_184212_Q().func_187225_a(MODEL_SCALE)).floatValue();
/*     */     }
/*     */     
/*     */     public void setEntityScale(float scale) {
/*  76 */       if (!this.field_70170_p.field_72995_K) {
/*  77 */         func_184212_Q().func_187227_b(MODEL_SCALE, Float.valueOf(scale));
/*  78 */         double x = this.field_70165_t;
/*  79 */         double y = this.field_70163_u;
/*  80 */         double z = this.field_70161_v;
/*  81 */         func_70105_a(this.ogWidth * scale, this.ogHeight * scale);
/*  82 */         func_70107_b(x, y, z);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_184206_a(DataParameter<?> key) {
/*  88 */       super.func_184206_a(key);
/*  89 */       if (MODEL_SCALE.equals(key) && this.field_70170_p.field_72995_K) {
/*  90 */         float scale = getEntityScale();
/*  91 */         func_70105_a(this.ogWidth * scale, this.ogHeight * scale);
/*     */       } 
/*     */     }
/*     */     
/*     */     public boolean isLaunched() {
/*  96 */       return (this.motionFactor > 0.0F);
/*     */     }
/*     */     
/*     */     public void haltMotion() {
/* 100 */       this.motionFactor = 0.0F;
/* 101 */       this.accelerationX = 0.0D;
/* 102 */       this.accelerationY = 0.0D;
/* 103 */       this.accelerationZ = 0.0D;
/* 104 */       this.field_70159_w = 0.0D;
/* 105 */       this.field_70181_x = 0.0D;
/* 106 */       this.field_70179_y = 0.0D;
/*     */     }
/*     */     
/*     */     protected void setWaterSlowdown(float f) {
/* 110 */       this.waterSlowdown = f;
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70186_c(double x, double y, double z, float speed, float inaccuracy) {
/* 115 */       shoot(x, y, z, speed, inaccuracy, true);
/*     */     }
/*     */     
/*     */     public void shoot(double x, double y, double z, float speed, float inaccuracy, boolean updateRotations) {
/* 119 */       x += this.field_70146_Z.nextGaussian() * inaccuracy;
/* 120 */       y += this.field_70146_Z.nextGaussian() * inaccuracy;
/* 121 */       z += this.field_70146_Z.nextGaussian() * inaccuracy;
/* 122 */       if (updateRotations) {
/* 123 */         float f1 = MathHelper.func_76133_a(x * x + z * z);
/* 124 */         this.field_70177_z = (float)(-MathHelper.func_181159_b(x, z) * 57.29577951308232D);
/* 125 */         this.field_70125_A = (float)(-MathHelper.func_181159_b(y, f1) * 57.29577951308232D);
/* 126 */         if (this.motionFactor == 0.0F) {
/* 127 */           this.field_70126_B = this.field_70177_z;
/* 128 */           this.field_70127_C = this.field_70125_A;
/*     */         } 
/*     */       } 
/* 131 */       double d0 = MathHelper.func_76133_a(x * x + y * y + z * z);
/* 132 */       if (func_189652_ae()) {
/* 133 */         this.accelerationX = x / d0 * 0.1D;
/* 134 */         this.accelerationY = y / d0 * 0.1D;
/* 135 */         this.accelerationZ = z / d0 * 0.1D;
/*     */       } else {
/* 137 */         this.field_70159_w = x / d0 * speed;
/* 138 */         this.field_70181_x = y / d0 * speed;
/* 139 */         this.field_70179_y = z / d0 * speed;
/*     */       } 
/* 141 */       this.motionFactor = speed;
/*     */     }
/*     */     
/*     */     public double getAcceleration() {
/* 145 */       return (MathHelper.func_76133_a(this.accelerationX * this.accelerationX + this.accelerationY * this.accelerationY + this.accelerationZ * this.accelerationZ) * this.motionFactor);
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70067_L() {
/* 150 */       return true;
/*     */     }
/*     */     
/*     */     protected void checkOnGround() {
/* 154 */       BlockPos pos = new BlockPos(this);
/* 155 */       if (!this.field_70170_p.func_175623_d(pos)) {
/* 156 */         AxisAlignedBB aabb = this.field_70170_p.func_180495_p(pos).func_185890_d((IBlockAccess)this.field_70170_p, pos);
/* 157 */         if (aabb != Block.field_185506_k && aabb.func_186670_a(pos).func_72318_a(func_174791_d())) {
/* 158 */           this.field_70122_E = true;
/*     */         }
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/* 165 */       super.func_70071_h_();
/* 166 */       this.ticksAlive++;
/* 167 */       if ((!this.field_70170_p.field_72995_K && this.shootingEntity != null && this.shootingEntity.field_70128_L) || 
/* 168 */         !this.field_70170_p.func_175667_e(new BlockPos(this))) {
/* 169 */         func_70106_y();
/*     */       } else {
/* 171 */         checkOnGround();
/* 172 */         if (this.field_70122_E) {
/* 173 */           this.motionFactor = 0.0F;
/* 174 */           if (++this.ticksInGround > this.maxInGroundTime) {
/* 175 */             func_70106_y();
/*     */           }
/*     */         } else {
/* 178 */           float f = this.motionFactor;
/* 179 */           if (f > 0.0F) {
/* 180 */             this.ticksInAir++;
/* 181 */             RayTraceResult raytraceresult = forwardsRaycast(true, (this.ticksInAir >= 25), (Entity)this.shootingEntity);
/* 182 */             if (raytraceresult != null) {
/* 183 */               onImpact(raytraceresult);
/*     */             }
/*     */           } 
/* 186 */           this.field_70165_t += this.field_70159_w;
/* 187 */           this.field_70163_u += this.field_70181_x;
/* 188 */           this.field_70161_v += this.field_70179_y;
/* 189 */           if (f > 0.0F && !func_189652_ae()) {
/* 190 */             updateInFlightRotations();
/*     */           }
/* 192 */           if (f > 0.0F && func_70090_H()) {
/* 193 */             for (int i = 0; i < 4; i++) {
/* 194 */               this.field_70170_p.func_175688_a(EnumParticleTypes.WATER_BUBBLE, this.field_70165_t - this.field_70159_w * 0.25D, this.field_70163_u - this.field_70181_x * 0.25D, this.field_70161_v - this.field_70179_y * 0.25D, this.field_70159_w, this.field_70181_x, this.field_70179_y, new int[0]);
/*     */             }
/* 196 */             f *= this.waterSlowdown;
/*     */           } 
/* 198 */           if (f > 0.0F && func_189652_ae()) {
/* 199 */             this.field_70159_w += this.accelerationX;
/* 200 */             this.field_70181_x += this.accelerationY;
/* 201 */             this.field_70179_y += this.accelerationZ;
/* 202 */             this.field_70159_w *= f;
/* 203 */             this.field_70181_x *= f;
/* 204 */             this.field_70179_y *= f;
/*     */           } 
/* 206 */           if (!func_189652_ae()) {
/* 207 */             this.field_70159_w *= 0.98D;
/* 208 */             this.field_70179_y *= 0.98D;
/* 209 */             this.field_70181_x = this.field_70181_x * 0.98D - 0.04D;
/*     */           } 
/* 211 */           renderParticles();
/* 212 */           func_70107_b(this.field_70165_t, this.field_70163_u, this.field_70161_v);
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     protected RayTraceResult forwardsRaycast(boolean includeEntities, boolean ignoreExcludedEntity, @Nullable Entity excludedEntity) {
/* 218 */       RayTraceResult res = EntityScalableProjectile.forwardsRaycast(this, includeEntities, ignoreExcludedEntity, excludedEntity);
/* 219 */       return (res != null && res.field_72308_g instanceof Base && ((Base)res.field_72308_g).shootingEntity != null && ((Base)res.field_72308_g).shootingEntity
/* 220 */         .equals(this.shootingEntity)) ? null : res;
/*     */     }
/*     */     
/*     */     public Random getRNG() {
/* 224 */       return this.field_70146_Z;
/*     */     }
/*     */     
/*     */     protected abstract void onImpact(RayTraceResult param1RayTraceResult);
/*     */     
/*     */     public void updateInFlightRotations() {
/* 230 */       double d = MathHelper.func_76133_a(this.field_70159_w * this.field_70159_w + this.field_70179_y * this.field_70179_y);
/* 231 */       float yaw = -((float)(MathHelper.func_181159_b(this.field_70159_w, this.field_70179_y) * 57.29577951308232D));
/* 232 */       float pitch = -((float)(MathHelper.func_181159_b(this.field_70181_x, d) * 57.29577951308232D));
/* 233 */       while (yaw - this.field_70126_B < -180.0F) {
/* 234 */         this.field_70126_B -= 360.0F;
/*     */       }
/* 236 */       while (yaw - this.field_70126_B >= 180.0F) {
/* 237 */         this.field_70126_B += 360.0F;
/*     */       }
/* 239 */       while (pitch - this.field_70127_C < -180.0F) {
/* 240 */         this.field_70127_C -= 360.0F;
/*     */       }
/* 242 */       while (pitch - this.field_70127_C >= 180.0F) {
/* 243 */         this.field_70127_C += 360.0F;
/*     */       }
/* 245 */       this.field_70125_A = this.field_70127_C + (pitch - this.field_70127_C) * 0.2F;
/* 246 */       this.field_70177_z = this.field_70126_B + (yaw - this.field_70126_B) * 0.2F;
/*     */     }
/*     */     
/*     */     public void renderParticles() {
/* 250 */       if (this.motionFactor > 0.0F && this.field_70170_p instanceof WorldServer) {
/* 251 */         ((WorldServer)this.field_70170_p).func_175739_a(EnumParticleTypes.SMOKE_LARGE, this.field_70165_t, this.field_70163_u + (this.field_70131_O / 2.0F), this.field_70161_v, 
/* 252 */             (int)getEntityScale(), this.field_70130_N * 0.5D, this.field_70131_O * 0.5D, this.field_70130_N * 0.5D, 0.0D, new int[0]);
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public boolean func_70112_a(double distance) {
/* 259 */       return (distance <= 4096.0D || super.func_70112_a(distance));
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70037_a(NBTTagCompound compound) {
/* 264 */       if (compound.func_150297_b("power", 9)) {
/* 265 */         NBTTagList nbttaglist = compound.func_150295_c("power", 6);
/* 266 */         if (nbttaglist.func_74745_c() == 3) {
/* 267 */           this.accelerationX = nbttaglist.func_150309_d(0);
/* 268 */           this.accelerationY = nbttaglist.func_150309_d(1);
/* 269 */           this.accelerationZ = nbttaglist.func_150309_d(2);
/*     */         } 
/*     */       } 
/* 272 */       if (compound.func_150297_b("direction", 9) && compound.func_150295_c("direction", 6).func_74745_c() == 3) {
/* 273 */         NBTTagList nbttaglist1 = compound.func_150295_c("direction", 6);
/* 274 */         this.field_70159_w = nbttaglist1.func_150309_d(0);
/* 275 */         this.field_70181_x = nbttaglist1.func_150309_d(1);
/* 276 */         this.field_70179_y = nbttaglist1.func_150309_d(2);
/*     */       } else {
/* 278 */         func_70106_y();
/*     */       } 
/* 280 */       setEntityScale(compound.func_74760_g("scale"));
/* 281 */       func_70105_a(this.ogWidth * getEntityScale(), this.ogHeight * getEntityScale());
/* 282 */       this.motionFactor = compound.func_74760_g("speed");
/* 283 */       this.ticksAlive = compound.func_74762_e("life");
/* 284 */       this.ticksInAir = compound.func_74762_e("flighttime");
/* 285 */       this.ticksInGround = compound.func_74762_e("groundtime");
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70014_b(NBTTagCompound compound) {
/* 290 */       compound.func_74782_a("direction", (NBTBase)func_70087_a(new double[] { this.field_70159_w, this.field_70181_x, this.field_70179_y }));
/* 291 */       compound.func_74782_a("power", (NBTBase)func_70087_a(new double[] { this.accelerationX, this.accelerationY, this.accelerationZ }));
/* 292 */       compound.func_74776_a("scale", getEntityScale());
/* 293 */       compound.func_74776_a("speed", this.motionFactor);
/* 294 */       compound.func_74768_a("life", this.ticksAlive);
/* 295 */       compound.func_74768_a("flighttime", this.ticksInAir);
/* 296 */       compound.func_74768_a("groundtime", this.ticksInGround);
/*     */     }
/*     */   }
/*     */   
/*     */   public static RayTraceResult forwardsRaycastBlocks(Entity projectile) {
/* 301 */     return forwardsRaycast(projectile, false, false, null);
/*     */   }
/*     */   
/*     */   public static RayTraceResult forwardsRaycast(Entity projectile, boolean includeEntities, boolean ignoreExcludedEntity, @Nullable Entity excludedEntity) {
/* 305 */     World world = projectile.field_70170_p;
/* 306 */     Vec3d vec3d = new Vec3d(projectile.field_70165_t, projectile.field_70163_u + (projectile.field_70131_O / 2.0F), projectile.field_70161_v);
/* 307 */     Vec3d vec3d1 = new Vec3d(projectile.field_70159_w, projectile.field_70181_x, projectile.field_70179_y);
/* 308 */     Vec3d vec3d2 = vec3d.func_178787_e(vec3d1);
/* 309 */     AxisAlignedBB bigAABB = projectile.func_174813_aQ().func_72321_a(vec3d1.field_72450_a, vec3d1.field_72448_b, vec3d1.field_72449_c).func_186662_g(1.0D);
/* 310 */     double d0 = 0.0D;
/* 311 */     BlockPos blockpos = null;
/* 312 */     EnumFacing facing = null;
/* 313 */     RayTraceResult raytraceresult = null;
/* 314 */     for (AxisAlignedBB aabb : world.func_184144_a(null, bigAABB)) {
/* 315 */       RayTraceResult result = aabb.func_72314_b((projectile.field_70130_N / 2.0F), (projectile.field_70131_O / 2.0F), (projectile.field_70130_N / 2.0F)).func_72327_a(vec3d, vec3d2);
/* 316 */       if (result != null) {
/* 317 */         double d = projectile.func_70092_e(ProcedureUtils.BB.getCenterX(aabb), ProcedureUtils.BB.getCenterY(aabb), ProcedureUtils.BB.getCenterZ(aabb));
/* 318 */         if (d < d0 || d0 == 0.0D) {
/* 319 */           blockpos = new BlockPos(aabb.field_72340_a, aabb.field_72338_b, aabb.field_72339_c);
/* 320 */           facing = result.field_178784_b;
/* 321 */           d0 = d;
/*     */         } 
/*     */       } 
/*     */     } 
/* 325 */     if (blockpos != null) {
/* 326 */       raytraceresult = new RayTraceResult(new Vec3d((Vec3i)blockpos), facing, blockpos);
/*     */     }
/* 328 */     if (includeEntities) {
/* 329 */       Entity entity = null;
/* 330 */       Vec3d hitvec = null;
/* 331 */       for (Entity entity1 : world.func_72839_b(projectile, bigAABB)) {
/* 332 */         if (entity1.func_70067_L() && (ignoreExcludedEntity || !entity1.equals(excludedEntity)) && !entity1.field_70145_X) {
/* 333 */           AxisAlignedBB aabb = entity1.func_174813_aQ().func_72314_b((projectile.field_70130_N / 2.0F), (projectile.field_70131_O / 2.0F), (projectile.field_70130_N / 2.0F));
/* 334 */           RayTraceResult result = aabb.func_72327_a(vec3d, vec3d2);
/* 335 */           if (result != null) {
/* 336 */             double d = vec3d.func_72438_d(result.field_72307_f);
/* 337 */             if (d < d0 || d0 == 0.0D) {
/* 338 */               entity = entity1;
/* 339 */               hitvec = result.field_72307_f;
/* 340 */               d0 = d;
/*     */             } 
/*     */           } 
/*     */         } 
/*     */       } 
/* 345 */       if (entity != null) {
/* 346 */         raytraceresult = new RayTraceResult(entity, hitvec);
/*     */       }
/*     */     } 
/* 349 */     return raytraceresult;
/*     */   }
/*     */   
/*     */   public Vec3d getCenter(AxisAlignedBB bb) {
/* 353 */     return new Vec3d(bb.field_72340_a + (bb.field_72336_d - bb.field_72340_a) * 0.5D, bb.field_72338_b + (bb.field_72337_e - bb.field_72338_b) * 0.5D, bb.field_72339_c + (bb.field_72334_f - bb.field_72339_c) * 0.5D);
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityScalableProjectile.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */