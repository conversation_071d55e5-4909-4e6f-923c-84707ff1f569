/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.mcreator.borutomodaddononero.procedure.ProcedureBoroCoreRegeneration;
/*    */ import net.minecraft.client.model.ModelBiped;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.init.Items;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemArmor;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraft.world.World;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.common.util.EnumHelper;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ @Tag
/*    */ public class ItemBoroCore
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   @ObjectHolder("borutomodaddononero:boro_corebody")
/* 33 */   public static final Item body = null;
/*    */   
/*    */   public ItemBoroCore(ElementsBorutomodaddononeroMod instance) {
/* 36 */     super(instance, 6);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 41 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("BORO_CORE", "borutomodaddononero:testing", 25, new int[] { 0, 0, 1, 0 }, 1, (SoundEvent)SoundEvent.field_187505_a
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */         
/* 47 */         .func_82594_a(new ResourceLocation("")), 0.0F);
/*    */ 
/*    */     
/* 50 */     enuma.setRepairItem(new ItemStack(Items.field_151166_bC));
/* 51 */     this.elements.items.add(() -> ((Item)(new ItemArmor(enuma, 0, EntityEquipmentSlot.CHEST)
/*    */         {
/*    */           @SideOnly(Side.CLIENT)
/*    */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/* 55 */             ModelBiped armorModel = new ModelBiped();
/* 56 */             return armorModel;
/*    */           }
/*    */ 
/*    */           
/*    */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/* 61 */             return "borutomodaddononero:textures/texture_boro_core.png";
/*    */           }
/*    */ 
/*    */           
/*    */           public void onArmorTick(World world, EntityPlayer entity, ItemStack itemstack) {
/* 66 */             int x = (int)entity.field_70165_t;
/* 67 */             int y = (int)entity.field_70163_u;
/* 68 */             int z = (int)entity.field_70161_v;
/*    */             
/* 70 */             Map<Object, Object> $_dependencies = new HashMap<>();
/* 71 */             $_dependencies.put("entity", entity);
/* 72 */             ProcedureBoroCoreRegeneration.executeProcedure($_dependencies);
/*    */           }
/*    */         }).func_77655_b("boro_corebody").setRegistryName("boro_corebody")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 81 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:boro_corebody", "inventory"));
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemBoroCore.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */