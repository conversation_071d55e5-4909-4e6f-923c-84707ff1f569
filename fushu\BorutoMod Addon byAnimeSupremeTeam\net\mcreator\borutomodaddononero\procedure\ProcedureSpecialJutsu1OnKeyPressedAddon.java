/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemBaryonCloak;
/*    */ import net.mcreator.borutomodaddononero.item.ItemKokugan;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.world.World;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSpecialJutsu1OnKeyPressedAddon
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSpecialJutsu1OnKeyPressedAddon(ElementsBorutomodaddononeroMod instance) {
/* 18 */     super(instance, 101);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 22 */     if (dependencies.get("is_pressed") == null) {
/* 23 */       System.err.println("Failed to load dependency is_pressed for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 26 */     if (dependencies.get("entity") == null) {
/* 27 */       System.err.println("Failed to load dependency entity for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 30 */     if (dependencies.get("x") == null) {
/* 31 */       System.err.println("Failed to load dependency x for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 34 */     if (dependencies.get("y") == null) {
/* 35 */       System.err.println("Failed to load dependency y for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 38 */     if (dependencies.get("z") == null) {
/* 39 */       System.err.println("Failed to load dependency z for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 42 */     if (dependencies.get("world") == null) {
/* 43 */       System.err.println("Failed to load dependency world for procedure SpecialJutsu1OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 46 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 47 */     Entity entity = (Entity)dependencies.get("entity");
/* 48 */     int x = ((Integer)dependencies.get("x")).intValue();
/* 49 */     int y = ((Integer)dependencies.get("y")).intValue();
/* 50 */     int z = ((Integer)dependencies.get("z")).intValue();
/* 51 */     World world = (World)dependencies.get("world");
/* 52 */     EntityPlayer player = (EntityPlayer)entity;
/* 53 */     ItemStack helmet = ItemStack.field_190927_a;
/* 54 */     ItemStack chestplate = (ItemStack)player.field_71071_by.field_70460_b.get(2);
/* 55 */     if (world.field_72995_K || ((EntityPlayer)entity).func_175149_v()) {
/*    */       return;
/*    */     }
/* 58 */     helmet = (entity instanceof EntityPlayer) ? (ItemStack)((EntityPlayer)entity).field_71071_by.field_70460_b.get(3) : ItemStack.field_190927_a;
/* 59 */     if (helmet.func_77973_b() == (new ItemStack(ItemKokugan.helmet, 1)).func_77973_b()) {
/*    */       
/* 61 */       Map<String, Object> $_dependencies = new HashMap<>();
/* 62 */       $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 63 */       $_dependencies.put("entity", entity);
/* 64 */       $_dependencies.put("world", world);
/* 65 */       ProcedureSukunahikona.executeProcedure($_dependencies);
/*    */     
/*    */     }
/* 68 */     else if (!helmet.func_190926_b() && helmet.func_77973_b() == ItemBaryonCloak.helmet && 
/* 69 */       !chestplate.func_190926_b() && chestplate.func_77973_b() == ItemBaryonCloak.body && 
/* 70 */       is_pressed) {
/* 71 */       Map<String, Object> $_dependencies = new HashMap<>();
/* 72 */       $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 73 */       $_dependencies.put("entity", entity);
/* 74 */       $_dependencies.put("x", Integer.valueOf(x));
/* 75 */       $_dependencies.put("y", Integer.valueOf(y));
/* 76 */       $_dependencies.put("z", Integer.valueOf(z));
/* 77 */       $_dependencies.put("world", world);
/* 78 */       ProcedureFistOfPush.executeProcedure($_dependencies);
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSpecialJutsu1OnKeyPressedAddon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */