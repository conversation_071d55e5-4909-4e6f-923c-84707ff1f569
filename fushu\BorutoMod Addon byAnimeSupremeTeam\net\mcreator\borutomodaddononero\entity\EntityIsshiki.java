/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ import com.google.common.base.Predicate;
/*     */ import java.util.List;
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ModConfigAddon;
/*     */ import net.mcreator.borutomodaddononero.item.ItemIsshikiCloak;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityCreature;
/*     */ import net.minecraft.entity.EntityLiving;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.IEntityLivingData;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.EntityAIAttackMelee;
/*     */ import net.minecraft.entity.ai.EntityAIBase;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.entity.player.EntityPlayerMP;
/*     */ import net.minecraft.init.Biomes;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.init.SoundEvents;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.nbt.NBTTagCompound;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.EnumHand;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundCategory;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.util.math.BlockPos;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraft.world.BossInfo;
/*     */ import net.minecraft.world.BossInfoServer;
/*     */ import net.minecraft.world.DifficultyInstance;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.entity.EntityBijuManager;
/*     */ import net.narutomod.item.ItemSharingan;
/*     */ 
/*     */ @Tag
/*     */ public class EntityIsshiki extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 5;
/*     */   
/*     */   public EntityIsshiki(ElementsBorutomodaddononeroMod instance) {
/*  54 */     super(instance, 29);
/*     */   }
/*     */   public static final int ENTITYID_RANGED = 6;
/*     */   
/*     */   public void initElements() {
/*  59 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(EntityCustom.class).id(new ResourceLocation("borutomodaddononero", "isshiki"), 5).name("isshiki").tracker(64, 3, true).egg(16777215, 8421504).build());
/*     */ 
/*     */     
/*  62 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(Entity4MobAppearance.class).id(new ResourceLocation("borutomodaddononero", "isshiki_mob_appearance"), 6).name("isshiki_mob_appearance").tracker(64, 1, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(FMLInitializationEvent event) {
/*  69 */     int i = MathHelper.func_76125_a(ModConfigAddon.SPAWN_WEIGHT_ISSHIKI, 0, 20);
/*  70 */     if (i > 0) {
/*  71 */       EntityRegistry.addSpawn(EntityCustom.class, i, 1, 1, EnumCreatureType.MONSTER, new Biome[] { Biomes.field_76767_f, Biomes.field_76768_g, Biomes.field_76780_h, Biomes.field_76781_i, Biomes.field_76785_t, Biomes.field_76784_u, Biomes.field_76782_w, Biomes.field_76792_x, Biomes.field_150583_P, Biomes.field_150582_Q, Biomes.field_150585_R, Biomes.field_150588_X, Biomes.field_76770_e, Biomes.field_185444_T, Biomes.field_150590_f, Biomes.field_150599_m, Biomes.field_185446_X, Biomes.field_185447_Y, Biomes.field_185448_Z, Biomes.field_185429_aa, Biomes.field_185430_ab, Biomes.field_185435_ag, Biomes.field_185443_S, Biomes.field_185434_af, Biomes.field_150580_W });
/*     */     }
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public static class EntityCustom
/*     */     extends EntityNinjaMobAddon.Base
/*     */     implements IMob, IRangedAttackMob
/*     */   {
/*  83 */     private final double INVIS_CHAKRA = 20.0D;
/*     */     private boolean isReal;
/*     */     private int lookedAtTime;
/*     */     private int lastInvisTime;
/*     */     private EntityLivingBase targetEntity;
/*  88 */     private int lastSpearTime = 0;
/*     */     
/*     */     private boolean cloakRemoved = false;
/*  91 */     private final BossInfoServer bossInfo = new BossInfoServer(func_145748_c_(), BossInfo.Color.RED, BossInfo.Overlay.PROGRESS);
/*     */     
/*     */     public EntityCustom(World world) {
/*  94 */       super(world, 120, 7000.0D);
/*     */       
/*  96 */       this.field_70178_ae = true;
/*     */     }
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */     
/*     */     public IEntityLivingData func_180482_a(DifficultyInstance difficulty, @Nullable IEntityLivingData livingdata) {
/* 104 */       func_184201_a(EntityEquipmentSlot.CHEST, new ItemStack(ItemIsshikiCloak.body, 1));
/*     */ 
/*     */       
/* 107 */       setIsReal((this.field_70146_Z.nextInt(ModConfigAddon.ISSHIKI_REAL_CHANCE) == 0));
/* 108 */       return super.func_180482_a(difficulty, livingdata);
/*     */     }
/*     */     
/*     */     public void setIsReal(boolean real) {
/* 112 */       this.isReal = real;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_110147_ax() {
/* 117 */       super.func_110147_ax();
/* 118 */       func_110148_a(SharedMonsterAttributes.field_188791_g).func_111128_a(100.0D);
/* 119 */       func_110148_a(SharedMonsterAttributes.field_111263_d).func_111128_a(0.5D);
/* 120 */       func_110148_a(SharedMonsterAttributes.field_111264_e).func_111128_a(10.0D);
/* 121 */       func_110148_a(SharedMonsterAttributes.field_111267_a).func_111128_a(500.0D);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_184651_r() {
/* 126 */       super.func_184651_r();
/* 127 */       this.field_70714_bg.func_75776_a(0, (EntityAIBase)new EntityAISwimming((EntityLiving)this));
/* 128 */       this.field_70714_bg.func_75776_a(2, new EntityNinjaMobAddon.AILeapAtTarget((EntityLiving)this, 1.0F)
/*     */           {
/*     */             public boolean func_75250_a() {
/* 131 */               return (super.func_75250_a() && !EntityIsshiki.EntityCustom.this.func_184218_aH() && 
/* 132 */                 (EntityIsshiki.EntityCustom.this.func_70638_az()).field_70163_u - EntityIsshiki.EntityCustom.this.field_70163_u > 3.0D);
/*     */             }
/*     */           });
/* 135 */       this.field_70714_bg.func_75776_a(3, (EntityAIBase)new EntityAIAttackMelee(this, 1.0D, true)
/*     */           {
/*     */             public boolean func_75250_a() {
/* 138 */               return (super.func_75250_a() && !EntityIsshiki.EntityCustom.this.func_184218_aH() && EntityIsshiki.EntityCustom.this
/* 139 */                 .func_70638_az().func_70032_d((Entity)EntityIsshiki.EntityCustom.this) <= 4.0D);
/*     */             }
/*     */           });
/*     */       
/* 143 */       this.field_70714_bg.func_75776_a(4, (EntityAIBase)new EntityAIWatchClosest2((EntityLiving)this, EntityPlayer.class, 15.0F, 1.0F));
/* 144 */       this.field_70714_bg.func_75776_a(5, (EntityAIBase)new EntityAIWander(this, 0.3D));
/* 145 */       this.field_70714_bg.func_75776_a(6, (EntityAIBase)new EntityAILookIdle((EntityLiving)this));
/* 146 */       this.field_70715_bh.func_75776_a(1, (EntityAIBase)new EntityAIHurtByTarget(this, false, new Class[0]));
/* 147 */       this.field_70715_bh.func_75776_a(2, (EntityAIBase)new EntityAINearestAttackableTarget(this, EntityPlayer.class, 10, true, false, new Predicate<EntityPlayer>()
/*     */             {
/*     */               public boolean apply(@Nullable EntityPlayer p_apply_1_) {
/* 150 */                 return (p_apply_1_ != null && (ModConfigAddon.AGGRESSIVE_BOSSES || 
/*     */                   
/* 152 */                   ItemSharingan.wearingAny((EntityLivingBase)p_apply_1_) || EntityBijuManager.isJinchuriki(p_apply_1_)));
/*     */               }
/*     */             }));
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70619_bc() {
/* 159 */       super.func_70619_bc();
/* 160 */       EntityLivingBase target = func_70638_az();
/* 161 */       if (target != null && target.func_70089_S()) {
/* 162 */         double distanceToTarget = func_70032_d((Entity)target);
/* 163 */         if (this.field_70173_aa > this.lastSpearTime + 10 && distanceToTarget > 5.0D && distanceToTarget <= 30.0D) {
/* 164 */           attackWithSpears(target);
/* 165 */           this.lastSpearTime = this.field_70173_aa;
/*     */         } 
/* 167 */         if (equals((ProcedureUtils.objectEntityLookingAt((Entity)target, 24.0D)).field_72308_g)) {
/* 168 */           this.lookedAtTime++;
/*     */         } else {
/* 170 */           this.lookedAtTime = 0;
/*     */         } 
/* 172 */       } else if (this.peacefulTicks > 200) {
/* 173 */         func_70624_b(target = null);
/*     */       } 
/*     */     }
/*     */     
/*     */     private void attackWithSpears(EntityLivingBase target) {
/* 178 */       int numberOfSpears = this.field_70146_Z.nextInt(5) + 1;
/* 179 */       boolean useSpecialSpear = (this.field_70146_Z.nextFloat() < 0.1D);
/* 180 */       if (useSpecialSpear) {
/* 181 */         useGroundSpears(target);
/*     */       } else {
/* 183 */         for (int i = 0; i < numberOfSpears; i++) {
/* 184 */           EntitySamidareSpear.SamidareSpears spear = new EntitySamidareSpear.SamidareSpears(this.field_70170_p, (EntityLivingBase)this);
/* 185 */           Vec3d startVec = func_174824_e(1.0F);
/* 186 */           Vec3d targetVec = target.func_174824_e(1.0F);
/* 187 */           double dirX = targetVec.field_72450_a - startVec.field_72450_a;
/* 188 */           double dirY = targetVec.field_72448_b - startVec.field_72448_b;
/* 189 */           double dirZ = targetVec.field_72449_c - startVec.field_72449_c;
/* 190 */           double offsetX = (this.field_70146_Z.nextDouble() - 0.5D) * 0.5D;
/* 191 */           double offsetY = (this.field_70146_Z.nextDouble() - 0.5D) * 0.5D;
/* 192 */           double offsetZ = (this.field_70146_Z.nextDouble() - 0.5D) * 0.5D;
/* 193 */           spear.func_70107_b(startVec.field_72450_a + offsetX, startVec.field_72448_b + offsetY, startVec.field_72449_c + offsetZ);
/* 194 */           spear.func_70186_c(dirX, dirY, dirZ, 1.6F, 0.5F);
/* 195 */           this.field_70170_p.func_72838_d(spear);
/*     */         } 
/* 197 */         this.field_70170_p.func_184148_a(null, this.field_70165_t, this.field_70163_u, this.field_70161_v, (SoundEvent)SoundEvent.field_187505_a
/* 198 */             .func_82594_a(new ResourceLocation("borutomodaddononero:samidare_spear")), SoundCategory.HOSTILE, 1.0F, 1.0F);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     private void useGroundSpears(EntityLivingBase target) {
/* 204 */       World world = this.field_70170_p;
/* 205 */       world.func_184148_a(null, this.field_70165_t, this.field_70163_u, this.field_70161_v, (SoundEvent)SoundEvent.field_187505_a
/* 206 */           .func_82594_a(new ResourceLocation("borutomodaddononero:samidare_spear")), SoundCategory.HOSTILE, 50.0F, 1.0F);
/*     */       
/* 208 */       BlockPos targetPos = new BlockPos(target.field_70165_t, target.field_70163_u, target.field_70161_v);
/* 209 */       for (int i = 0; i < 5; i++) {
/* 210 */         double offsetX = (this.field_70146_Z.nextDouble() - 0.5D) * 6.0D;
/* 211 */         double offsetZ = (this.field_70146_Z.nextDouble() - 0.5D) * 6.0D;
/* 212 */         BlockPos spawnPos = targetPos.func_177963_a(offsetX, 0.0D, offsetZ);
/* 213 */         BlockPos groundPos = world.func_175645_m(spawnPos);
/* 214 */         EntitySpear.Base spear = new EntitySpear.Base(world);
/* 215 */         spear.func_70107_b(groundPos.func_177958_n(), groundPos.func_177956_o() + 0.5D, groundPos.func_177952_p());
/* 216 */         world.func_72838_d(spear);
/*     */       } 
/* 218 */       double radius = 1.7D;
/* 219 */       List<Entity> entitiesInArea = world.func_72872_a(Entity.class, new AxisAlignedBB(targetPos
/* 220 */             .func_177963_a(-radius, -radius, -radius), targetPos
/* 221 */             .func_177963_a(radius, radius, radius)));
/* 222 */       for (Entity nearbyEntity : entitiesInArea) {
/* 223 */         if (nearbyEntity instanceof EntityLivingBase) {
/* 224 */           EntityLivingBase livingTarget = (EntityLivingBase)nearbyEntity;
/* 225 */           livingTarget.func_70097_a(DamageSource.func_76358_a((EntityLivingBase)this), 10.0F);
/* 226 */           livingTarget.func_70690_d(new PotionEffect(PotionParalysis.potion, 300, 1, false, false));
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_184610_a(boolean wasRecentlyHit, int lootingModifier, DamageSource source) {
/* 233 */       func_70099_a(new ItemStack(ItemIsshikiCloak.body, 1), 0.0F);
/* 234 */       if (this.field_70146_Z.nextFloat() < 0.6F) {
/* 235 */         func_70099_a(new ItemStack(ItemKokugan.helmet, 1), 0.0F);
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184615_bR() {
/* 241 */       return SoundEvents.field_193786_de;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_184191_r(Entity entityIn) {
/* 246 */       return (super.func_184191_r(entityIn) || EntityNinjaMobAddon.TeamOtsutsuki.contains(entityIn.getClass()));
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70097_a(DamageSource source, float amount) {
/* 251 */       if (source == DamageSource.field_76379_h) {
/* 252 */         return false;
/*     */       }
/* 254 */       if (!this.field_70170_p.field_72995_K) {
/* 255 */         boolean ret = true;
/* 256 */         Entity entity1 = source.func_76346_g();
/* 257 */         if (this.field_70146_Z.nextInt(3) <= 1) {
/* 258 */           func_70634_a(this.field_70165_t + (this.field_70146_Z.nextDouble() - 0.5D) * 2.0D, this.field_70163_u, this.field_70161_v + (this.field_70146_Z.nextDouble() - 0.5D) * 2.0D);
/* 259 */           ret = false;
/* 260 */         } else if (this.field_70173_aa > this.lastInvisTime + 200 && getChakra() >= 20.0D) {
/* 261 */           func_70690_d(new PotionEffect(MobEffects.field_76441_p, 200, 1, false, false));
/* 262 */           ItemStack oldCloak = func_184582_a(EntityEquipmentSlot.CHEST);
/* 263 */           func_184201_a(EntityEquipmentSlot.CHEST, ItemStack.field_190927_a);
/* 264 */           EntitySukunahikona.Sukunahikona sukunahikonaEntity = new EntitySukunahikona.Sukunahikona((EntityLivingBase)this, 0.2F);
/* 265 */           sukunahikonaEntity.func_70012_b(this.field_70165_t, this.field_70163_u, this.field_70161_v, this.field_70177_z, this.field_70125_A);
/* 266 */           sukunahikonaEntity.func_82142_c(true);
/* 267 */           this.field_70170_p.func_184148_a(null, this.field_70165_t, this.field_70163_u, this.field_70161_v, (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("borutomodaddononero:sukunahikona")), SoundCategory.NEUTRAL, 1.0F, 1.0F);
/* 268 */           this.field_70170_p.func_72838_d((Entity)sukunahikonaEntity);
/* 269 */           func_184220_m((Entity)sukunahikonaEntity);
/* 270 */           consumeChakra(20.0D);
/* 271 */           this.lastInvisTime = this.field_70173_aa;
/* 272 */           if (!func_70644_a(MobEffects.field_76441_p)) {
/* 273 */             func_184210_p();
/* 274 */             sukunahikonaEntity.func_70106_y();
/* 275 */             func_184201_a(EntityEquipmentSlot.CHEST, oldCloak);
/*     */           } 
/*     */         } 
/* 278 */         if (!ret) {
/* 279 */           if (entity1 instanceof EntityLivingBase) {
/* 280 */             func_70604_c((EntityLivingBase)entity1);
/*     */           }
/* 282 */           return false;
/*     */         } 
/* 284 */         if (entity1 != null && entity1 instanceof EntityLivingBase && !entity1.equals(this)) {
/* 285 */           if (entity1 instanceof EntityPlayer && ((EntityPlayer)entity1).func_184812_l_()) {
/* 286 */             return false;
/*     */           }
/* 288 */           this.targetEntity = (EntityLivingBase)entity1;
/* 289 */           func_184609_a(EnumHand.MAIN_HAND);
/* 290 */           func_70652_k((Entity)this.targetEntity);
/*     */         } 
/*     */       } 
/* 293 */       return super.func_70097_a(source, amount);
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70601_bi() {
/* 298 */       return (super.func_70601_bi() && this.field_70170_p
/* 299 */         .func_175644_a(EntityCustom.class, EntitySelectors.field_94557_a).isEmpty() && 
/* 300 */         !EntityNinjaMobAddon.SpawnData.spawnedRecentlyHere(this, 36000L));
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void func_184724_a(boolean swingingArms) {}
/*     */ 
/*     */ 
/*     */     
/*     */     public void func_82196_d(EntityLivingBase target, float distanceFactor) {}
/*     */ 
/*     */     
/*     */     public void func_184203_c(EntityPlayerMP player) {
/* 313 */       super.func_184203_c(player);
/*     */       
/* 315 */       if (this.bossInfo.func_186757_c().contains(player)) {
/* 316 */         this.bossInfo.func_186761_b(player);
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/* 322 */       super.func_70071_h_();
/* 323 */       trackAttackedPlayers();
/* 324 */       this.bossInfo.func_186735_a(func_110143_aJ() / func_110138_aP());
/* 325 */       if (func_110143_aJ() <= func_110138_aP() / 2.0D && !this.cloakRemoved) {
/* 326 */         if (!func_184582_a(EntityEquipmentSlot.CHEST).func_190926_b()) {
/* 327 */           func_184201_a(EntityEquipmentSlot.CHEST, ItemStack.field_190927_a);
/* 328 */           this.field_70170_p.func_184148_a(null, this.field_70165_t, this.field_70163_u, this.field_70161_v, SoundEvents.field_187728_s, SoundCategory.HOSTILE, 1.0F, 1.0F);
/* 329 */           this.cloakRemoved = true;
/*     */         } 
/* 331 */         if (this.field_70146_Z.nextFloat() < 0.2D) {
/* 332 */           activateDaikokutenCubeOnTarget();
/*     */         }
/*     */       } 
/* 335 */       if (!func_70644_a(MobEffects.field_76441_p)) {
/* 336 */         func_184210_p();
/* 337 */         if (func_184582_a(EntityEquipmentSlot.CHEST).func_190926_b() && !this.cloakRemoved) {
/* 338 */           ItemStack oldCloak = new ItemStack(ItemIsshikiCloak.body);
/* 339 */           func_184201_a(EntityEquipmentSlot.CHEST, oldCloak);
/*     */         } 
/* 341 */         if (func_70644_a(MobEffects.field_76441_p)) {
/* 342 */           func_184589_d(MobEffects.field_76441_p);
/*     */         }
/*     */       } 
/* 345 */       if (this.targetEntity != null && this.targetEntity.func_70089_S() && 
/* 346 */         func_70032_d((Entity)this.targetEntity) <= 4.0D) {
/* 347 */         func_70652_k((Entity)this.targetEntity);
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     private void activateDaikokutenCubeOnTarget() {
/* 353 */       if (this.targetEntity != null && this.targetEntity.func_70089_S()) {
/* 354 */         World world = this.field_70170_p;
/* 355 */         BlockPos targetPos = new BlockPos(this.targetEntity.field_70165_t, this.targetEntity.field_70163_u, this.targetEntity.field_70161_v);
/* 356 */         for (int i = 0; i < 5; i++) {
/* 357 */           if (this.field_70146_Z.nextFloat() < 0.2D) {
/* 358 */             double offsetX = (this.field_70146_Z.nextDouble() - 0.5D) * 6.0D;
/* 359 */             double offsetZ = (this.field_70146_Z.nextDouble() - 0.5D) * 6.0D;
/* 360 */             BlockPos spawnPos = targetPos.func_177963_a(offsetX, 0.0D, offsetZ);
/* 361 */             EntityDaikokutenCube.DaikokutenCube cubeEntity = new EntityDaikokutenCube.DaikokutenCube(world, spawnPos.func_177958_n(), (spawnPos.func_177956_o() + 10), spawnPos.func_177952_p());
/* 362 */             cubeEntity.setOwner((EntityLivingBase)this);
/* 363 */             world.func_72838_d(cubeEntity);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     private void trackAttackedPlayers() {
/* 370 */       EntityLivingBase entityLivingBase = func_94060_bK();
/* 371 */       if (entityLivingBase instanceof EntityPlayerMP || entityLivingBase = func_70638_az() instanceof EntityPlayerMP) {
/* 372 */         this.bossInfo.func_186760_a((EntityPlayerMP)entityLivingBase);
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     protected boolean canSeeInvisible(Entity entityIn) {
/* 378 */       return (!entityIn.func_82150_aj() || func_70068_e(entityIn) <= 400.0D);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70014_b(NBTTagCompound compound) {
/* 383 */       super.func_70014_b(compound);
/* 384 */       compound.func_74757_a("isReal", this.isReal);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70037_a(NBTTagCompound compound) {
/* 389 */       super.func_70037_a(compound);
/* 390 */       setIsReal(compound.func_74767_n("isReal"));
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70652_k(Entity entityIn) {
/* 395 */       boolean success = super.func_70652_k(entityIn);
/* 396 */       func_184609_a(EnumHand.MAIN_HAND);
/* 397 */       return success;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class Entity4MobAppearance extends EntityCustom {
/*     */     public Entity4MobAppearance(World worldIn) {
/* 403 */       super(worldIn);
/* 404 */       func_184201_a(EntityEquipmentSlot.HEAD, new ItemStack(ItemMangekyoSharingan.helmet));
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   public void preInit(FMLPreInitializationEvent event) {
/* 410 */     (new Renderer()).register();
/*     */   }
/*     */   
/*     */   public static class Renderer
/*     */     extends EntityRendererRegister {
/*     */     @SideOnly(Side.CLIENT)
/*     */     public void register() {
/* 417 */       RenderingRegistry.registerEntityRenderingHandler(EntityIsshiki.EntityCustom.class, renderManager -> new RenderCustom(renderManager));
/*     */     }
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public class RenderCustom extends EntityNinjaMobAddon.RenderBase<EntityIsshiki.EntityCustom> {
/* 422 */       private final ResourceLocation texture = new ResourceLocation("borutomodaddononero:textures/isshiki_boss.png");
/*     */       
/*     */       public RenderCustom(RenderManager renderManagerIn) {
/* 425 */         super(renderManagerIn, new EntityIsshiki.ModelIsshiki());
/*     */       }
/*     */ 
/*     */       
/*     */       protected void renderLayers(EntityIsshiki.EntityCustom entity, float f0, float f1, float f2, float f3, float f4, float f5, float f6) {
/* 430 */         if (!entity.func_82150_aj()) {
/* 431 */           super.func_177093_a((EntityLivingBase)entity, f0, f1, f2, f3, f4, f5, f6);
/*     */         }
/*     */       }
/*     */ 
/*     */       
/*     */       protected void preRenderCallback(EntityIsshiki.EntityCustom entity, float partialTickTime) {
/* 437 */         GlStateManager.func_179152_a(0.9375F, 0.9375F, 0.9375F);
/* 438 */         if (entity.func_82150_aj() || (entity.func_184218_aH() && entity.func_184187_bx() instanceof EntitySukunahikona.Sukunahikona)) {
/* 439 */           this.field_76989_e = 0.0F;
/*     */         } else {
/* 441 */           this.field_76989_e = 0.5F;
/*     */         } 
/*     */       }
/*     */ 
/*     */       
/*     */       public void func_82422_c() {
/* 447 */         GlStateManager.func_179109_b(0.0F, 0.1875F, 0.0F);
/*     */       }
/*     */ 
/*     */       
/*     */       protected ResourceLocation getEntityTexture(EntityIsshiki.EntityCustom entity) {
/* 452 */         return this.texture;
/*     */       }
/*     */     }
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class ModelIsshiki
/*     */     extends EntityNinjaMobAddon.ModelOtsutsuki
/*     */   {
/*     */     private final ModelRenderer HatLayer_r1;
/*     */     private final ModelRenderer HatLayer_r2;
/*     */     private final ModelRenderer HatLayer_r3;
/*     */     private final ModelRenderer HatLayer_r4;
/*     */     private final ModelRenderer HatLayer_r5;
/*     */     
/*     */     public ModelIsshiki() {
/* 468 */       this.field_78090_t = 1024;
/* 469 */       this.field_78089_u = 256;
/*     */       
/* 471 */       this.field_78116_c = new ModelRenderer((ModelBase)this);
/* 472 */       this.field_78116_c.func_78793_a(0.0F, 0.0F, 0.0F);
/* 473 */       this.field_78116_c.field_78804_l.add(new ModelBox(this.field_78116_c, 913, 29, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.0F, false));
/* 474 */       this.field_78116_c.field_78804_l.add(new ModelBox(this.field_78116_c, 945, 29, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.1F, false));
/* 475 */       this.field_78116_c.field_78804_l.add(new ModelBox(this.field_78116_c, 512, 0, -64.0F, -68.2F, -63.95F, 128, 128, 128, -59.9F, false));
/* 476 */       this.field_78116_c.field_78804_l.add(new ModelBox(this.field_78116_c, 968, 1, 3.8F, -8.7F, -4.2F, 1, 3, 9, -0.2F, false));
/*     */       
/* 478 */       this.HatLayer_r1 = new ModelRenderer((ModelBase)this);
/* 479 */       this.HatLayer_r1.func_78793_a(-0.3417F, -8.7432F, 2.2F);
/* 480 */       this.field_78116_c.func_78792_a(this.HatLayer_r1);
/* 481 */       setRotationAngle(this.HatLayer_r1, 0.0F, 0.0F, 0.0262F);
/* 482 */       this.HatLayer_r1.field_78804_l.add(new ModelBox(this.HatLayer_r1, 988, 6, -3.8708F, -0.0728F, 1.7F, 9, 3, 1, -0.2F, false));
/*     */       
/* 484 */       this.HatLayer_r2 = new ModelRenderer((ModelBase)this);
/* 485 */       this.HatLayer_r2.func_78793_a(-0.3417F, -8.7432F, 2.2F);
/* 486 */       this.field_78116_c.func_78792_a(this.HatLayer_r2);
/* 487 */       setRotationAngle(this.HatLayer_r2, 0.0F, 0.0F, 0.3491F);
/* 488 */       this.HatLayer_r2.field_78804_l.add(new ModelBox(this.HatLayer_r2, 1010, 26, -6.1718F, -3.9833F, -2.2081F, 1, 2, 1, 0.0F, false));
/*     */       
/* 490 */       this.HatLayer_r3 = new ModelRenderer((ModelBase)this);
/* 491 */       this.HatLayer_r3.func_78793_a(-0.3417F, -8.7432F, 2.2F);
/* 492 */       this.field_78116_c.func_78792_a(this.HatLayer_r3);
/* 493 */       setRotationAngle(this.HatLayer_r3, 0.0F, 0.0F, -0.0436F);
/* 494 */       this.HatLayer_r3.field_78804_l.add(new ModelBox(this.HatLayer_r3, 1000, 24, -5.5234F, -4.3109F, -2.7081F, 2, 3, 2, -0.1F, false));
/*     */       
/* 496 */       this.HatLayer_r4 = new ModelRenderer((ModelBase)this);
/* 497 */       this.HatLayer_r4.func_78793_a(-0.3417F, -8.7432F, 2.2F);
/* 498 */       this.field_78116_c.func_78792_a(this.HatLayer_r4);
/* 499 */       setRotationAngle(this.HatLayer_r4, 0.0F, 0.0F, -0.4363F);
/* 500 */       this.HatLayer_r4.field_78804_l.add(new ModelBox(this.HatLayer_r4, 986, 22, -4.9136F, -3.5389F, -3.2081F, 3, 4, 3, 0.0F, false));
/*     */       
/* 502 */       this.HatLayer_r5 = new ModelRenderer((ModelBase)this);
/* 503 */       this.HatLayer_r5.func_78793_a(-0.3417F, -8.7432F, 2.2F);
/* 504 */       this.field_78116_c.func_78792_a(this.HatLayer_r5);
/* 505 */       setRotationAngle(this.HatLayer_r5, -0.0262F, 0.0F, 0.0F);
/* 506 */       this.HatLayer_r5.field_78804_l.add(new ModelBox(this.HatLayer_r5, 1010, 0, -4.4583F, -0.2224F, -3.3417F, 1, 3, 6, -0.2F, false));
/*     */       
/* 508 */       this.field_78115_e = new ModelRenderer((ModelBase)this);
/* 509 */       this.field_78115_e.func_78793_a(0.0F, 0.0F, 0.0F);
/* 510 */       this.field_78115_e.field_78804_l.add(new ModelBox(this.field_78115_e, 913, 13, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.0F, false));
/* 511 */       this.field_78115_e.field_78804_l.add(new ModelBox(this.field_78115_e, 916, 0, -4.0F, 0.0F, -2.0F, 8, 12, 1, 0.1F, false));
/*     */       
/* 513 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 514 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 515 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 937, 13, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, false));
/*     */       
/* 517 */       this.field_178724_i = new ModelRenderer((ModelBase)this);
/* 518 */       this.field_178724_i.func_78793_a(5.0F, 2.0F, 0.0F);
/* 519 */       this.field_178724_i.field_78804_l.add(new ModelBox(this.field_178724_i, 937, 13, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, true));
/*     */       
/* 521 */       this.field_178721_j = new ModelRenderer((ModelBase)this);
/* 522 */       this.field_178721_j.func_78793_a(-1.9F, 12.0F, 0.0F);
/* 523 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 953, 13, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 524 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 969, 13, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.1F, false));
/*     */       
/* 526 */       this.field_178722_k = new ModelRenderer((ModelBase)this);
/* 527 */       this.field_178722_k.func_78793_a(1.9F, 12.0F, 0.0F);
/* 528 */       this.field_178722_k.field_78804_l.add(new ModelBox(this.field_178722_k, 953, 13, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, true));
/* 529 */       this.field_178722_k.field_78804_l.add(new ModelBox(this.field_178722_k, 969, 13, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.1F, true));
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 533 */       modelRenderer.field_78795_f = x;
/* 534 */       modelRenderer.field_78796_g = y;
/* 535 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityIsshiki.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */