{"credit": "Made with Blockbench", "texture_size": [64, 64], "elements": [{"from": [6, 0, 6], "to": [10, 6, 10], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5, 12.75, 7, 16], "texture": "#0"}, "east": {"uv": [4.75, 13, 6.75, 16], "texture": "#0"}, "south": {"uv": [4.5, 13, 6.5, 16], "texture": "#0"}, "west": {"uv": [5.5, 12.75, 7.5, 16], "texture": "#0"}, "up": {"uv": [6, 14.5, 5, 13.5], "texture": "#0"}, "down": {"uv": [7, 13.5, 6, 14.5], "texture": "#0"}}}, {"from": [6.25, 6, 6.25], "to": [9.75, 11.5, 9.75], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.25, 12.5, 6.75, 14.75], "texture": "#0"}, "east": {"uv": [5, 11.5, 6.75, 14], "texture": "#0"}, "south": {"uv": [6, 12.5, 7.5, 14.75], "texture": "#0"}, "west": {"uv": [5, 12.25, 6.75, 14.75], "texture": "#0"}, "up": {"uv": [6, 13.5, 5.25, 12.75], "texture": "#0"}, "down": {"uv": [6.75, 12.75, 6, 13.5], "texture": "#0"}}}, {"from": [6.5, 11.5, 6.5], "to": [9.5, 16.5, 9.5], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.25, 11.5, 6.75, 13.75], "texture": "#0"}, "east": {"uv": [4.5, 11.5, 6, 13.75], "texture": "#0"}, "south": {"uv": [6, 11.5, 7.5, 13.75], "texture": "#0"}, "west": {"uv": [6, 11.5, 7.5, 13.5], "texture": "#0"}, "up": {"uv": [6, 11.5, 5.25, 10.75], "texture": "#0"}, "down": {"uv": [6.75, 10.75, 6, 11.5], "texture": "#0"}}}, {"from": [6.75, 16.5, 6.75], "to": [9.25, 21, 9.25], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.5, 7.75, 6.75, 9.5], "texture": "#0"}, "east": {"uv": [5, 7.75, 6.5, 10], "texture": "#0"}, "south": {"uv": [5.75, 7.75, 7, 9.75], "texture": "#0"}, "west": {"uv": [5.25, 7.75, 6.5, 9.75], "texture": "#0"}, "up": {"uv": [6, 7.75, 5.5, 7.25], "texture": "#0"}, "down": {"uv": [6.5, 7.25, 6, 7.75], "texture": "#0"}}}, {"from": [7, 21, 7], "to": [9, 25, 9], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.5, 5.5, 6.5, 7], "texture": "#0"}, "east": {"uv": [5, 7.25, 6.5, 9.5], "texture": "#0"}, "south": {"uv": [5.75, 7.25, 7, 9.25], "texture": "#0"}, "west": {"uv": [5.5, 6.5, 6.5, 8.25], "texture": "#0"}, "up": {"uv": [6, 7.25, 5.5, 6.75], "texture": "#0"}, "down": {"uv": [6.5, 6.75, 6, 7.25], "texture": "#0"}}}, {"from": [7.25, 25, 7.25], "to": [8.75, 28, 8.75], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.75, 5.25, 6.75, 7], "texture": "#0"}, "east": {"uv": [5.5, 5.25, 6.5, 7], "texture": "#0"}, "south": {"uv": [5.5, 5.25, 6.5, 7], "texture": "#0"}, "west": {"uv": [5.25, 5.25, 6.25, 7.25], "texture": "#0"}, "up": {"uv": [6, 5.25, 5.75, 5], "texture": "#0"}, "down": {"uv": [6.25, 5, 6, 5.25], "texture": "#0"}}}, {"from": [7.5, 28, 7.5], "to": [8.5, 30, 8.5], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [5.75, 3.25, 6.5, 4.5], "texture": "#0"}, "east": {"uv": [5.5, 3.25, 6.25, 4.5], "texture": "#0"}, "south": {"uv": [5.75, 3.25, 6.5, 4.5], "texture": "#0"}, "west": {"uv": [5.5, 3.25, 6.25, 4.5], "texture": "#0"}, "up": {"uv": [6, 3.25, 5.75, 3], "texture": "#0"}, "down": {"uv": [6.25, 3, 6, 3.25], "texture": "#0"}}}, {"from": [7.75, 30, 7.75], "to": [8.25, 31, 8.25], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [6, 2.25, 6, 2.5], "texture": "#0"}, "east": {"uv": [6, 2.25, 6, 2.5], "texture": "#0"}, "south": {"uv": [6, 2.25, 6, 2.5], "texture": "#0"}, "west": {"uv": [6, 2.25, 6, 2.5], "texture": "#0"}, "up": {"uv": [6, 2.25, 6, 2.25], "texture": "#0"}, "down": {"uv": [6, 2.25, 6, 2.25], "texture": "#0"}}}, {"from": [7.85, 31, 7.85], "to": [8.15, 31.5, 8.15], "rotation": {"angle": 0, "axis": "y", "origin": [8, 16, 8]}, "faces": {"north": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}, "east": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}, "south": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}, "west": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}, "up": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}, "down": {"uv": [6, 1.75, 6, 1.75], "texture": "#0"}}}], "display": {"thirdperson_righthand": {"rotation": [0, 45, 0], "translation": [0, 1, 1.5], "scale": [0.2, 0.4, 0.2]}, "thirdperson_lefthand": {"rotation": [0, 45, 0], "translation": [0, 1, 1.5], "scale": [0.2, 0.4, 0.2]}, "firstperson_righthand": {"rotation": [0, 15, 0], "translation": [0, 2, 1.5], "scale": [0.2, 0.4, 0.2]}, "firstperson_lefthand": {"rotation": [0, 15, 0], "translation": [0, 2, 1.5], "scale": [0.2, 0.4, 0.2]}, "ground": {"scale": [0.2, 0.4, 0.2]}, "gui": {"rotation": [-30, -30, -56], "translation": [-3.25, -3.25, -0.25], "scale": [0.4, 0.7, 0.4]}, "fixed": {"rotation": [-37.5, 30, 55], "translation": [4.25, -4, -0.25], "scale": [0.5, 0.8, 0.5]}}}