package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntitySamidareSpear;
import net.narutomod.Chakra;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSamidareSpear extends ElementsNarutomodMod.ModElement {
	public ProcedureSamidareSpear(ElementsNarutomodMod instance) {
		super(instance, 664);
	}

	public static void executeProcedure(Map<String, Object> dependencies) {
		if (dependencies.get("is_pressed") == null) {
			System.err.println("Failed to load dependency is_pressed for procedure SamidareSpear!");
			return;
		}
		if (dependencies.get("entity") == null) {
			System.err.println("Failed to load dependency entity for procedure SamidareSpear!");
			return;
		}
		if (dependencies.get("world") == null) {
			System.err.println("Failed to load dependency world for procedure SamidareSpear!");
			return;
		}

		boolean is_pressed = (Boolean) dependencies.get("is_pressed");
		Entity entity = (Entity) dependencies.get("entity");
		World world = (World) dependencies.get("world");

		if (is_pressed && entity instanceof EntityPlayer) {
			EntityPlayer player = (EntityPlayer) entity;
			if (Chakra.pathway(player).getAmount() >= 150.0D) {
				// 服务器端执行
				if (!world.isRemote) {
					Chakra.pathway(player).consume(150.0D);

					// 安全的音效播放
					try {
						SoundEvent soundEvent = SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:samidare_spear_sound"));
						if (soundEvent != null) {
							world.playSound(null, entity.posX, entity.posY, entity.posZ, soundEvent, SoundCategory.NEUTRAL, 50.0F, 1.0F);
						}
					} catch (Exception e) {
						System.err.println("Failed to play samidare spear sound: " + e.getMessage());
					}

					try {
						EntitySamidareSpear.EC.Jutsu jutsu = new EntitySamidareSpear.EC.Jutsu();
						jutsu.createJutsu(player.getHeldItemMainhand(), player, 5.0F);
					} catch (Exception e) {
						System.err.println("Failed to create samidare spear jutsu: " + e.getMessage());
					}
				}
			}
		}
	}
}
