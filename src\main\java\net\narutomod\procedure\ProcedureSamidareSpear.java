package net.narutomod.procedure;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.entity.EntitySamidareSpear;
import net.narutomod.Chakra;
import net.narutomod.item.ItemIsshikiDojutsu;

import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.world.World;

import java.util.Map;

@ElementsNarutomodMod.ModElement.Tag
public class ProcedureSamidareSpear extends ElementsNarutomodMod.ModElement {
    
    public ProcedureSamidareSpear(ElementsNarutomodMod instance) {
        super(instance, 607);
    }
    
    public static void executeProcedure(Map<String, Object> dependencies) {
        if (dependencies.get("is_pressed") == null) {
            System.err.println("Failed to load dependency is_pressed for procedure <PERSON>dareSpear!");
            return;
        }
        if (dependencies.get("entity") == null) {
            System.err.println("Failed to load dependency entity for procedure SamidareSpear!");
            return;
        }
        if (dependencies.get("world") == null) {
            System.err.println("Failed to load dependency world for procedure SamidareSpear!");
            return;
        }
        
        boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
        Entity entity = (Entity)dependencies.get("entity");
        World world = (World)dependencies.get("world");
        
        if (!is_pressed || !(entity instanceof EntityPlayer)) {
            return;
        }
        
        EntityPlayer player = (EntityPlayer)entity;
        
        // 检查查克拉是否足够
        double requiredChakra = ItemIsshikiDojutsu.getSamidareSpearChakraUsage(player);
        if (Chakra.pathway(player).getAmount() >= requiredChakra) {
            // 消耗查克拉
            Chakra.pathway(player).consume(requiredChakra);
            
            // 播放音效
            world.playSound((EntityPlayer)null, entity.posX, entity.posY, entity.posZ, 
                SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:samidare_spear_isshiki")), 
                SoundCategory.NEUTRAL, 50.0F, 1.0F);
            
            // 创建雨五月长矛攻击
            EntitySamidareSpear.SamidareSpears.Jutsu jutsu = new EntitySamidareSpear.SamidareSpears.Jutsu();
            jutsu.createJutsu(player.getHeldItemMainhand(), (EntityLivingBase)player, 5.0F);
        }
    }
}
