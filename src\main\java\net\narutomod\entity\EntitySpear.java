package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;
import net.minecraft.client.model.ModelBase;
import net.minecraft.client.model.ModelBox;
import net.minecraft.client.model.ModelRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.entity.Render;
import net.minecraft.client.renderer.entity.RenderManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.network.datasync.DataParameter;
import net.minecraft.network.datasync.DataSerializers;
import net.minecraft.network.datasync.EntityDataManager;
import net.minecraft.util.ResourceLocation;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.registry.EntityEntry;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import javax.annotation.Nullable;

@ElementsNarutomodMod.ModElement.Tag
public class EntitySpear extends ElementsNarutomodMod.ModElement {
	public static final int ENTITYID = 662;

	public EntitySpear(ElementsNarutomodMod instance) {
		super(instance, 662);
	}

	@Override
	public void initElements() {
		elements.entities.add(() -> EntityEntryBuilder.create().entity(Base.class)
			.id(new ResourceLocation("narutomod", "spear"), ENTITYID)
			.name("spear").tracker(64, 3, true).build());
	}

	@Override
	public void preInit(FMLPreInitializationEvent event) {
		net.minecraftforge.fml.client.registry.RenderingRegistry.registerEntityRenderingHandler(Base.class, renderManager -> new Renderer<>(renderManager));
	}

	@Nullable
	public static Base spawnSpear(World worldIn, int color) {
		Base entity = new Base(worldIn, color);
		return worldIn.spawnEntity(entity) ? entity : null;
	}

	@Nullable
	public static Base spawnSpear(EntityLivingBase entityIn, int color) {
		Base entity = new Base(entityIn, color);
		return entityIn.world.spawnEntity(entity) ? entity : null;
	}

	public static class Base extends EntityScalableProjectile.Base {
		private static final DataParameter<Integer> COLOR = EntityDataManager.<Integer>createKey(Base.class, DataSerializers.VARINT);

		public Base(World worldIn) {
			super(worldIn);
			setOGSize(0.5F, 1.5F);
		}

		public Base(EntityLivingBase userIn) {
			super(userIn);
			setOGSize(0.5F, 1.5F);
		}

		public Base(EntityLivingBase userIn, int color) {
			this(userIn);
			setColor(color);
		}

		public Base(World worldIn, int color) {
			this(worldIn);
			setColor(color);
		}

		@Override
		protected void entityInit() {
			super.entityInit();
			dataManager.register(COLOR, -1);
		}

		protected int getColor() {
			return dataManager.get(COLOR);
		}

		protected void setColor(int color) {
			dataManager.set(COLOR, color);
		}

		@Override
		protected void onImpact(net.minecraft.util.math.RayTraceResult result) {
			// 基础实现，子类可以重写
			if (!this.world.isRemote) {
				this.setDead();
			}
		}
	}

	@SideOnly(Side.CLIENT)
	public static class Renderer<T extends Base> extends Render<T> {
		private static final ResourceLocation TEXTURE = new ResourceLocation("narutomod:textures/entity/spear.png");
		protected final ModelBase model;

		public Renderer(RenderManager renderManagerIn) {
			super(renderManagerIn);
			this.model = new ModelSpear();
		}

		@Override
		public void doRender(T entity, double x, double y, double z, float entityYaw, float partialTicks) {
			GlStateManager.pushMatrix();
			GlStateManager.translate((float)x, (float)y, (float)z);
			GlStateManager.enableRescaleNormal();
			float scale = entity.getEntityScale();
			GlStateManager.rotate(-entity.prevRotationYaw - (entity.rotationYaw - entity.prevRotationYaw) * partialTicks, 0.0F, 1.0F, 0.0F);
			GlStateManager.rotate(entity.prevRotationPitch + (entity.rotationPitch - entity.prevRotationPitch) * partialTicks - 180.0F, 1.0F, 0.0F, 0.0F);
			GlStateManager.scale(scale, scale, scale);
			int color = entity.getColor();
			float alpha = (color >> 24 & 0xFF) / 255.0F;
			float red = (color >> 16 & 0xFF) / 255.0F;
			float green = (color >> 8 & 0xFF) / 255.0F;
			float blue = (color & 0xFF) / 255.0F;
			if (alpha < 1.0F) {
				GlStateManager.enableBlend();
				GlStateManager.disableAlpha();
				GlStateManager.enableAlpha();
				GlStateManager.blendFunc(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
				GlStateManager.disableCull();
			}
			GlStateManager.color(red, green, blue, alpha);
			this.bindEntityTexture(entity);
			this.model.render(entity, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0625F);
			GlStateManager.color(1.0F, 1.0F, 1.0F, 1.0F);
			if (alpha < 1.0F) {
				GlStateManager.enableCull();
				GlStateManager.disableBlend();
				GlStateManager.enableAlpha();
				GlStateManager.disableRescaleNormal();
			}
			GlStateManager.popMatrix();
		}

		@Override
		protected ResourceLocation getEntityTexture(T entity) {
			return TEXTURE;
		}
	}

	@SideOnly(Side.CLIENT)
	public static class ModelSpear extends ModelBase {
		private final ModelRenderer bb_main;
		private final ModelRenderer cube_r1;
		private final ModelRenderer cube_r2;
		private final ModelRenderer cube_r3;
		private final ModelRenderer cube_r4;
		private final ModelRenderer cube_r5;
		private final ModelRenderer cube_r6;
		private final ModelRenderer cube_r7;
		private final ModelRenderer cube_r8;

		public ModelSpear() {
			textureWidth = 8;
			textureHeight = 8;

			bb_main = new ModelRenderer(this);
			bb_main.setRotationPoint(0.0F, 24.0F, 0.0F);
			bb_main.cubeList.add(new ModelBox(bb_main, 0, 3, -1.0F, -43.0F, -1.0F, 1, 3, 1, 0.3F, false));
			bb_main.cubeList.add(new ModelBox(bb_main, 0, 3, -1.0F, -37.0F, -1.0F, 1, 4, 1, 0.3F, false));
			bb_main.cubeList.add(new ModelBox(bb_main, 0, 3, -1.0F, -47.0F, -1.0F, 1, 4, 1, 0.3F, false));
			bb_main.cubeList.add(new ModelBox(bb_main, 0, 3, -1.0F, -40.0F, -1.0F, 1, 3, 1, 0.3F, false));

			cube_r1 = new ModelRenderer(this);
			cube_r1.setRotationPoint(-0.4414F, -31.7471F, -0.5414F);
			bb_main.addChild(cube_r1);
			setRotationAngle(cube_r1, 0.0F, 0.0F, -2.8362F);
			cube_r1.cubeList.add(new ModelBox(cube_r1, 0, 0, -0.1387F, -1.1686F, -0.4586F, 1, 2, 1, 0.3F, true));

			cube_r2 = new ModelRenderer(this);
			cube_r2.setRotationPoint(-0.4414F, -31.7471F, -0.5414F);
			bb_main.addChild(cube_r2);
			setRotationAngle(cube_r2, 0.0F, 0.0F, 2.7489F);
			cube_r2.cubeList.add(new ModelBox(cube_r2, 0, 0, -0.8851F, -1.103F, -0.4586F, 1, 2, 1, 0.3F, false));

			cube_r3 = new ModelRenderer(this);
			cube_r3.setRotationPoint(-0.4414F, -31.7471F, -0.5414F);
			bb_main.addChild(cube_r3);
			setRotationAngle(cube_r3, -1.5708F, -1.2654F, -1.5708F);
			cube_r3.cubeList.add(new ModelBox(cube_r3, 4, 0, -0.1387F, -1.1686F, -0.5414F, 1, 2, 1, 0.3F, true));

			cube_r4 = new ModelRenderer(this);
			cube_r4.setRotationPoint(-0.4414F, -31.7471F, -0.5414F);
			bb_main.addChild(cube_r4);
			setRotationAngle(cube_r4, 1.5708F, -1.1781F, 1.5708F);
			cube_r4.cubeList.add(new ModelBox(cube_r4, 4, 0, -0.8851F, -1.103F, -0.5414F, 1, 2, 1, 0.3F, false));

			cube_r5 = new ModelRenderer(this);
			cube_r5.setRotationPoint(-0.5F, -48.1949F, -0.9366F);
			bb_main.addChild(cube_r5);
			setRotationAngle(cube_r5, 1.5708F, -1.1781F, -1.5708F);
			cube_r5.cubeList.add(new ModelBox(cube_r5, 4, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, false));

			cube_r6 = new ModelRenderer(this);
			cube_r6.setRotationPoint(-0.5F, -48.2993F, -0.1461F);
			bb_main.addChild(cube_r6);
			setRotationAngle(cube_r6, -1.5708F, -1.2654F, 1.5708F);
			cube_r6.cubeList.add(new ModelBox(cube_r6, 4, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, true));

			cube_r7 = new ModelRenderer(this);
			cube_r7.setRotationPoint(-0.4747F, -48.3862F, -0.5F);
			bb_main.addChild(cube_r7);
			setRotationAngle(cube_r7, 0.0F, 0.0F, -0.3927F);
			cube_r7.cubeList.add(new ModelBox(cube_r7, 0, 0, -1.0F, -1.0F, -0.5F, 1, 2, 1, 0.3F, false));

			cube_r8 = new ModelRenderer(this);
			cube_r8.setRotationPoint(-0.1461F, -48.2993F, -0.5F);
			bb_main.addChild(cube_r8);
			setRotationAngle(cube_r8, 0.0F, 0.0F, 0.3054F);
			cube_r8.cubeList.add(new ModelBox(cube_r8, 0, 0, -0.5F, -1.0F, -0.5F, 1, 2, 1, 0.3F, true));
		}

		@Override
		public void render(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
			bb_main.render(f5);
		}

		public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
			modelRenderer.rotateAngleX = x;
			modelRenderer.rotateAngleY = y;
			modelRenderer.rotateAngleZ = z;
		}
	}
}
