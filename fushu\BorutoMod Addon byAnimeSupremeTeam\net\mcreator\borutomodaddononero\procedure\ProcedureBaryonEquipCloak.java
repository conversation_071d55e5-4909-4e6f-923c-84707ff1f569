/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemBaryonCloak;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.narutomod.entity.EntityBijuManager;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureBaryonEquipCloak
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureBaryonEquipCloak(ElementsBorutomodaddononeroMod instance) {
/* 16 */     super(instance, 108);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 20 */     if (dependencies.get("entity") == null) {
/* 21 */       System.err.println("Failed to load dependency entity for procedure EquipBaryonCloak!");
/*    */       return;
/*    */     } 
/* 24 */     if (dependencies.get("is_pressed") == null) {
/* 25 */       System.err.println("Failed to load dependency is_pressed for procedure EquipBaryonCloak!");
/*    */       return;
/*    */     } 
/* 28 */     Entity entity = (Entity)dependencies.get("entity");
/* 29 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/*    */     
/* 31 */     if (entity instanceof EntityPlayer) {
/* 32 */       EntityPlayer player = (EntityPlayer)entity;
/* 33 */       if (EntityBijuManager.getTails(player) == 9 && 
/* 34 */         EntityBijuManager.cloakLevel(player) == 2 && 
/* 35 */         EntityBijuManager.getCloakXp(player) >= 4800 && 
/* 36 */         is_pressed && player.func_70093_af()) {
/* 37 */         ItemStack helmet = (ItemStack)player.field_71071_by.field_70460_b.get(3);
/* 38 */         ItemStack chest = (ItemStack)player.field_71071_by.field_70460_b.get(2);
/* 39 */         ItemStack legs = (ItemStack)player.field_71071_by.field_70460_b.get(1);
/*    */ 
/*    */ 
/*    */         
/* 43 */         boolean hasFullBijuCloak = (helmet.func_77973_b().getRegistryName() != null && helmet.func_77973_b().getRegistryName().toString().contains("biju_cloakhelmet") && chest.func_77973_b().getRegistryName() != null && chest.func_77973_b().getRegistryName().toString().contains("biju_cloakbody") && legs.func_77973_b().getRegistryName() != null && legs.func_77973_b().getRegistryName().toString().contains("biju_cloaklegs"));
/* 44 */         if (hasFullBijuCloak) {
/* 45 */           player.field_71071_by.field_70460_b.set(3, ItemStack.field_190927_a);
/* 46 */           player.field_71071_by.field_70460_b.set(2, ItemStack.field_190927_a);
/* 47 */           player.field_71071_by.field_70460_b.set(1, ItemStack.field_190927_a);
/*    */         } 
/* 49 */         ItemStack baryonCloakHelmet = new ItemStack(ItemBaryonCloak.helmet);
/* 50 */         ItemStack baryonCloakBody = new ItemStack(ItemBaryonCloak.body);
/* 51 */         player.field_71071_by.field_70460_b.set(3, baryonCloakHelmet);
/* 52 */         player.field_71071_by.field_70460_b.set(2, baryonCloakBody);
/*    */         
/* 54 */         for (EntityBijuManager bm : EntityBijuManager.getBMList()) {
/* 55 */           if (player.equals(bm.getJinchurikiPlayer())) {
/* 56 */             int[] xps = bm.getCloakXPs();
/* 57 */             xps[0] = 0;
/* 58 */             xps[1] = 0;
/* 59 */             bm.setCloakXPs(xps);
/* 60 */             bm.markDirty();
/*    */             break;
/*    */           } 
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureBaryonEquipCloak.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */