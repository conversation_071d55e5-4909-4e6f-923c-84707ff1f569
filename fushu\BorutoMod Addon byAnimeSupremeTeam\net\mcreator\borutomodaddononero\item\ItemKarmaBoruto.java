/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ 
/*     */ import com.google.common.collect.ImmutableMap;
/*     */ import com.google.common.collect.Maps;
/*     */ import java.util.List;
/*     */ import java.util.Map;
/*     */ import java.util.Random;
/*     */ import java.util.UUID;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.entity.AbstractClientPlayer;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.client.util.ITooltipFlag;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*     */ import net.minecraft.entity.ai.attributes.IAttribute;
/*     */ import net.minecraft.entity.ai.attributes.IAttributeInstance;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.entity.player.EntityPlayerMP;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.nbt.NBTTagCompound;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.ActionResult;
/*     */ import net.minecraft.util.EnumActionResult;
/*     */ import net.minecraft.util.EnumHand;
/*     */ import net.minecraft.util.text.ITextComponent;
/*     */ import net.minecraft.util.text.TextComponentTranslation;
/*     */ import net.minecraft.util.text.TextFormatting;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.common.MinecraftForge;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*     */ import net.minecraftforge.fml.common.gameevent.PlayerEvent;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.Chakra;
/*     */ import net.narutomod.gui.overlay.OverlayChakraDisplay;
/*     */ import net.narutomod.item.ItemJutsu;
/*     */ import net.narutomod.item.ItemOnBody;
/*     */ import net.narutomod.procedure.ProcedureOnLeftClickEmpty;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ @Tag
/*     */ public class ItemKarmaBoruto
/*     */   extends ElementsBorutomodaddononeroMod.ModElement
/*     */ {
/*     */   @ObjectHolder("borutomodaddononero:karmaboruto")
/*  61 */   public static final Item block = null;
/*     */   private static final String BORUTOKARMA1MODEACTIVATEDKEY = "BorutoKarma1ModeActivated";
/*     */   private static final String BORUTOKARMA2MODEACTIVATEDKEY = "BorutoKarma2ModeActivated";
/*     */   private static final String SAGECHAKRADEPLETIONAMOUNT = "SageChakraDepletionAmount";
/*  65 */   public static final BorutoKarma1Mode BORUTOKARMA1MODE = new BorutoKarma1Mode();
/*  66 */   public static final ItemJutsu.JutsuEnum BORUTOKARMA2MODE = new ItemJutsu.JutsuEnum(0, "karma2", 'S', 10.0D, new BorutoKarma2Mode());
/*  67 */   private static final Random RAND = new Random();
/*     */   
/*     */   public ItemKarmaBoruto(ElementsBorutomodaddononeroMod instance) {
/*  70 */     super(instance, 710);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  75 */     this.elements.items.add(() -> new RangedItem(new ItemJutsu.JutsuEnum[] { BORUTOKARMA2MODE }));
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/*  81 */     ModelLoader.setCustomModelResourceLocation(block, 0, new ModelResourceLocation("borutomodaddononero:karmaboruto", "inventory"));
/*     */   }
/*     */ 
/*     */   
/*     */   public void init(FMLInitializationEvent event) {
/*  86 */     MinecraftForge.EVENT_BUS.register(new EventHook());
/*  87 */     ProcedureOnLeftClickEmpty.addQualifiedItem(block, EnumHand.MAIN_HAND);
/*     */   }
/*     */   
/*     */   public static class RangedItem extends ItemJutsu.Base implements ItemOnBody.Interface {
/*     */     private static final String TYPEKEY = "KarmaType";
/*  92 */     private static final Map<IAttribute, AttributeModifier> buffMap = (Map<IAttribute, AttributeModifier>)ImmutableMap.builder()
/*  93 */       .put(EntityPlayer.REACH_DISTANCE, new AttributeModifier(UUID.fromString("c3ee1250-8b80-4668-b58a-33af5ea73ee6"), "borutokarmamode.reach", 2.0D, 0))
/*  94 */       .put(SharedMonsterAttributes.field_111264_e, new AttributeModifier(UUID.fromString("6d6202e1-9aac-4c3d-ba0c-6684bdd58868"), "borutokarmamode.damage", 60.0D, 0))
/*  95 */       .put(SharedMonsterAttributes.field_188790_f, new AttributeModifier(UUID.fromString("33b7fa14-828a-4964-b014-b61863526589"), "borutokarmamode.damagespeed", 2.0D, 1))
/*  96 */       .put(SharedMonsterAttributes.field_111263_d, new AttributeModifier(UUID.fromString("74f3ab51-a73f-45e3-a4c4-aae6974b6414"), "borutokarmamode.movement", 1.5D, 1))
/*  97 */       .put(SharedMonsterAttributes.field_111267_a, new AttributeModifier(UUID.fromString("70e0acc2-cf75-4bbd-a21a-753088324a59"), "borutokarmamode.health", 80.0D, 0))
/*  98 */       .build();
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     private ModelBiped karma1Model;
/*     */     @SideOnly(Side.CLIENT)
/*     */     private ModelBiped karma2Model;
/*     */     
/*     */     public RangedItem(ItemJutsu.JutsuEnum... list) {
/* 106 */       super(ItemJutsu.JutsuEnum.Type.OTHER, list);
/* 107 */       func_77655_b("karmaboruto");
/* 108 */       setRegistryName("karmaboruto");
/*     */     }
/*     */ 
/*     */ 
/*     */     
/*     */     public void setSageType(ItemStack stack, ItemKarmaBoruto.Type type) {
/* 114 */       if (!stack.func_77942_o()) {
/* 115 */         stack.func_77982_d(new NBTTagCompound());
/*     */       }
/* 117 */       stack.func_77978_p().func_74768_a("KarmaType", type.getID());
/*     */     }
/*     */     
/*     */     public ItemKarmaBoruto.Type getSageType(ItemStack stack) {
/* 121 */       return stack.func_77942_o() ? ItemKarmaBoruto.Type.getTypeFromId(stack.func_77978_p().func_74762_e("KarmaType")) : ItemKarmaBoruto.Type.NONE;
/*     */     }
/*     */ 
/*     */     
/*     */     protected float getMaxPower(ItemStack stack, EntityLivingBase entity) {
/* 126 */       float f = super.getMaxPower(stack, entity);
/* 127 */       return Math.min(f, 100.0F);
/*     */     }
/*     */ 
/*     */     
/*     */     protected float getPower(ItemStack stack, EntityLivingBase entity, int timeLeft) {
/* 132 */       return getPower(stack, entity, timeLeft, 0.0F, 20.0F);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_77663_a(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
/* 137 */       super.func_77663_a(itemstack, world, entity, par4, par5);
/* 138 */       if (!world.field_72995_K && entity instanceof EntityPlayer) {
/* 139 */         EntityPlayer player = (EntityPlayer)entity;
/* 140 */         ItemKarmaBoruto.Type karmaBorutoType = getSageType(itemstack);
/* 141 */         if (karmaBorutoType == ItemKarmaBoruto.Type.NONE) {
/*     */ 
/*     */           
/* 144 */           ItemKarmaBoruto.Type forcedType = (itemstack.func_77942_o() && itemstack.func_77978_p().func_150297_b("Type", 8)) ? ItemKarmaBoruto.Type.getTypeFromName(itemstack.func_77978_p().func_74779_i("Type")) : (player.func_184812_l_() ? ItemKarmaBoruto.Type.random() : ItemKarmaBoruto.Type.NONE);
/* 145 */           if (forcedType != ItemKarmaBoruto.Type.NONE) {
/* 146 */             setSageType(itemstack, forcedType);
/* 147 */             enableJutsu(itemstack, ItemKarmaBoruto.BORUTOKARMA2MODE, true);
/* 148 */             karmaBorutoType = forcedType;
/*     */           } else {
/*     */             return;
/*     */           } 
/*     */         } 
/* 153 */         boolean isKarma1ModeActivated = ItemKarmaBoruto.isBorutoKarma1ModeActivated(itemstack);
/* 154 */         boolean isKarma2ModeActivated = ItemKarmaBoruto.isBorutoKarma2ModeActivated(itemstack);
/* 155 */         if (!isKarma1ModeActivated && !isKarma2ModeActivated) {
/* 156 */           ItemKarmaBoruto.activateBorutoKarma1Mode(itemstack, player);
/*     */         }
/* 158 */         boolean hasHealthModifier = player.func_110148_a(SharedMonsterAttributes.field_111267_a).func_180374_a(buffMap.get(SharedMonsterAttributes.field_111267_a));
/* 159 */         if (isKarma2ModeActivated && !hasHealthModifier) {
/* 160 */           for (Map.Entry<IAttribute, AttributeModifier> entry : buffMap.entrySet()) {
/* 161 */             IAttributeInstance attr = player.func_110148_a(entry.getKey());
/* 162 */             if (attr != null) {
/* 163 */               attr.func_111121_a(entry.getValue());
/*     */             }
/*     */           } 
/* 166 */         } else if (!isKarma2ModeActivated && hasHealthModifier) {
/* 167 */           for (Map.Entry<IAttribute, AttributeModifier> entry : buffMap.entrySet()) {
/* 168 */             IAttributeInstance attr = player.func_110148_a(entry.getKey());
/* 169 */             if (attr != null) {
/* 170 */               attr.func_188479_b(((AttributeModifier)entry.getValue()).func_111167_a());
/*     */             }
/*     */           } 
/*     */         } 
/* 174 */         if (isKarma2ModeActivated) {
/* 175 */           Chakra.PathwayPlayer pathwayPlayer = Chakra.pathway(player);
/* 176 */           if (pathwayPlayer.getAmount() < itemstack.func_77978_p().func_74769_h("SageChakraDepletionAmount")) {
/* 177 */             ItemKarmaBoruto.deactivateBorutoKarma2Mode(itemstack, (EntityLivingBase)player);
/* 178 */           } else if (player.field_70173_aa % 20 == 10) {
/* 179 */             player.func_70690_d(new PotionEffect(MobEffects.field_76443_y, 22, 0, false, false));
/* 180 */             pathwayPlayer.consume(50.0D);
/*     */           } 
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_77615_a(ItemStack itemstack, World world, EntityLivingBase entity, int timeLeft) {
/* 188 */       super.func_77615_a(itemstack, world, entity, timeLeft);
/*     */     }
/*     */ 
/*     */     
/*     */     public void onUsingTick(ItemStack stack, EntityLivingBase player, int timeLeft) {
/* 193 */       if (!player.field_70170_p.field_72995_K && getCurrentJutsu(stack) == ItemKarmaBoruto.BORUTOKARMA2MODE) {
/* 194 */         player.func_184602_cy();
/*     */       } else {
/* 196 */         super.onUsingTick(stack, player, timeLeft);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public ActionResult<ItemStack> func_77659_a(World world, EntityPlayer entity, EnumHand hand) {
/* 202 */       ItemStack stack = entity.func_184586_b(hand);
/* 203 */       ItemJutsu.JutsuEnum jutsu = getCurrentJutsu(stack);
/* 204 */       if (jutsu == ItemKarmaBoruto.BORUTOKARMA2MODE) {
/* 205 */         if (!ItemKarmaBoruto.isBorutoKarma1ModeActivated(stack)) {
/* 206 */           entity.func_146105_b((ITextComponent)new TextComponentTranslation("msg.karma2_requires_karma1", new Object[0]), true);
/* 207 */           return new ActionResult(EnumActionResult.FAIL, stack);
/* 208 */         }  if (!ItemKarmaBoruto.isBorutoKarma2ModeActivated(stack)) {
/* 209 */           ItemKarmaBoruto.activateBorutoKarma2Mode(stack, entity);
/* 210 */           return new ActionResult(EnumActionResult.SUCCESS, stack);
/*     */         } 
/*     */       } 
/* 213 */       return super.func_77659_a(world, entity, hand);
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean onLeftClickEntity(ItemStack itemstack, EntityPlayer attacker, Entity target) {
/* 218 */       if (attacker.equals(target) && (getSageType(itemstack) == ItemKarmaBoruto.Type.BORUTOKARMA1 || getSageType(itemstack) == ItemKarmaBoruto.Type.BORUTOKARMA2) && (
/* 219 */         ItemKarmaBoruto.isBorutoKarma1ModeActivated(itemstack) || ItemKarmaBoruto.isBorutoKarma2ModeActivated(itemstack))) {
/* 220 */         target = (ProcedureUtils.objectEntityLookingAt((Entity)attacker, ProcedureUtils.getReachDistance((EntityLivingBase)attacker), 3.0D)).field_72308_g;
/* 221 */         if (target == null) {
/* 222 */           target = (ProcedureUtils.objectEntityLookingAt((Entity)attacker, ProcedureUtils.getReachDistance((EntityLivingBase)attacker), 4.5D)).field_72308_g;
/* 223 */           if (target != null) {
/* 224 */             attacker.func_71059_n(target);
/*     */           }
/*     */         } 
/*     */       } 
/* 228 */       return super.onLeftClickEntity(itemstack, attacker, target);
/*     */     }
/*     */ 
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public void func_77624_a(ItemStack itemstack, World world, List<String> list, ITooltipFlag flag) {
/* 234 */       ItemKarmaBoruto.Type type = getSageType(itemstack);
/* 235 */       if (type != ItemKarmaBoruto.Type.NONE) {
/* 236 */         list.add(TextFormatting.BLUE + (new TextComponentTranslation("tooltip.karma.type", new Object[0])).func_150261_e() + type
/* 237 */             .getLocalizedName() + TextFormatting.RESET);
/*     */       }
/* 239 */       super.func_77624_a(itemstack, world, list, flag);
/*     */     }
/*     */ 
/*     */     
/*     */     @SideOnly(Side.CLIENT)
/*     */     public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/* 245 */       if (ItemKarmaBoruto.isBorutoKarma1ModeActivated(stack)) {
/* 246 */         if (this.karma1Model == null) {
/* 247 */           this.karma1Model = new ItemKarmaBoruto.ModelKarmaBoruto1();
/*     */         }
/* 249 */         this.karma1Model.field_78117_n = living.func_70093_af();
/* 250 */         this.karma1Model.field_78093_q = living.func_184218_aH();
/* 251 */         this.karma1Model.field_78091_s = living.func_70631_g_();
/*     */         
/* 253 */         return this.karma1Model;
/* 254 */       }  if (ItemKarmaBoruto.isBorutoKarma2ModeActivated(stack)) {
/* 255 */         if (this.karma2Model == null) {
/* 256 */           this.karma2Model = new ItemKarmaBoruto.ModelKarmaBoruto2();
/*     */         }
/* 258 */         this.karma2Model.field_78117_n = living.func_70093_af();
/* 259 */         this.karma2Model.field_78093_q = living.func_184218_aH();
/* 260 */         this.karma2Model.field_78091_s = living.func_70631_g_();
/* 261 */         return this.karma2Model;
/*     */       } 
/* 263 */       return null;
/*     */     }
/*     */ 
/*     */     
/*     */     public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/* 268 */       if (ItemKarmaBoruto.isBorutoKarma1ModeActivated(stack))
/* 269 */         return "borutomodaddononero:textures/borutokarmastage1.png"; 
/* 270 */       if (ItemKarmaBoruto.isBorutoKarma2ModeActivated(stack)) {
/* 271 */         return "borutomodaddononero:textures/borutokarmastage2.png";
/*     */       }
/* 273 */       return null;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean showSkinLayer() {
/* 278 */       return true;
/*     */     }
/*     */ 
/*     */     
/*     */     public ItemOnBody.BodyPart showOnBody() {
/* 283 */       return ItemOnBody.BodyPart.NONE;
/*     */     }
/*     */   }
/*     */   
/*     */   public static boolean isBorutoKarma1ModeActivated(ItemStack stack) {
/* 288 */     return (stack.func_77942_o() && stack.func_77978_p().func_74767_n("BorutoKarma1ModeActivated"));
/*     */   }
/*     */   
/*     */   public static boolean isBorutoKarma1ModeActivated(EntityPlayer entity) {
/* 292 */     ItemStack stack = ProcedureUtils.getMatchingItemStack(entity, block);
/* 293 */     return (stack != null && isBorutoKarma1ModeActivated(stack));
/*     */   }
/*     */   
/*     */   public static void deactivateBorutoKarma1Mode(ItemStack stack, EntityLivingBase entity) {
/* 297 */     if (stack.func_77942_o()) {
/* 298 */       Chakra.Pathway cp = Chakra.pathway(entity);
/* 299 */       double d = stack.func_77978_p().func_74769_h("SageChakraDepletionAmount");
/* 300 */       if (d > 0.0D && cp.getAmount() > d) {
/* 301 */         cp.consume(cp.getAmount() - d);
/*     */       }
/* 303 */       stack.func_77978_p().func_82580_o("BorutoKarma1ModeActivated");
/* 304 */       stack.func_77978_p().func_82580_o("SageChakraDepletionAmount");
/*     */     } 
/* 306 */     if (entity instanceof EntityPlayerMP) {
/* 307 */       OverlayChakraDisplay.ShowFlamesMessage.send((EntityPlayerMP)entity, false);
/*     */     }
/*     */   }
/*     */   
/*     */   public static void activateBorutoKarma1Mode(ItemStack stack, EntityPlayer player) {
/* 312 */     if (!stack.func_77942_o()) {
/* 313 */       stack.func_77982_d(new NBTTagCompound());
/*     */     }
/* 315 */     stack.func_77978_p().func_74757_a("BorutoKarma1ModeActivated", true);
/*     */   }
/*     */   
/*     */   public static boolean isBorutoKarma2ModeActivated(ItemStack stack) {
/* 319 */     return (stack.func_77942_o() && stack.func_77978_p().func_74767_n("BorutoKarma2ModeActivated"));
/*     */   }
/*     */   
/*     */   public static boolean isBorutoKarma2ModeActivated(EntityPlayer entity) {
/* 323 */     ItemStack stack = ProcedureUtils.getMatchingItemStack(entity, block);
/* 324 */     return (stack != null && isBorutoKarma2ModeActivated(stack));
/*     */   }
/*     */   
/*     */   public static void deactivateBorutoKarma2Mode(ItemStack stack, EntityLivingBase entity) {
/* 328 */     if (stack.func_77942_o()) {
/* 329 */       Chakra.Pathway cp = Chakra.pathway(entity);
/* 330 */       double d = stack.func_77978_p().func_74769_h("SageChakraDepletionAmount");
/* 331 */       if (d > 0.0D && cp.getAmount() > d) {
/* 332 */         cp.consume(cp.getAmount() - d);
/*     */       }
/* 334 */       stack.func_77978_p().func_82580_o("BorutoKarma2ModeActivated");
/* 335 */       stack.func_77978_p().func_82580_o("SageChakraDepletionAmount");
/*     */     } 
/* 337 */     if (entity instanceof EntityPlayerMP) {
/* 338 */       OverlayChakraDisplay.ShowFlamesMessage.send((EntityPlayerMP)entity, false);
/*     */     }
/*     */   }
/*     */   
/*     */   public static void activateBorutoKarma2Mode(ItemStack stack, EntityPlayer player) {
/* 343 */     if (!stack.func_77942_o()) {
/* 344 */       stack.func_77982_d(new NBTTagCompound());
/*     */     }
/* 346 */     if (isBorutoKarma1ModeActivated(stack)) {
/* 347 */       deactivateBorutoKarma1Mode(stack, (EntityLivingBase)player);
/*     */     }
/* 349 */     Chakra.PathwayPlayer pathwayPlayer = Chakra.pathway(player);
/* 350 */     stack.func_77978_p().func_74780_a("SageChakraDepletionAmount", pathwayPlayer.getAmount());
/* 351 */     pathwayPlayer.consume(-0.6F, true);
/* 352 */     stack.func_77978_p().func_74757_a("BorutoKarma2ModeActivated", true);
/* 353 */     for (Map.Entry<IAttribute, AttributeModifier> entry : (Iterable<Map.Entry<IAttribute, AttributeModifier>>)RangedItem.buffMap.entrySet()) {
/* 354 */       IAttributeInstance attr = player.func_110148_a(entry.getKey());
/* 355 */       if (attr != null) {
/* 356 */         attr.func_111121_a(entry.getValue());
/*     */       }
/*     */     } 
/* 359 */     if (player instanceof EntityPlayerMP)
/* 360 */       OverlayChakraDisplay.ShowFlamesMessage.send((EntityPlayerMP)player, true); 
/*     */   }
/*     */   
/*     */   public static class EventHook
/*     */   {
/*     */     @SubscribeEvent
/*     */     public void onPlayerLogin(PlayerEvent.PlayerLoggedInEvent event) {
/* 367 */       if (!event.player.field_70170_p.field_72995_K) {
/* 368 */         ItemStack stack = ProcedureUtils.getMatchingItemStack(event.player, ItemKarmaBoruto.block);
/* 369 */         if (stack != null && !ItemKarmaBoruto.isBorutoKarma1ModeActivated(stack)) {
/* 370 */           ItemKarmaBoruto.activateBorutoKarma1Mode(stack, event.player);
/*     */         }
/* 372 */         if (stack != null)
/* 373 */           ItemKarmaBoruto.deactivateBorutoKarma2Mode(stack, (EntityLivingBase)event.player); 
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   public static class BorutoKarma1Mode
/*     */     implements ItemJutsu.IJutsuCallback
/*     */   {
/*     */     public boolean createJutsu(ItemStack stack, EntityLivingBase entity, float power) {
/* 382 */       if (power >= 100.0F) {
/* 383 */         stack.func_77978_p().func_74757_a("BorutoKarma1ModeActivated", true);
/* 384 */         return true;
/*     */       } 
/* 386 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean isActivated(ItemStack stack) {
/* 391 */       return ItemKarmaBoruto.isBorutoKarma1ModeActivated(stack);
/*     */     }
/*     */   }
/*     */   
/*     */   public static class BorutoKarma2Mode
/*     */     implements ItemJutsu.IJutsuCallback {
/*     */     public boolean createJutsu(ItemStack stack, EntityLivingBase entity, float power) {
/* 398 */       if (power >= 100.0F) {
/* 399 */         Chakra.Pathway cp = Chakra.pathway(entity);
/* 400 */         stack.func_77978_p().func_74780_a("SageChakraDepletionAmount", cp.getAmount());
/* 401 */         float f = (stack.func_77973_b() == ItemKarmaBoruto.block) ? ((ItemKarmaBoruto.RangedItem)stack.func_77973_b()).getCurrentJutsuXpModifier(stack, entity) : 1.0F;
/* 402 */         cp.consume(-0.6F / f, true);
/* 403 */         stack.func_77978_p().func_74757_a("BorutoKarma2ModeActivated", true);
/* 404 */         if (entity instanceof EntityPlayerMP) {
/* 405 */           OverlayChakraDisplay.ShowFlamesMessage.send((EntityPlayerMP)entity, true);
/*     */         }
/* 407 */         return true;
/*     */       } 
/* 409 */       return false;
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean isActivated(ItemStack stack) {
/* 414 */       return ItemKarmaBoruto.isBorutoKarma2ModeActivated(stack);
/*     */     }
/*     */   }
/*     */   
/*     */   public enum Type {
/* 419 */     NONE("none", 0),
/* 420 */     BORUTOKARMA1("borutokarma1", 1),
/* 421 */     BORUTOKARMA2("borutokarma2", 2);
/*     */     
/*     */     private final String name;
/*     */     private final int id;
/* 425 */     private static final Map<Integer, Type> TYPES_BY_ID = Maps.newHashMap();
/* 426 */     private static final Map<String, Type> TYPES_BY_NAME = Maps.newHashMap();
/*     */     
/*     */     static {
/* 429 */       for (Type type : values()) {
/* 430 */         TYPES_BY_ID.put(Integer.valueOf(type.getID()), type);
/* 431 */         TYPES_BY_NAME.put(type.name, type);
/*     */       } 
/*     */     }
/*     */     
/*     */     Type(String s, int i) {
/* 436 */       this.name = s;
/* 437 */       this.id = i;
/*     */     }
/*     */     
/*     */     public String getLocalizedName() {
/* 441 */       return (new TextComponentTranslation("entity." + this.name + ".name", new Object[0])).func_150261_e();
/*     */     }
/*     */     
/*     */     public int getID() {
/* 445 */       return this.id;
/*     */     }
/*     */     
/*     */     public static Type random() {
/* 449 */       return getTypeFromId(1 + ItemKarmaBoruto.RAND.nextInt(2));
/*     */     }
/*     */     
/*     */     public static Type getTypeFromId(int id) {
/* 453 */       return TYPES_BY_ID.containsKey(Integer.valueOf(id)) ? TYPES_BY_ID.get(Integer.valueOf(id)) : NONE;
/*     */     }
/*     */     
/*     */     public static Type getTypeFromName(String s) {
/* 457 */       return TYPES_BY_NAME.containsKey(s) ? TYPES_BY_NAME.get(s) : NONE;
/*     */     } }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class ModelKarmaBoruto1 extends ModelBiped {
/*     */     private ModelBiped wearerModel;
/*     */     
/*     */     public ModelKarmaBoruto1() {
/* 465 */       this.field_78090_t = 16;
/* 466 */       this.field_78089_u = 16;
/* 467 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 468 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 469 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 0, 0, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.005F, false));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 474 */       this.field_178720_f.field_78806_j = false;
/* 475 */       this.field_78116_c.field_78806_j = false;
/* 476 */       this.field_78115_e.field_78806_j = false;
/* 477 */       this.field_178724_i.field_78806_j = false;
/* 478 */       this.field_178722_k.field_78806_j = false;
/* 479 */       this.field_178721_j.field_78806_j = false;
/* 480 */       GlStateManager.func_179147_l();
/* 481 */       super.func_78088_a(entity, f, f1, f2, f3, f4, f5);
/* 482 */       GlStateManager.func_179084_k();
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_178686_a(ModelBase model) {
/* 487 */       super.func_178686_a(model);
/* 488 */       if (model instanceof ModelBiped) {
/* 489 */         this.wearerModel = (ModelBiped)model;
/*     */       }
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity entity) {
/* 495 */       if (entity instanceof AbstractClientPlayer && ((AbstractClientPlayer)entity).func_175154_l().equals("slim")) {
/* 496 */         this.field_178724_i.func_78793_a(5.0F, 2.5F, 0.0F);
/* 497 */         this.field_178723_h.func_78793_a(-5.0F, 2.5F, 0.0F);
/*     */       } 
/* 499 */       super.func_78087_a(f, f1, f2, f3, f4, f5, entity);
/* 500 */       if (!(entity instanceof AbstractClientPlayer) && this.wearerModel != null) {
/* 501 */         func_178685_a(this.wearerModel.field_178724_i, this.field_178724_i);
/* 502 */         func_178685_a(this.wearerModel.field_178723_h, this.field_178723_h);
/* 503 */         func_178685_a(this.wearerModel.field_178722_k, this.field_178722_k);
/* 504 */         func_178685_a(this.wearerModel.field_178721_j, this.field_178721_j);
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class ModelKarmaBoruto2 extends ModelBiped {
/*     */     public ModelKarmaBoruto2() {
/* 512 */       this.field_78090_t = 64;
/* 513 */       this.field_78089_u = 64;
/* 514 */       this.field_78115_e = new ModelRenderer((ModelBase)this);
/* 515 */       this.field_78115_e.func_78793_a(0.0F, 0.0F, 0.0F);
/* 516 */       this.field_78115_e.field_78804_l.add(new ModelBox(this.field_78115_e, 64, 64, 0.0F, 6.0F, 0.0F, 0, 0, 0, 0.025F, false));
/*     */       
/* 518 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 519 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 520 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 48, 48, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.025F, false));
/*     */       
/* 522 */       this.field_178724_i = new ModelRenderer((ModelBase)this);
/* 523 */       this.field_178724_i.func_78793_a(5.0F, 2.0F, 0.0F);
/* 524 */       this.field_178724_i.field_78804_l.add(new ModelBox(this.field_178724_i, 64, 64, 1.0F, 4.0F, 0.0F, 0, 0, 0, 0.025F, true));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entityIn, float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
/* 529 */       this.field_78116_c.field_78806_j = false;
/* 530 */       this.field_178720_f.field_78806_j = false;
/* 531 */       this.field_78115_e.field_78806_j = false;
/* 532 */       this.field_178724_i.field_78806_j = false;
/* 533 */       this.field_178722_k.field_78806_j = false;
/* 534 */       this.field_178721_j.field_78806_j = false;
/* 535 */       GlStateManager.func_179147_l();
/* 536 */       super.func_78088_a(entityIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);
/* 537 */       GlStateManager.func_179084_k();
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemKarmaBoruto.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */