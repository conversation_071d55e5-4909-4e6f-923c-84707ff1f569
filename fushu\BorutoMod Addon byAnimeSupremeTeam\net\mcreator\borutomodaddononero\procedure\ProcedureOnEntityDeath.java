/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemKarmaBoruto;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.entity.player.EntityPlayerMP;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.network.Packet;
/*    */ import net.minecraft.network.play.server.SPacketSetSlot;
/*    */ import net.minecraft.util.text.ITextComponent;
/*    */ import net.minecraft.util.text.TextComponentTranslation;
/*    */ import net.minecraft.world.World;
/*    */ import net.minecraftforge.common.MinecraftForge;
/*    */ import net.minecraftforge.event.entity.living.LivingDeathEvent;
/*    */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*    */ import net.minecraftforge.fml.common.eventhandler.SubscribeEvent;
/*    */ import net.narutomod.procedure.ProcedureUtils;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureOnEntityDeath
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureOnEntityDeath(ElementsBorutomodaddononeroMod instance) {
/* 27 */     super(instance, 729);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 31 */     Entity entity = (Entity)dependencies.get("entity");
/* 32 */     if (entity == null) {
/*    */       return;
/*    */     }
/*    */     
/* 36 */     if (dependencies.get("sourceentity") instanceof EntityPlayer) {
/* 37 */       EntityPlayer player = (EntityPlayer)dependencies.get("sourceentity");
/* 38 */       if (entity instanceof net.minecraft.entity.monster.EntityZombie) {
/* 39 */         ItemStack karmaItem = ProcedureUtils.getMatchingItemStack(player, ItemKarmaBoruto.block);
/* 40 */         if (karmaItem != null && karmaItem.func_77973_b() instanceof ItemKarmaBoruto.RangedItem) {
/* 41 */           ItemKarmaBoruto.Type newKarmaType = ItemKarmaBoruto.Type.BORUTOKARMA1;
/* 42 */           ((ItemKarmaBoruto.RangedItem)karmaItem.func_77973_b()).setSageType(karmaItem, newKarmaType);
/* 43 */           karmaItem.func_77964_b(karmaItem.func_77952_i());
/* 44 */           player.field_71071_by.func_70296_d();
/* 45 */           if (player instanceof EntityPlayerMP) {
/* 46 */             ((EntityPlayerMP)player).field_71135_a.func_147359_a((Packet)new SPacketSetSlot(-2, player.field_71071_by.field_70461_c, karmaItem));
/*    */           }
/* 48 */           player.func_146105_b((ITextComponent)new TextComponentTranslation("chattext.karma.assigned", new Object[] { newKarmaType.getLocalizedName() }), true);
/* 49 */           System.out.println("Karma assigned: " + newKarmaType.getLocalizedName());
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */   
/*    */   @SubscribeEvent
/*    */   public void onEntityDeath(LivingDeathEvent event) {
/* 57 */     if (event != null && event.getEntity() != null) {
/* 58 */       Entity entity = event.getEntity();
/* 59 */       World world = entity.field_70170_p;
/* 60 */       Map<String, Object> dependencies = new HashMap<>();
/* 61 */       dependencies.put("entity", entity);
/* 62 */       dependencies.put("world", world);
/* 63 */       dependencies.put("event", event);
/* 64 */       dependencies.put("sourceentity", event.getSource().func_76346_g());
/* 65 */       this; executeProcedure(dependencies);
/*    */     } 
/*    */   }
/*    */ 
/*    */   
/*    */   public void preInit(FMLPreInitializationEvent event) {
/* 71 */     MinecraftForge.EVENT_BUS.register(this);
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureOnEntityDeath.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */