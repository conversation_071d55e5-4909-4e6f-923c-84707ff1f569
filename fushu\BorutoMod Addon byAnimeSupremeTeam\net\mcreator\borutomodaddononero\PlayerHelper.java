/*    */ package net.mcreator.borutomodaddononero;
/*    */ 
/*    */ import java.util.Random;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.entity.player.EntityPlayerMP;
/*    */ import net.minecraft.world.World;
/*    */ import net.minecraft.world.chunk.IChunkProvider;
/*    */ import net.minecraft.world.gen.IChunkGenerator;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.fml.common.FMLCommonHandler;
/*    */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*    */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*    */ import net.minecraftforge.fml.common.event.FMLServerStartingEvent;
/*    */ 
/*    */ @Tag
/*    */ public class PlayerHelper
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   public PlayerHelper(ElementsBorutomodaddononeroMod instance) {
/* 20 */     super(instance, 13);
/*    */   }
/*    */ 
/*    */ 
/*    */   
/*    */   public void initElements() {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void init(FMLInitializationEvent event) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void preInit(FMLPreInitializationEvent event) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void generateWorld(Random random, int posX, int posZ, World world, int dimID, IChunkGenerator cg, IChunkProvider cp) {}
/*    */ 
/*    */ 
/*    */   
/*    */   public void serverLoad(FMLServerStartingEvent event) {}
/*    */ 
/*    */   
/*    */   public void registerModels(ModelRegistryEvent event) {}
/*    */ 
/*    */   
/*    */   public static boolean isPlayerReal(EntityPlayer player) {
/* 48 */     return (player != null && player.field_70170_p != null && !player.field_70170_p.field_72995_K && (player
/*    */ 
/*    */       
/* 51 */       .getClass() == EntityPlayerMP.class || 
/* 52 */       FMLCommonHandler.instance().getMinecraftServerInstance().func_184103_al().func_181057_v().contains(player)));
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\PlayerHelper.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */