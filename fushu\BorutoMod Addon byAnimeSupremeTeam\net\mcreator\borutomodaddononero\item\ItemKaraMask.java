/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.minecraft.client.model.ModelBase;
/*    */ import net.minecraft.client.model.ModelBiped;
/*    */ import net.minecraft.client.model.ModelBox;
/*    */ import net.minecraft.client.model.ModelRenderer;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemArmor;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.world.World;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.common.util.EnumHelper;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ @Tag
/*    */ public class ItemKaraMask extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   @ObjectHolder("borutomodaddononero:kashinkoji_mask")
/* 28 */   public static final Item helmet = null;
/* 29 */   public static ItemArmor.ArmorMaterial ENUMA = EnumHelper.addArmorMaterial("KASHINKOJI_MASK", "borutomodaddononero:kashinkoji_", 25, new int[] { 2, 5, 6, 2 }, 0, null, 0.0F);
/*    */   
/*    */   public ItemKaraMask(ElementsBorutomodaddononeroMod instance) {
/* 32 */     super(instance, 809);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 37 */     this.elements.items.add(() -> ((Item)(new ItemArmor(ENUMA, 0, EntityEquipmentSlot.HEAD)
/*    */         {
/*    */           @SideOnly(Side.CLIENT)
/*    */           private ModelBiped armorModel;
/*    */           
/*    */           @SideOnly(Side.CLIENT)
/*    */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/* 44 */             if (this.armorModel == null) {
/* 45 */               this.armorModel = new ItemKaraMask.ModelKaraMask();
/*    */             }
/*    */             
/* 48 */             this.armorModel.field_78117_n = living.func_70093_af();
/* 49 */             this.armorModel.field_78093_q = living.func_184218_aH();
/* 50 */             this.armorModel.field_78091_s = living.func_70631_g_();
/* 51 */             return this.armorModel;
/*    */           }
/*    */ 
/*    */           
/*    */           public void func_77663_a(ItemStack itemstack, World world, Entity entity, int par4, boolean par5) {
/* 56 */             super.func_77663_a(itemstack, world, entity, par4, par5);
/* 57 */             if (entity.field_70173_aa % 10 == 6 && entity instanceof EntityLivingBase) {
/* 58 */               entity.func_174805_g(!((EntityLivingBase)entity).func_184582_a(EntityEquipmentSlot.HEAD).equals(itemstack));
/*    */             }
/*    */           }
/*    */ 
/*    */           
/*    */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/* 64 */             return "borutomodaddononero:textures/kashinkoji_mask.png";
/*    */           }
/*    */         }).func_77655_b("kashinkoji_mask").setRegistryName("kashinkoji_mask")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 72 */     ModelLoader.setCustomModelResourceLocation(helmet, 0, new ModelResourceLocation("borutomodaddononero:kashinkoji_mask", "inventory"));
/*    */   }
/*    */ 
/*    */   
/*    */   public static class ModelKaraMask
/*    */     extends ModelBiped
/*    */   {
/*    */     public ModelKaraMask() {
/* 80 */       this.field_78090_t = 32;
/* 81 */       this.field_78089_u = 32;
/* 82 */       this.field_78116_c = new ModelRenderer((ModelBase)this);
/* 83 */       this.field_78116_c.func_78793_a(0.0F, 0.0F, 0.0F);
/* 84 */       this.field_78116_c.field_78804_l.add(new ModelBox(this.field_78116_c, 0, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.025F, false));
/*    */     }
/*    */     
/*    */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 88 */       modelRenderer.field_78795_f = x;
/* 89 */       modelRenderer.field_78796_g = y;
/* 90 */       modelRenderer.field_78808_h = z;
/*    */     }
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemKaraMask.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */