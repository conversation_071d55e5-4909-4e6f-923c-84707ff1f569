/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.item.ItemKarmaBoruto;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderLiving;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityCreature;
/*     */ import net.minecraft.entity.EntityLiving;
/*     */ import net.minecraft.entity.EnumCreatureAttribute;
/*     */ import net.minecraft.entity.EnumCreatureType;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.EntityAIAttackMelee;
/*     */ import net.minecraft.entity.ai.EntityAIBase;
/*     */ import net.minecraft.entity.ai.EntityAIHurtByTarget;
/*     */ import net.minecraft.entity.ai.EntityAILookIdle;
/*     */ import net.minecraft.entity.ai.EntityAIWander;
/*     */ import net.minecraft.entity.monster.EntityMob;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraft.world.biome.Biome;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ @Tag
/*     */ public class EntityMomoshikiBoss extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 3;
/*     */   
/*     */   public EntityMomoshikiBoss(ElementsBorutomodaddononeroMod instance) {
/*  43 */     super(instance, 28);
/*     */   }
/*     */   public static final int ENTITYID_RANGED = 4;
/*     */   
/*     */   public void initElements() {
/*  48 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(EntityCustom.class).id(new ResourceLocation("borutomodaddononero", "momoshiki_boss"), 3).name("momoshiki_boss").tracker(64, 3, true).egg(-1, -1).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(FMLInitializationEvent event) {
/*  55 */     Biome[] spawnBiomes = { (Biome)Biome.field_185377_q.func_82594_a(new ResourceLocation("plains")) };
/*  56 */     EntityRegistry.addSpawn(EntityCustom.class, 1, 1, 1, EnumCreatureType.MONSTER, spawnBiomes);
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  62 */     RenderingRegistry.registerEntityRenderingHandler(EntityCustom.class, renderManager -> new RenderLiving(renderManager, new ModelMomoshiki(), 0.5F)
/*     */         {
/*     */           protected ResourceLocation func_110775_a(Entity entity) {
/*  65 */             return new ResourceLocation("borutomodaddononero:textures/momoshiki_boss_texture.png");
/*     */           }
/*     */         });
/*     */   }
/*     */   
/*     */   public static class EntityCustom extends EntityMob {
/*     */     public EntityCustom(World world) {
/*  72 */       super(world);
/*  73 */       func_70105_a(0.6F, 1.8F);
/*  74 */       this.field_70728_aV = 0;
/*  75 */       this.field_70178_ae = false;
/*  76 */       func_94061_f(false);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_184651_r() {
/*  81 */       super.func_184651_r();
/*  82 */       this.field_70714_bg.func_75776_a(1, (EntityAIBase)new EntityAIAttackMelee((EntityCreature)this, 1.2D, false));
/*  83 */       this.field_70714_bg.func_75776_a(2, (EntityAIBase)new EntityAIWander((EntityCreature)this, 1.0D));
/*  84 */       this.field_70715_bh.func_75776_a(3, (EntityAIBase)new EntityAIHurtByTarget((EntityCreature)this, false, new Class[0]));
/*  85 */       this.field_70714_bg.func_75776_a(4, (EntityAIBase)new EntityAILookIdle((EntityLiving)this));
/*  86 */       this.field_70714_bg.func_75776_a(5, (EntityAIBase)new EntityAISwimming((EntityLiving)this));
/*     */     }
/*     */ 
/*     */     
/*     */     public EnumCreatureAttribute func_70668_bt() {
/*  91 */       return EnumCreatureAttribute.UNDEFINED;
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184639_G() {
/*  96 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation(""));
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184601_bQ(DamageSource ds) {
/* 101 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("entity.generic.hurt"));
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184615_bR() {
/* 106 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("entity.generic.death"));
/*     */     }
/*     */ 
/*     */     
/*     */     protected float func_70599_aP() {
/* 111 */       return 1.0F;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_110147_ax() {
/* 116 */       super.func_110147_ax();
/* 117 */       if (func_110148_a(SharedMonsterAttributes.field_188791_g) != null)
/* 118 */         func_110148_a(SharedMonsterAttributes.field_188791_g).func_111128_a(0.0D); 
/* 119 */       if (func_110148_a(SharedMonsterAttributes.field_111263_d) != null)
/* 120 */         func_110148_a(SharedMonsterAttributes.field_111263_d).func_111128_a(0.3D); 
/* 121 */       if (func_110148_a(SharedMonsterAttributes.field_111267_a) != null)
/* 122 */         func_110148_a(SharedMonsterAttributes.field_111267_a).func_111128_a(1.0D); 
/* 123 */       if (func_110148_a(SharedMonsterAttributes.field_111264_e) != null) {
/* 124 */         func_110148_a(SharedMonsterAttributes.field_111264_e).func_111128_a(3.0D);
/*     */       }
/*     */     }
/*     */     
/*     */     public void func_70645_a(DamageSource cause) {
/* 129 */       super.func_70645_a(cause);
/* 130 */       if (cause.func_76346_g() instanceof EntityPlayer) {
/* 131 */         EntityPlayer player = (EntityPlayer)cause.func_76346_g();
/* 132 */         ItemStack karmaItem = new ItemStack(ItemKarmaBoruto.block);
/* 133 */         if (!player.field_71071_by.func_70441_a(karmaItem)) {
/* 134 */           player.func_71019_a(karmaItem, false);
/*     */         }
/* 136 */         ItemStack stack = ProcedureUtils.getMatchingItemStack(player, ItemKarmaBoruto.block);
/* 137 */         if (stack != null) {
/* 138 */           ((ItemKarmaBoruto.RangedItem)stack.func_77973_b()).setSageType(stack, ItemKarmaBoruto.Type.BORUTOKARMA1);
/* 139 */           ItemKarmaBoruto.activateBorutoKarma1Mode(stack, player);
/* 140 */           player.getEntityData().func_74757_a("HasBorutoKarma1Mode", true);
/*     */         } 
/*     */       } 
/*     */     }
/*     */   }
/*     */   
/*     */   public static class ModelMomoshiki
/*     */     extends ModelBase
/*     */   {
/*     */     private final ModelRenderer Head;
/*     */     private final ModelRenderer Body;
/*     */     private final ModelRenderer RightArm;
/*     */     private final ModelRenderer LeftArm;
/*     */     private final ModelRenderer RightLeg;
/*     */     private final ModelRenderer LeftLeg;
/*     */     
/*     */     public ModelMomoshiki() {
/* 157 */       this.field_78090_t = 64;
/* 158 */       this.field_78089_u = 64;
/* 159 */       this.Head = new ModelRenderer(this);
/* 160 */       this.Head.func_78793_a(0.0F, 0.0F, 0.0F);
/* 161 */       this.Head.field_78804_l.add(new ModelBox(this.Head, 0, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.0F, false));
/* 162 */       this.Head.field_78804_l.add(new ModelBox(this.Head, 32, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.5F, false));
/* 163 */       this.Body = new ModelRenderer(this);
/* 164 */       this.Body.func_78793_a(0.0F, 0.0F, 0.0F);
/* 165 */       this.Body.field_78804_l.add(new ModelBox(this.Body, 16, 16, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.0F, false));
/* 166 */       this.Body.field_78804_l.add(new ModelBox(this.Body, 16, 32, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.25F, false));
/* 167 */       this.RightArm = new ModelRenderer(this);
/* 168 */       this.RightArm.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 169 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 40, 16, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 170 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 40, 32, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 171 */       this.LeftArm = new ModelRenderer(this);
/* 172 */       this.LeftArm.func_78793_a(5.0F, 2.0F, 0.0F);
/* 173 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 32, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 174 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 48, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 175 */       this.RightLeg = new ModelRenderer(this);
/* 176 */       this.RightLeg.func_78793_a(-1.9F, 12.0F, 0.0F);
/* 177 */       this.RightLeg.field_78804_l.add(new ModelBox(this.RightLeg, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 178 */       this.RightLeg.field_78804_l.add(new ModelBox(this.RightLeg, 0, 32, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 179 */       this.LeftLeg = new ModelRenderer(this);
/* 180 */       this.LeftLeg.func_78793_a(1.9F, 12.0F, 0.0F);
/* 181 */       this.LeftLeg.field_78804_l.add(new ModelBox(this.LeftLeg, 16, 48, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 182 */       this.LeftLeg.field_78804_l.add(new ModelBox(this.LeftLeg, 0, 48, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.25F, false));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 187 */       this.Head.func_78785_a(f5);
/* 188 */       this.Body.func_78785_a(f5);
/* 189 */       this.RightArm.func_78785_a(f5);
/* 190 */       this.LeftArm.func_78785_a(f5);
/* 191 */       this.RightLeg.func_78785_a(f5);
/* 192 */       this.LeftLeg.func_78785_a(f5);
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 196 */       modelRenderer.field_78795_f = x;
/* 197 */       modelRenderer.field_78796_g = y;
/* 198 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */     
/*     */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity e) {
/* 202 */       super.func_78087_a(f, f1, f2, f3, f4, f5, e);
/* 203 */       this.RightArm.field_78795_f = MathHelper.func_76134_b(f * 0.6662F + 3.1415927F) * f1;
/* 204 */       this.LeftLeg.field_78795_f = MathHelper.func_76134_b(f * 1.0F) * -1.0F * f1;
/* 205 */       this.Head.field_78796_g = f3 / 57.295776F;
/* 206 */       this.Head.field_78795_f = f4 / 57.295776F;
/* 207 */       this.LeftArm.field_78795_f = MathHelper.func_76134_b(f * 0.6662F) * f1;
/* 208 */       this.RightLeg.field_78795_f = MathHelper.func_76134_b(f * 1.0F) * 1.0F * f1;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityMomoshikiBoss.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */