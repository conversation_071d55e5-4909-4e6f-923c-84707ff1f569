/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.entity.EntityDaikokutenCube;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.util.math.RayTraceResult;
/*    */ import net.minecraft.util.math.Vec3d;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.Chakra;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureDaikokutenCube
/*    */   extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureDaikokutenCube(ElementsBorutomodaddononeroMod instance) {
/* 18 */     super(instance, 264);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 22 */     if (dependencies.get("is_pressed") == null) {
/* 23 */       System.err.println("Failed to load dependency is_pressed for procedure DaikokutenCube!");
/*    */       return;
/*    */     } 
/* 26 */     if (dependencies.get("entity") == null) {
/* 27 */       System.err.println("Failed to load dependency entity for procedure DaikokutenCube!");
/*    */       return;
/*    */     } 
/* 30 */     if (dependencies.get("world") == null) {
/* 31 */       System.err.println("Failed to load dependency world for procedure DaikokutenCube!");
/*    */       return;
/*    */     } 
/* 34 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 35 */     Entity entity = (Entity)dependencies.get("entity");
/* 36 */     World world = (World)dependencies.get("world");
/*    */     
/* 38 */     if (is_pressed) {
/* 39 */       if (!(entity instanceof EntityPlayer)) {
/*    */         return;
/*    */       }
/* 42 */       if (Chakra.pathway((EntityPlayer)entity).getAmount() >= 250.0D) {
/* 43 */         Chakra.pathway((EntityPlayer)entity).consume(250.0D);
/* 44 */         EntityPlayer player = (EntityPlayer)entity;
/* 45 */         Vec3d look = player.func_70676_i(1.0F);
/* 46 */         Vec3d start = player.func_174824_e(1.0F);
/* 47 */         Vec3d end = start.func_178787_e(look.func_186678_a(20.0D));
/* 48 */         RayTraceResult result = player.field_70170_p.func_72933_a(start, end);
/* 49 */         if (result != null && result.field_72313_a == RayTraceResult.Type.BLOCK) {
/* 50 */           int x = result.func_178782_a().func_177958_n();
/* 51 */           int y = result.func_178782_a().func_177956_o();
/* 52 */           int z = result.func_178782_a().func_177952_p();
/* 53 */           EntityDaikokutenCube.DaikokutenCube cubeEntity = new EntityDaikokutenCube.DaikokutenCube(world, x, (y + 10), z);
/* 54 */           world.func_72838_d((Entity)cubeEntity);
/*    */         } 
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureDaikokutenCube.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */