/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.item.ItemArmor;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.common.util.EnumHelper;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class ItemIsshikiCloak extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   @ObjectHolder("borutomodaddononero:isshiki_cloackbody")
/*  28 */   public static final Item body = null;
/*     */   
/*     */   public ItemIsshikiCloak(ElementsBorutomodaddononeroMod instance) {
/*  31 */     super(instance, 740);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  36 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("ISSHIKI_CLOACK", "borutomodaddononero:isshiki_", 25, new int[] { 2, 5, 6, 2 }, 9, (SoundEvent)SoundEvent.field_187505_a
/*  37 */         .func_82594_a(new ResourceLocation("")), 0.0F);
/*  38 */     this.elements.items.add(() -> ((Item)(new ItemArmor(enuma, 0, EntityEquipmentSlot.CHEST)
/*     */         {
/*     */           @SideOnly(Side.CLIENT)
/*     */           public ModelBiped getArmorModel(EntityLivingBase living, ItemStack stack, EntityEquipmentSlot slot, ModelBiped defaultModel) {
/*  42 */             ModelBiped armorModel = new ModelBiped();
/*  43 */             armorModel.field_78115_e = (new ItemIsshikiCloak.ModelIsshikiRobe()).Body;
/*  44 */             armorModel.field_178723_h = (new ItemIsshikiCloak.ModelIsshikiRobe()).RightArm;
/*  45 */             armorModel.field_178724_i = (new ItemIsshikiCloak.ModelIsshikiRobe()).LeftArm;
/*  46 */             armorModel.field_78117_n = living.func_70093_af();
/*  47 */             armorModel.field_78093_q = living.func_184218_aH();
/*  48 */             armorModel.field_78091_s = living.func_70631_g_();
/*  49 */             return armorModel;
/*     */           }
/*     */ 
/*     */           
/*     */           public String getArmorTexture(ItemStack stack, Entity entity, EntityEquipmentSlot slot, String type) {
/*  54 */             return "borutomodaddononero:textures/cloack_isshiki.png";
/*     */           }
/*     */         }).func_77655_b("isshiki_cloackbody").setRegistryName("isshiki_cloackbody")).func_77637_a(TabBorutoExpansion.tab));
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/*  62 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:isshiki_cloackbody", "inventory"));
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class ModelIsshikiRobe
/*     */     extends ModelBiped
/*     */   {
/*     */     private final ModelRenderer Body;
/*     */     private final ModelRenderer Body_r1;
/*     */     private final ModelRenderer Body_r2;
/*     */     private final ModelRenderer Body_r3;
/*     */     private final ModelRenderer Body_r4;
/*     */     private final ModelRenderer Body_r5;
/*     */     private final ModelRenderer Body_r6;
/*     */     private final ModelRenderer Body_r7;
/*     */     private final ModelRenderer Body_r8;
/*     */     private final ModelRenderer Body_r9;
/*     */     private final ModelRenderer Body_r10;
/*     */     private final ModelRenderer Body_r11;
/*     */     private final ModelRenderer Body_r12;
/*     */     private final ModelRenderer Body_r13;
/*     */     private final ModelRenderer Body_r14;
/*     */     private final ModelRenderer Body_r15;
/*     */     private final ModelRenderer Body_r16;
/*     */     private final ModelRenderer Body_r17;
/*     */     private final ModelRenderer Body_r18;
/*     */     private final ModelRenderer Body_r19;
/*     */     private final ModelRenderer Body_r20;
/*     */     private final ModelRenderer Body_r21;
/*     */     private final ModelRenderer RightArm;
/*     */     private final ModelRenderer LeftArm;
/*     */     
/*     */     public ModelIsshikiRobe() {
/*  95 */       this.field_78090_t = 64;
/*  96 */       this.field_78089_u = 64;
/*     */       
/*  98 */       this.Body = new ModelRenderer((ModelBase)this);
/*  99 */       this.Body.func_78793_a(0.0F, 0.0F, 0.0F);
/* 100 */       this.Body_r1 = new ModelRenderer((ModelBase)this);
/* 101 */       this.Body_r1.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 102 */       this.Body.func_78792_a(this.Body_r1);
/* 103 */       setRotationAngle(this.Body_r1, 1.5708F, 0.1745F, 1.5708F);
/* 104 */       this.Body_r1.field_78804_l.add(new ModelBox(this.Body_r1, 0, -2, 2.6473F, -4.6528F, -5.0125F, 0, 6, 10, -0.001F, true));
/* 105 */       this.Body_r2 = new ModelRenderer((ModelBase)this);
/* 106 */       this.Body_r2.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 107 */       this.Body.func_78792_a(this.Body_r2);
/* 108 */       setRotationAngle(this.Body_r2, -1.5708F, 1.3963F, -1.5708F);
/* 109 */       this.Body_r2.field_78804_l.add(new ModelBox(this.Body_r2, 0, -5, 4.6508F, -0.3507F, -5.0125F, 0, 3, 10, -0.001F, true));
/* 110 */       this.Body_r3 = new ModelRenderer((ModelBase)this);
/* 111 */       this.Body_r3.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 112 */       this.Body.func_78792_a(this.Body_r3);
/* 113 */       setRotationAngle(this.Body_r3, -1.5827F, 1.3839F, -1.5704F);
/* 114 */       this.Body_r3.field_78804_l.add(new ModelBox(this.Body_r3, 44, -1, -4.4285F, -2.9444F, -4.8719F, 0, 2, 5, -0.001F, false));
/* 115 */       this.Body_r4 = new ModelRenderer((ModelBase)this);
/* 116 */       this.Body_r4.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 117 */       this.Body.func_78792_a(this.Body_r4);
/* 118 */       setRotationAngle(this.Body_r4, -0.2618F, 0.7703F, -0.1719F);
/* 119 */       this.Body_r4.field_78804_l.add(new ModelBox(this.Body_r4, 42, -2, -6.5754F, -2.9444F, -3.3111F, 0, 2, 3, -0.001F, false));
/* 120 */       this.Body_r5 = new ModelRenderer((ModelBase)this);
/* 121 */       this.Body_r5.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 122 */       this.Body.func_78792_a(this.Body_r5);
/* 123 */       setRotationAngle(this.Body_r5, -0.1745F, 0.0F, 0.0F);
/* 124 */       this.Body_r5.field_78804_l.add(new ModelBox(this.Body_r5, 21, -7, -6.9531F, -3.0017F, -4.6528F, 0, 2, 7, -0.001F, false));
/* 125 */       this.Body_r6 = new ModelRenderer((ModelBase)this);
/* 126 */       this.Body_r6.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 127 */       this.Body.func_78792_a(this.Body_r6);
/* 128 */       setRotationAngle(this.Body_r6, -0.738F, -0.3507F, -1.748F);
/* 129 */       this.Body_r6.field_78804_l.add(new ModelBox(this.Body_r6, 53, -3, 2.5399F, -6.1442F, -3.2683F, 0, 2, 3, -0.001F, false));
/* 130 */       this.Body_r7 = new ModelRenderer((ModelBase)this);
/* 131 */       this.Body_r7.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 132 */       this.Body.func_78792_a(this.Body_r7);
/* 133 */       setRotationAngle(this.Body_r7, 0.0306F, -0.1719F, -1.748F);
/* 134 */       this.Body_r7.field_78804_l.add(new ModelBox(this.Body_r7, 21, -3, 2.1941F, -6.6754F, -4.6528F, 0, 2, 7, -0.001F, false));
/* 135 */       this.Body_r8 = new ModelRenderer((ModelBase)this);
/* 136 */       this.Body_r8.func_78793_a(-0.013F, 0.1742F, 1.9149F);
/* 137 */       this.Body.func_78792_a(this.Body_r8);
/* 138 */       setRotationAngle(this.Body_r8, -0.1745F, 0.0F, 0.0F);
/* 139 */       this.Body_r8.field_78804_l.add(new ModelBox(this.Body_r8, 0, -8, -4.986F, -1.3507F, -4.6528F, 0, 4, 8, -0.001F, false));
/* 140 */       this.Body_r9 = new ModelRenderer((ModelBase)this);
/* 141 */       this.Body_r9.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 142 */       this.Body.func_78792_a(this.Body_r9);
/* 143 */       setRotationAngle(this.Body_r9, -1.5827F, -1.3839F, 1.5704F);
/* 144 */       this.Body_r9.field_78804_l.add(new ModelBox(this.Body_r9, 44, -1, 4.4285F, -2.9444F, -4.8719F, 0, 2, 5, -0.001F, true));
/* 145 */       this.Body_r10 = new ModelRenderer((ModelBase)this);
/* 146 */       this.Body_r10.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 147 */       this.Body.func_78792_a(this.Body_r10);
/* 148 */       setRotationAngle(this.Body_r10, -0.738F, 0.3507F, 1.748F);
/* 149 */       this.Body_r10.field_78804_l.add(new ModelBox(this.Body_r10, 53, -3, -2.5399F, -6.1442F, -3.2683F, 0, 2, 3, -0.001F, true));
/* 150 */       this.Body_r11 = new ModelRenderer((ModelBase)this);
/* 151 */       this.Body_r11.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 152 */       this.Body.func_78792_a(this.Body_r11);
/* 153 */       setRotationAngle(this.Body_r11, -0.2618F, -0.7703F, 0.1719F);
/* 154 */       this.Body_r11.field_78804_l.add(new ModelBox(this.Body_r11, 42, -2, 6.5754F, -2.9444F, -3.3111F, 0, 2, 3, -0.001F, true));
/* 155 */       this.Body_r12 = new ModelRenderer((ModelBase)this);
/* 156 */       this.Body_r12.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 157 */       this.Body.func_78792_a(this.Body_r12);
/* 158 */       setRotationAngle(this.Body_r12, -0.1745F, 0.0F, 0.0F);
/* 159 */       this.Body_r12.field_78804_l.add(new ModelBox(this.Body_r12, 21, -7, 6.9531F, -3.0017F, -4.6528F, 0, 2, 7, -0.001F, true));
/* 160 */       this.Body_r13 = new ModelRenderer((ModelBase)this);
/* 161 */       this.Body_r13.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 162 */       this.Body.func_78792_a(this.Body_r13);
/* 163 */       setRotationAngle(this.Body_r13, 0.0306F, 0.1719F, 1.748F);
/* 164 */       this.Body_r13.field_78804_l.add(new ModelBox(this.Body_r13, 21, -3, -2.1941F, -6.6754F, -4.6528F, 0, 2, 7, -0.001F, true));
/* 165 */       this.Body_r14 = new ModelRenderer((ModelBase)this);
/* 166 */       this.Body_r14.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 167 */       this.Body.func_78792_a(this.Body_r14);
/* 168 */       setRotationAngle(this.Body_r14, -0.1745F, 0.0F, 0.0F);
/* 169 */       this.Body_r14.field_78804_l.add(new ModelBox(this.Body_r14, 0, -8, 4.986F, -1.3507F, -4.6528F, 0, 4, 8, -0.001F, true));
/* 170 */       this.Body_r15 = new ModelRenderer((ModelBase)this);
/* 171 */       this.Body_r15.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 172 */       this.Body.func_78792_a(this.Body_r15);
/* 173 */       setRotationAngle(this.Body_r15, -1.9199F, 0.0F, 0.0F);
/* 174 */       this.Body_r15.field_78804_l.add(new ModelBox(this.Body_r15, 44, 7, -5.0125F, -5.0752F, -1.7356F, 10, 3, 0, -0.001F, false));
/* 175 */       this.Body_r16 = new ModelRenderer((ModelBase)this);
/* 176 */       this.Body_r16.func_78793_a(5.0E-4F, 1.2357F, 2.9393F);
/* 177 */       this.Body.func_78792_a(this.Body_r16);
/* 178 */       setRotationAngle(this.Body_r16, 0.6545F, 0.0F, -3.1416F);
/* 179 */       this.Body_r16.field_78804_l.add(new ModelBox(this.Body_r16, 44, 14, -5.0F, -2.5F, 0.0F, 10, 5, 0, -0.001F, false));
/* 180 */       this.Body_r17 = new ModelRenderer((ModelBase)this);
/* 181 */       this.Body_r17.func_78793_a(0.013F, 0.1742F, 1.9149F);
/* 182 */       this.Body.func_78792_a(this.Body_r17);
/* 183 */       setRotationAngle(this.Body_r17, -0.6109F, 0.0F, 0.0F);
/* 184 */       this.Body_r17.field_78804_l.add(new ModelBox(this.Body_r17, 44, 14, -5.0125F, -3.1677F, 2.3384F, 10, 5, 0, -0.001F, false));
/* 185 */       this.Body_r18 = new ModelRenderer((ModelBase)this);
/* 186 */       this.Body_r18.func_78793_a(0.0F, 12.3117F, 1.3917F);
/* 187 */       this.Body.func_78792_a(this.Body_r18);
/* 188 */       setRotationAngle(this.Body_r18, 0.1222F, 0.0F, 0.0F);
/* 189 */       this.Body_r18.field_78804_l.add(new ModelBox(this.Body_r18, 12, 18, -4.0F, -10.0F, -2.0F, 8, 18, 4, 0.475F, false));
/* 190 */       this.Body_r19 = new ModelRenderer((ModelBase)this);
/* 191 */       this.Body_r19.func_78793_a(0.0F, 12.3117F, -1.4555F);
/* 192 */       this.Body.func_78792_a(this.Body_r19);
/* 193 */       setRotationAngle(this.Body_r19, -0.1222F, 0.0F, 0.0F);
/* 194 */       this.Body_r19.field_78804_l.add(new ModelBox(this.Body_r19, 0, 18, -4.0F, -10.0F, -2.0F, 8, 18, 4, 0.475F, false));
/* 195 */       this.Body_r20 = new ModelRenderer((ModelBase)this);
/* 196 */       this.Body_r20.func_78793_a(0.0F, 11.3117F, 1.2917F);
/* 197 */       this.Body.func_78792_a(this.Body_r20);
/* 198 */       setRotationAngle(this.Body_r20, 0.1222F, 0.0F, 0.0F);
/* 199 */       this.Body_r20.field_78804_l.add(new ModelBox(this.Body_r20, 12, 42, -4.0F, -9.0F, -2.0F, 8, 18, 4, 0.5F, false));
/* 200 */       this.Body_r21 = new ModelRenderer((ModelBase)this);
/* 201 */       this.Body_r21.func_78793_a(0.0F, 11.3117F, -1.3555F);
/* 202 */       this.Body.func_78792_a(this.Body_r21);
/* 203 */       setRotationAngle(this.Body_r21, -0.1222F, 0.0F, 0.0F);
/* 204 */       this.Body_r21.field_78804_l.add(new ModelBox(this.Body_r21, 0, 42, -4.0F, -9.0F, -2.0F, 8, 18, 4, 0.5F, false));
/* 205 */       this.RightArm = new ModelRenderer((ModelBase)this);
/* 206 */       this.RightArm.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 207 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 48, 48, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.2F, true));
/* 208 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 48, 32, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.125F, false));
/* 209 */       this.LeftArm = new ModelRenderer((ModelBase)this);
/* 210 */       this.LeftArm.func_78793_a(5.0F, 2.0F, 0.0F);
/* 211 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 48, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.2F, false));
/* 212 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 48, 32, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.125F, false));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 217 */       this.Body.func_78785_a(f5);
/* 218 */       this.RightArm.func_78785_a(f5);
/* 219 */       this.LeftArm.func_78785_a(f5);
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 223 */       modelRenderer.field_78795_f = x;
/* 224 */       modelRenderer.field_78796_g = y;
/* 225 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */     
/*     */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity e) {
/* 229 */       super.func_78087_a(f, f1, f2, f3, f4, f5, e);
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemIsshikiCloak.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */