/*    */ package net.mcreator.borutomodaddononero.item;
/*    */ 
/*    */ import com.google.common.collect.Multimap;
/*    */ import java.util.HashMap;
/*    */ import java.util.Set;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*    */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*    */ import net.minecraft.entity.SharedMonsterAttributes;
/*    */ import net.minecraft.entity.ai.attributes.AttributeModifier;
/*    */ import net.minecraft.inventory.EntityEquipmentSlot;
/*    */ import net.minecraft.item.Item;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.item.ItemSword;
/*    */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*    */ import net.minecraftforge.client.model.ModelLoader;
/*    */ import net.minecraftforge.common.util.EnumHelper;
/*    */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*    */ import net.minecraftforge.fml.relauncher.Side;
/*    */ import net.minecraftforge.fml.relauncher.SideOnly;
/*    */ 
/*    */ 
/*    */ 
/*    */ 
/*    */ @Tag
/*    */ public class ItemChakraAxe
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   @ObjectHolder("borutomodaddononero:chakra_axe")
/* 31 */   public static final Item block = null;
/*    */   public ItemChakraAxe(ElementsBorutomodaddononeroMod instance) {
/* 33 */     super(instance, 2);
/*    */   }
/*    */ 
/*    */   
/*    */   public void initElements() {
/* 38 */     this.elements.items.add(() -> ((Item)(new ItemSword(EnumHelper.addToolMaterial("CHAKRA_AXE", 0, 25, 0.0F, 21.0F, 0))
/*    */         {
/*    */           public Multimap<String, AttributeModifier> func_111205_h(EntityEquipmentSlot slot) {
/* 41 */             Multimap<String, AttributeModifier> multimap = super.func_111205_h(slot);
/* 42 */             if (slot == EntityEquipmentSlot.MAINHAND) {
/* 43 */               multimap.put(SharedMonsterAttributes.field_111264_e.func_111108_a(), new AttributeModifier(field_111210_e, "Weapon modifier", 
/* 44 */                     func_150931_i(), 0));
/* 45 */               multimap.put(SharedMonsterAttributes.field_188790_f.func_111108_a(), new AttributeModifier(field_185050_h, "Weapon modifier", -3.0D, 0));
/*    */             } 
/*    */             
/* 48 */             return multimap;
/*    */           }
/*    */           
/*    */           public Set<String> getToolClasses(ItemStack stack) {
/* 52 */             HashMap<Object, Object> ret = new HashMap<>();
/* 53 */             ret.put("sword", Integer.valueOf(0));
/* 54 */             return ret.keySet();
/*    */           }
/*    */         }).func_77655_b("chakra_axe").setRegistryName("chakra_axe")).func_77637_a(TabBorutoExpansion.tab));
/*    */   }
/*    */ 
/*    */   
/*    */   @SideOnly(Side.CLIENT)
/*    */   public void registerModels(ModelRegistryEvent event) {
/* 62 */     ModelLoader.setCustomModelResourceLocation(block, 0, new ModelResourceLocation("borutomodaddononero:chakra_axe", "inventory"));
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemChakraAxe.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */