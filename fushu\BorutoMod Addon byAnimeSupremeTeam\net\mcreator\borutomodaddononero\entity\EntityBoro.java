/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderLiving;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityCreature;
/*     */ import net.minecraft.entity.EntityLiving;
/*     */ import net.minecraft.entity.EnumCreatureAttribute;
/*     */ import net.minecraft.entity.EnumCreatureType;
/*     */ import net.minecraft.entity.SharedMonsterAttributes;
/*     */ import net.minecraft.entity.ai.EntityAIAttackMelee;
/*     */ import net.minecraft.entity.ai.EntityAIBase;
/*     */ import net.minecraft.entity.ai.EntityAIHurtByTarget;
/*     */ import net.minecraft.entity.ai.EntityAILookIdle;
/*     */ import net.minecraft.entity.ai.EntityAISwimming;
/*     */ import net.minecraft.entity.ai.EntityAIWander;
/*     */ import net.minecraft.entity.monster.EntityMob;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraft.world.biome.Biome;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class EntityBoro extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 1;
/*     */   
/*     */   public EntityBoro(ElementsBorutomodaddononeroMod instance) {
/*  43 */     super(instance, 8);
/*     */   }
/*     */   public static final int ENTITYID_RANGED = 2;
/*     */   
/*     */   public void initElements() {
/*  48 */     this.elements.entities
/*  49 */       .add(() -> EntityEntryBuilder.create().entity(EntityCustom.class).id(new ResourceLocation("borutomodaddononero", "boro"), 1).name("boro").tracker(64, 3, true).egg(-16777216, -3407668).build());
/*     */   }
/*     */ 
/*     */ 
/*     */   
/*     */   public void init(FMLInitializationEvent event) {
/*  55 */     Biome[] spawnBiomes = { (Biome)Biome.field_185377_q.func_82594_a(new ResourceLocation("jungle")) };
/*  56 */     EntityRegistry.addSpawn(EntityCustom.class, 1, 1, 1, EnumCreatureType.MONSTER, spawnBiomes);
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  62 */     RenderingRegistry.registerEntityRenderingHandler(EntityCustom.class, renderManager -> new RenderLiving(renderManager, new Modelboro_start(), 0.5F)
/*     */         {
/*     */           protected ResourceLocation func_110775_a(Entity entity) {
/*  65 */             return new ResourceLocation("borutomodaddononero:textures/boro_start.png");
/*     */           }
/*     */         });
/*     */   }
/*     */   
/*     */   public static class EntityCustom extends EntityMob {
/*     */     public EntityCustom(World world) {
/*  72 */       super(world);
/*  73 */       func_70105_a(0.6F, 1.8F);
/*  74 */       this.field_70728_aV = 0;
/*  75 */       this.field_70178_ae = false;
/*  76 */       func_94061_f(false);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_184651_r() {
/*  81 */       super.func_184651_r();
/*  82 */       this.field_70714_bg.func_75776_a(1, (EntityAIBase)new EntityAIAttackMelee((EntityCreature)this, 1.2D, false));
/*  83 */       this.field_70714_bg.func_75776_a(2, (EntityAIBase)new EntityAIWander((EntityCreature)this, 1.0D));
/*  84 */       this.field_70715_bh.func_75776_a(3, (EntityAIBase)new EntityAIHurtByTarget((EntityCreature)this, false, new Class[0]));
/*  85 */       this.field_70714_bg.func_75776_a(4, (EntityAIBase)new EntityAILookIdle((EntityLiving)this));
/*  86 */       this.field_70714_bg.func_75776_a(5, (EntityAIBase)new EntityAISwimming((EntityLiving)this));
/*     */     }
/*     */ 
/*     */     
/*     */     public EnumCreatureAttribute func_70668_bt() {
/*  91 */       return EnumCreatureAttribute.UNDEFINED;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70628_a(boolean wasRecentlyHit, int lootingModifier) {
/*  96 */       func_145779_a((Item)Item.field_150901_e.func_82594_a(new ResourceLocation("borutomodaddononero", "boro_corebody")), 1);
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184639_G() {
/* 101 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation(""));
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184601_bQ(DamageSource ds) {
/* 106 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("entity.generic.hurt"));
/*     */     }
/*     */ 
/*     */     
/*     */     public SoundEvent func_184615_bR() {
/* 111 */       return (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("entity.generic.death"));
/*     */     }
/*     */ 
/*     */     
/*     */     protected float func_70599_aP() {
/* 116 */       return 1.0F;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_110147_ax() {
/* 121 */       super.func_110147_ax();
/* 122 */       if (func_110148_a(SharedMonsterAttributes.field_188791_g) != null)
/* 123 */         func_110148_a(SharedMonsterAttributes.field_188791_g).func_111128_a(0.0D); 
/* 124 */       if (func_110148_a(SharedMonsterAttributes.field_111263_d) != null)
/* 125 */         func_110148_a(SharedMonsterAttributes.field_111263_d).func_111128_a(0.3D); 
/* 126 */       if (func_110148_a(SharedMonsterAttributes.field_111267_a) != null)
/* 127 */         func_110148_a(SharedMonsterAttributes.field_111267_a).func_111128_a(750.0D); 
/* 128 */       if (func_110148_a(SharedMonsterAttributes.field_111264_e) != null)
/* 129 */         func_110148_a(SharedMonsterAttributes.field_111264_e).func_111128_a(4.0D); 
/*     */     }
/*     */   }
/*     */   
/*     */   public static class Modelboro_start
/*     */     extends ModelBase
/*     */   {
/*     */     private final ModelRenderer Head;
/*     */     private final ModelRenderer Body;
/*     */     private final ModelRenderer RightArm;
/*     */     private final ModelRenderer LeftArm;
/*     */     private final ModelRenderer RightLeg;
/*     */     private final ModelRenderer LeftLeg;
/*     */     
/*     */     public Modelboro_start() {
/* 144 */       this.field_78090_t = 64;
/* 145 */       this.field_78089_u = 64;
/* 146 */       this.Head = new ModelRenderer(this);
/* 147 */       this.Head.func_78793_a(0.0F, 0.0F, 0.0F);
/* 148 */       this.Head.field_78804_l.add(new ModelBox(this.Head, 0, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.0F, false));
/* 149 */       this.Head.field_78804_l.add(new ModelBox(this.Head, 32, 0, -4.0F, -8.0F, -4.0F, 8, 8, 8, 0.5F, false));
/* 150 */       this.Body = new ModelRenderer(this);
/* 151 */       this.Body.func_78793_a(0.0F, 0.0F, 0.0F);
/* 152 */       this.Body.field_78804_l.add(new ModelBox(this.Body, 16, 16, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.0F, false));
/* 153 */       this.Body.field_78804_l.add(new ModelBox(this.Body, 16, 32, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.25F, false));
/* 154 */       this.RightArm = new ModelRenderer(this);
/* 155 */       this.RightArm.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 156 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 40, 16, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 157 */       this.RightArm.field_78804_l.add(new ModelBox(this.RightArm, 40, 32, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 158 */       this.LeftArm = new ModelRenderer(this);
/* 159 */       this.LeftArm.func_78793_a(5.0F, 2.0F, 0.0F);
/* 160 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 32, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 161 */       this.LeftArm.field_78804_l.add(new ModelBox(this.LeftArm, 48, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 162 */       this.RightLeg = new ModelRenderer(this);
/* 163 */       this.RightLeg.func_78793_a(-1.9F, 12.0F, 0.0F);
/* 164 */       this.RightLeg.field_78804_l.add(new ModelBox(this.RightLeg, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 165 */       this.RightLeg.field_78804_l.add(new ModelBox(this.RightLeg, 0, 32, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.25F, false));
/* 166 */       this.LeftLeg = new ModelRenderer(this);
/* 167 */       this.LeftLeg.func_78793_a(1.9F, 12.0F, 0.0F);
/* 168 */       this.LeftLeg.field_78804_l.add(new ModelBox(this.LeftLeg, 16, 48, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.0F, false));
/* 169 */       this.LeftLeg.field_78804_l.add(new ModelBox(this.LeftLeg, 0, 48, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.25F, false));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 174 */       this.Head.func_78785_a(f5);
/* 175 */       this.Body.func_78785_a(f5);
/* 176 */       this.RightArm.func_78785_a(f5);
/* 177 */       this.LeftArm.func_78785_a(f5);
/* 178 */       this.RightLeg.func_78785_a(f5);
/* 179 */       this.LeftLeg.func_78785_a(f5);
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 183 */       modelRenderer.field_78795_f = x;
/* 184 */       modelRenderer.field_78796_g = y;
/* 185 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */     
/*     */     public void func_78087_a(float f, float f1, float f2, float f3, float f4, float f5, Entity e) {
/* 189 */       super.func_78087_a(f, f1, f2, f3, f4, f5, e);
/* 190 */       this.RightArm.field_78795_f = MathHelper.func_76134_b(f * 0.6662F + 3.1415927F) * f1;
/* 191 */       this.LeftLeg.field_78795_f = MathHelper.func_76134_b(f * 1.0F) * -1.0F * f1;
/* 192 */       this.Head.field_78796_g = f3 / 57.295776F;
/* 193 */       this.Head.field_78795_f = f4 / 57.295776F;
/* 194 */       this.LeftArm.field_78795_f = MathHelper.func_76134_b(f * 0.6662F) * f1;
/* 195 */       this.RightLeg.field_78795_f = MathHelper.func_76134_b(f * 1.0F) * 1.0F * f1;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityBoro.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */