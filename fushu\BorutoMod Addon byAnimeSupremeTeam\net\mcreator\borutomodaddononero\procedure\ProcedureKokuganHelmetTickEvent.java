/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.potion.PotionEffect;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.potion.PotionFlight;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureKokuganHelmetTickEvent
/*    */   extends ElementsBorutomodaddononeroMod.ModElement
/*    */ {
/*    */   public ProcedureKokuganHelmetTickEvent(ElementsBorutomodaddononeroMod instance) {
/* 18 */     super(instance, 99);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(HashMap<String, Object> dependencies) {
/* 22 */     if (dependencies.get("entity") == null) {
/* 23 */       System.err.println("Failed to load dependency entity for procedure KokuganHelmetTickEvent!");
/*    */       return;
/*    */     } 
/* 26 */     if (dependencies.get("world") == null) {
/* 27 */       System.err.println("Failed to load dependency world for procedure KokuganHelmetTickEvent!");
/*    */       return;
/*    */     } 
/* 30 */     if (dependencies.get("itemstack") == null) {
/* 31 */       System.err.println("Failed to load dependency itemstack for procedure KokuganHelmetTickEvent!");
/*    */       return;
/*    */     } 
/* 34 */     Entity entity = (Entity)dependencies.get("entity");
/* 35 */     World world = (World)dependencies.get("world");
/* 36 */     ItemStack itemstack = (ItemStack)dependencies.get("itemstack");
/* 37 */     if (entity instanceof EntityLivingBase && !world.field_72995_K)
/*    */     {
/* 39 */       ((EntityLivingBase)entity).func_70690_d(new PotionEffect(PotionFlight.potion, 210, 0, false, false));
/*    */     }
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureKokuganHelmetTickEvent.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */