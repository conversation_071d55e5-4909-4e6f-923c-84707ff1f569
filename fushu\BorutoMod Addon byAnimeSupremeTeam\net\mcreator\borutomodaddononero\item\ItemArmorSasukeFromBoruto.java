/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ 
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.item.ItemNinjaArmor;
/*     */ 
/*     */ @Tag
/*     */ public class ItemArmorSasukeFromBoruto extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   @ObjectHolder("borutomodaddononero:sasuke_from_borutobody")
/*  24 */   public static final Item body = null;
/*     */   @ObjectHolder("borutomodaddononero:sasuke_from_borutolegs")
/*  26 */   public static final Item legs = null;
/*     */   
/*     */   public ItemArmorSasukeFromBoruto(ElementsBorutomodaddononeroMod instance) {
/*  29 */     super(instance, 662);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  34 */     this.elements.items.add(() -> ((Item)(new ItemNinjaArmor.Base(ItemNinjaArmor.Type.OTHER, EntityEquipmentSlot.CHEST)
/*     */         {
/*     */           protected ItemNinjaArmor.ArmorData setArmorData(ItemNinjaArmor.Type type, EntityEquipmentSlot slotIn) {
/*  37 */             return new Armor4SlotBody();
/*     */           }
/*     */           
/*     */           class Armor4SlotBody
/*     */             extends ItemNinjaArmor.ArmorData {
/*     */             @SideOnly(Side.CLIENT)
/*     */             protected void init() {
/*  44 */               this.model = new ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto();
/*  45 */               this.texture = "borutomodaddononero:textures/sasuke_boruto.png";
/*     */             }
/*     */ 
/*     */             
/*     */             @SideOnly(Side.CLIENT)
/*     */             public void setSlotVisible() {
/*  51 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).shirt.field_78806_j = true;
/*  52 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).vest.field_78806_j = false;
/*  53 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178723_h.field_78806_j = false;
/*  54 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178724_i.field_78806_j = false;
/*  55 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178721_j.field_78806_j = false;
/*  56 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178722_k.field_78806_j = false;
/*  57 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_78116_c.field_78806_j = false;
/*     */             }
/*     */           }
/*     */         }).func_77655_b("sasuke_from_borutobody").setRegistryName("sasuke_from_borutobody")).func_77637_a(TabBorutoExpansion.tab));
/*     */     
/*  62 */     this.elements.items.add(() -> ((Item)(new ItemNinjaArmor.Base(ItemNinjaArmor.Type.OTHER, EntityEquipmentSlot.LEGS)
/*     */         {
/*     */           protected ItemNinjaArmor.ArmorData setArmorData(ItemNinjaArmor.Type type, EntityEquipmentSlot slotIn) {
/*  65 */             return new Armor4SlotLegs();
/*     */           }
/*     */           
/*     */           class Armor4SlotLegs
/*     */             extends ItemNinjaArmor.ArmorData {
/*     */             @SideOnly(Side.CLIENT)
/*     */             protected void init() {
/*  72 */               this.model = new ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto();
/*  73 */               this.texture = "borutomodaddononero:textures/sasuke_boruto.png";
/*     */             }
/*     */ 
/*     */             
/*     */             @SideOnly(Side.CLIENT)
/*     */             public void setSlotVisible() {
/*  79 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).shirt.field_78806_j = false;
/*  80 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).vest.field_78806_j = true;
/*  81 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178723_h.field_78806_j = true;
/*  82 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178724_i.field_78806_j = true;
/*  83 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178721_j.field_78806_j = true;
/*  84 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_178722_k.field_78806_j = true;
/*  85 */               ((ItemArmorSasukeFromBoruto.ModelArmorSasukeFromBoruto)this.model).field_78116_c.field_78806_j = false;
/*     */             }
/*     */           }
/*     */         }).func_77655_b("sasuke_from_borutolegs").setRegistryName("sasuke_from_borutolegs")).func_77637_a(TabBorutoExpansion.tab));
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/*  94 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:sasuke_from_borutobody", "inventory"));
/*  95 */     ModelLoader.setCustomModelResourceLocation(legs, 0, new ModelResourceLocation("borutomodaddononero:sasuke_from_borutolegs", "inventory"));
/*     */   }
/*     */   
/*     */   public static class ModelArmorSasukeFromBoruto extends ModelBiped {
/*     */     public final ModelRenderer shirt;
/*     */     
/*     */     public ModelArmorSasukeFromBoruto() {
/* 102 */       super(1.0F);
/*     */       
/* 104 */       this.field_78090_t = 64;
/* 105 */       this.field_78089_u = 64;
/*     */       
/* 107 */       this.field_78115_e = new ModelRenderer((ModelBase)this);
/* 108 */       this.field_78115_e.func_78793_a(0.0F, 0.0F, 0.0F);
/*     */       
/* 110 */       this.shirt = new ModelRenderer((ModelBase)this);
/* 111 */       this.shirt.func_78793_a(0.0F, 0.0F, 0.0F);
/* 112 */       this.field_78115_e.func_78792_a(this.shirt);
/* 113 */       this.shirt.field_78804_l.add(new ModelBox(this.shirt, 16, 32, -4.0F, 0.0F, -2.0F, 8, 8, 4, 0.075F, false));
/* 114 */       this.shirt.field_78804_l.add(new ModelBox(this.shirt, 16, 42, -4.0F, 10.0F, -2.0F, 8, 2, 4, 0.03F, false));
/* 115 */       this.shirt.field_78804_l.add(new ModelBox(this.shirt, 40, 32, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.05F, false));
/*     */       
/* 117 */       this.vest = new ModelRenderer((ModelBase)this);
/* 118 */       this.vest.func_78793_a(0.0F, 0.0F, 0.0F);
/* 119 */       this.field_78115_e.func_78792_a(this.vest);
/* 120 */       this.vest.field_78804_l.add(new ModelBox(this.vest, 16, 16, -4.0F, 0.0F, -2.0F, 8, 12, 4, 0.025F, false));
/*     */       
/* 122 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 123 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 124 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 40, 16, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.025F, false));
/*     */       
/* 126 */       this.field_178724_i = new ModelRenderer((ModelBase)this);
/* 127 */       this.field_178724_i.func_78793_a(5.0F, 2.0F, 0.0F);
/* 128 */       this.field_178724_i.field_78804_l.add(new ModelBox(this.field_178724_i, 40, 16, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.025F, true));
/*     */       
/* 130 */       this.field_178721_j = new ModelRenderer((ModelBase)this);
/* 131 */       this.field_178721_j.func_78793_a(-1.9F, 12.0F, 0.0F);
/* 132 */       this.field_178721_j.field_78804_l.add(new ModelBox(this.field_178721_j, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.025F, false));
/*     */       
/* 134 */       this.field_178722_k = new ModelRenderer((ModelBase)this);
/* 135 */       this.field_178722_k.func_78793_a(1.9F, 12.0F, 0.0F);
/* 136 */       this.field_178722_k.field_78804_l.add(new ModelBox(this.field_178722_k, 0, 16, -2.0F, 0.0F, -2.0F, 4, 12, 4, 0.025F, false));
/*     */     }
/*     */     public final ModelRenderer vest;
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 140 */       modelRenderer.field_78795_f = x;
/* 141 */       modelRenderer.field_78796_g = y;
/* 142 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 147 */       super.func_78088_a(entity, f, f1, f2, f3, f4, f5);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78087_a(float limbSwing, float limbSwingAmount, float ageInTicks, float netHeadYaw, float headPitch, float scaleFactor, Entity entityIn) {
/* 152 */       super.func_78087_a(limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scaleFactor, entityIn);
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemArmorSasukeFromBoruto.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */