/*     */ package net.mcreator.borutomodaddononero.item;
/*     */ 
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.mcreator.borutomodaddononero.creativetab.TabBorutoExpansion;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.block.model.ModelResourceLocation;
/*     */ import net.minecraft.init.SoundEvents;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.Item;
/*     */ import net.minecraft.item.ItemArmor;
/*     */ import net.minecraftforge.client.event.ModelRegistryEvent;
/*     */ import net.minecraftforge.client.model.ModelLoader;
/*     */ import net.minecraftforge.common.util.EnumHelper;
/*     */ import net.minecraftforge.fml.common.registry.GameRegistry.ObjectHolder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.item.ItemNinjaArmor;
/*     */ 
/*     */ 
/*     */ 
/*     */ 
/*     */ @Tag
/*     */ public class ItemKaraCloak
/*     */   extends ElementsBorutomodaddononeroMod.ModElement
/*     */ {
/*     */   @ObjectHolder("borutomodaddononero:kara_cloak")
/*  31 */   public static final Item body = null;
/*     */   
/*     */   public ItemKaraCloak(ElementsBorutomodaddononeroMod instance) {
/*  34 */     super(instance, 740);
/*     */   }
/*     */ 
/*     */   
/*     */   public void initElements() {
/*  39 */     ItemArmor.ArmorMaterial enuma = EnumHelper.addArmorMaterial("KARA_CLOAK", "borutomodaddononero:kara_", 100, new int[] { 1, 2, 3, 1 }, 9, SoundEvents.field_187728_s, 0.0F);
/*     */     
/*  41 */     this.elements.items.add(() -> ((Item)(new ItemNinjaArmor.Base(ItemNinjaArmor.Type.OTHER, enuma, EntityEquipmentSlot.CHEST)
/*     */         {
/*     */           protected ItemNinjaArmor.ArmorData setArmorData(ItemNinjaArmor.Type type, EntityEquipmentSlot slotIn) {
/*  44 */             return new Armor4Slot();
/*     */           }
/*     */           
/*     */           class Armor4Slot
/*     */             extends ItemNinjaArmor.ArmorData {
/*     */             @SideOnly(Side.CLIENT)
/*     */             protected void init() {
/*  51 */               this.model = new ItemKaraCloak.ModelKaraRobe();
/*  52 */               this.texture = "borutomodaddononero:textures/kara_cloak.png";
/*     */             }
/*     */             
/*     */             @SideOnly(Side.CLIENT)
/*     */             public void setSlotVisible() {
/*  57 */               this.model.field_178720_f.field_78806_j = true;
/*     */             }
/*     */           }
/*     */         }).func_77655_b("kara_cloak").setRegistryName("kara_cloak")).func_77637_a(TabBorutoExpansion.tab));
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void registerModels(ModelRegistryEvent event) {
/*  66 */     ModelLoader.setCustomModelResourceLocation(body, 0, new ModelResourceLocation("borutomodaddononero:kara_cloak", "inventory"));
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class ModelKaraRobe
/*     */     extends ModelBiped {
/*     */     private final ModelRenderer Body_r1;
/*     */     private final ModelRenderer Body_r2;
/*     */     private final ModelRenderer Body_r3;
/*     */     
/*     */     public ModelKaraRobe() {
/*  77 */       this.field_78090_t = 64;
/*  78 */       this.field_78089_u = 64;
/*     */       
/*  80 */       this.field_178720_f = new ModelRenderer((ModelBase)this);
/*  81 */       this.field_178720_f.func_78793_a(0.0F, 0.0F, 0.0F);
/*  82 */       this.field_178720_f.field_78804_l.add(new ModelBox(this.field_178720_f, 32, 6, -4.0F, -2.0F, -4.0F, 8, 2, 8, 0.1F, false));
/*  83 */       this.field_178720_f.field_78804_l.add(new ModelBox(this.field_178720_f, 0, 6, -4.0F, -2.0F, -4.0F, 8, 2, 8, 0.075F, false));
/*     */       
/*  85 */       this.field_78115_e = new ModelRenderer((ModelBase)this);
/*  86 */       this.field_78115_e.func_78793_a(0.0F, 0.0F, 0.0F);
/*     */       
/*  88 */       this.Body_r1 = new ModelRenderer((ModelBase)this);
/*  89 */       this.Body_r1.func_78793_a(1.1F, 1.3425F, 0.9531F);
/*  90 */       this.field_78115_e.func_78792_a(this.Body_r1);
/*  91 */       setRotationAngle(this.Body_r1, -3.0194F, 0.0F, 3.1416F);
/*  92 */       this.Body_r1.field_78804_l.add(new ModelBox(this.Body_r1, 30, 0, -4.0F, -4.0F, 0.0F, 8, 8, 1, -3.675F, false));
/*     */       
/*  94 */       this.Body_r2 = new ModelRenderer((ModelBase)this);
/*  95 */       this.Body_r2.func_78793_a(0.0F, 11.3117F, 1.2917F);
/*  96 */       this.field_78115_e.func_78792_a(this.Body_r2);
/*  97 */       setRotationAngle(this.Body_r2, 0.1222F, 0.0F, 0.0F);
/*  98 */       this.Body_r2.field_78804_l.add(new ModelBox(this.Body_r2, 40, 16, -4.0F, -11.0F, -2.0F, 8, 20, 4, 0.475F, false));
/*  99 */       this.Body_r2.field_78804_l.add(new ModelBox(this.Body_r2, 40, 40, -4.0F, -11.0F, -2.0F, 8, 20, 4, 0.5F, false));
/*     */       
/* 101 */       this.Body_r3 = new ModelRenderer((ModelBase)this);
/* 102 */       this.Body_r3.func_78793_a(0.0F, 11.3117F, -1.3555F);
/* 103 */       this.field_78115_e.func_78792_a(this.Body_r3);
/* 104 */       setRotationAngle(this.Body_r3, -0.1222F, 0.0F, 0.0F);
/* 105 */       this.Body_r3.field_78804_l.add(new ModelBox(this.Body_r3, 16, 16, -4.0F, -11.0F, -2.0F, 8, 20, 4, 0.475F, false));
/* 106 */       this.Body_r3.field_78804_l.add(new ModelBox(this.Body_r3, 16, 40, -4.0F, -11.0F, -2.0F, 8, 20, 4, 0.5F, false));
/*     */       
/* 108 */       this.field_178723_h = new ModelRenderer((ModelBase)this);
/* 109 */       this.field_178723_h.func_78793_a(-5.0F, 2.0F, 0.0F);
/* 110 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 0, 48, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.4F, false));
/* 111 */       this.field_178723_h.field_78804_l.add(new ModelBox(this.field_178723_h, 0, 32, -3.0F, -2.0F, -2.0F, 4, 12, 4, 0.375F, false));
/*     */       
/* 113 */       this.field_178724_i = new ModelRenderer((ModelBase)this);
/* 114 */       this.field_178724_i.func_78793_a(5.0F, 2.0F, 0.0F);
/* 115 */       this.field_178724_i.field_78804_l.add(new ModelBox(this.field_178724_i, 0, 48, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.4F, false));
/* 116 */       this.field_178724_i.field_78804_l.add(new ModelBox(this.field_178724_i, 0, 32, -1.0F, -2.0F, -2.0F, 4, 12, 4, 0.375F, false));
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 120 */       modelRenderer.field_78795_f = x;
/* 121 */       modelRenderer.field_78796_g = y;
/* 122 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\item\ItemKaraCloak.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */