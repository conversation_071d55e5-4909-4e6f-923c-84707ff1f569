package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;
import net.minecraft.client.model.ModelBase;
import net.minecraft.client.model.ModelBox;
import net.minecraft.client.model.ModelRenderer;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.renderer.entity.Render;
import net.minecraft.client.renderer.entity.RenderManager;
import net.minecraft.entity.Entity;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.MoverType;
import net.minecraft.init.MobEffects;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.DamageSource;
import net.minecraft.util.ResourceLocation;
import net.minecraft.world.World;
import net.minecraftforge.fml.client.registry.RenderingRegistry;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.registry.EntityEntry;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import java.util.List;

@ElementsNarutomodMod.ModElement.Tag
public class EntityDaikokutenCube extends ElementsNarutomodMod.ModElement {
	public static final int ENTITYID = 660;

	public EntityDaikokutenCube(ElementsNarutomodMod instance) {
		super(instance, 660);
	}

	@Override
	public void initElements() {
		elements.entities.add(() -> EntityEntryBuilder.create().entity(EntityCustom.class)
			.id(new ResourceLocation("narutomod", "daikokuten_cube"), ENTITYID)
			.name("daikokuten_cube").tracker(64, 1, true).build());
	}

	@SideOnly(Side.CLIENT)
	@Override
	public void preInit(FMLPreInitializationEvent event) {
		RenderingRegistry.registerEntityRenderingHandler(EntityCustom.class, renderManager -> new RenderCustom(renderManager));
	}

	public static class EntityCustom extends Entity {
		private int ticksOnGround = 0;
		private EntityLivingBase owner;

		public EntityCustom(World world) {
			super(world);
			setSize(5.0F, 5.0F);
			this.isImmuneToFire = true;
		}

		public EntityCustom(World world, double x, double y, double z) {
			super(world);
			setPosition(x, y, z);
			this.isImmuneToFire = true;
		}

		public void setOwner(EntityLivingBase owner) {
			this.owner = owner;
		}

		@Override
		protected void entityInit() {}

		@Override
		protected void readEntityFromNBT(NBTTagCompound compound) {
			this.ticksOnGround = compound.getInteger("TicksOnGround");
		}

		@Override
		protected void writeEntityToNBT(NBTTagCompound compound) {
			compound.setInteger("TicksOnGround", this.ticksOnGround);
		}

		@Override
		public void onUpdate() {
			super.onUpdate();
			if (isBurning()) {
				extinguish();
			}
			if (this.onGround) {
				if (this.ticksOnGround == 0) {
					// 立方体落地时造成伤害和效果
					List<EntityLivingBase> entities = this.world.getEntitiesWithinAABB(EntityLivingBase.class, 
						getEntityBoundingBox().grow(2.0D));
					for (Entity entity : entities) {
						if (entity != this.owner && entity instanceof EntityLivingBase) {
							entity.attackEntityFrom(DamageSource.MAGIC, 100.0F);
							((EntityLivingBase)entity).addPotionEffect(new PotionEffect(MobEffects.NAUSEA, 200, 4, false, false));
							((EntityLivingBase)entity).addPotionEffect(new PotionEffect(MobEffects.SLOWNESS, 200, 4, false, false));
						}
					}
				}

				this.ticksOnGround++;
				if (this.ticksOnGround > 200) { // 10秒后消失
					setDead();
				}
			} else {
				// 重力下落
				this.motionY -= 0.02D;
				move(MoverType.SELF, this.motionX, this.motionY, this.motionZ);
			}

			// 阻止其他实体移动
			List<Entity> collidingEntities = this.world.getEntitiesInAABBexcluding(this, getEntityBoundingBox(), null);
			for (Entity entity : collidingEntities) {
				if (!(entity instanceof EntityCustom)) {
					entity.motionX = 0.0D;
					entity.motionY = 0.0D;
					entity.motionZ = 0.0D;
					entity.setPosition(entity.prevPosX, entity.prevPosY, entity.prevPosZ);
				}
			}
		}

		@Override
		public boolean attackEntityFrom(DamageSource source, float amount) {
			if (source.isFireDamage() || source == DamageSource.LAVA) {
				return false;
			}
			return super.attackEntityFrom(source, amount);
		}

		@Override
		public void setFire(int seconds) {}
	}

	@SideOnly(Side.CLIENT)
	public class RenderCustom extends Render<EntityCustom> {
		private final ModelBase model;

		public RenderCustom(RenderManager renderManager) {
			super(renderManager);
			this.model = new ModelDaikokutenCube();
		}

		@Override
		public void doRender(EntityCustom entity, double x, double y, double z, float entityYaw, float partialTicks) {
			GlStateManager.pushMatrix();
			GlStateManager.translate(x, y, z);
			bindEntityTexture(entity);
			this.model.render(entity, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0625F);
			GlStateManager.popMatrix();
		}

		@Override
		protected ResourceLocation getEntityTexture(EntityCustom entity) {
			return new ResourceLocation("narutomod:textures/entity/daikokuten_cube.png");
		}
	}

	@SideOnly(Side.CLIENT)
	public static class ModelDaikokutenCube extends ModelBase {
		private final ModelRenderer bb_main;

		public ModelDaikokutenCube() {
			this.textureWidth = 64;
			this.textureHeight = 64;

			this.bb_main = new ModelRenderer(this);
			this.bb_main.setRotationPoint(0.0F, 24.0F, 0.0F);
			this.bb_main.cubeList.add(new ModelBox(this.bb_main, 0, 0, -8.0F, 8.0F, -8.0F, 16, 16, 16, 32.0F, false));
		}

		@Override
		public void render(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
			this.bb_main.render(f5);
		}

		public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
			modelRenderer.rotateAngleX = x;
			modelRenderer.rotateAngleY = y;
			modelRenderer.rotateAngleZ = z;
		}
	}
}
