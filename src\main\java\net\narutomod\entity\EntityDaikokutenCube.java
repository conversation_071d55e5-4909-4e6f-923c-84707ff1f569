package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.client.registry.RenderingRegistry;
import net.minecraftforge.client.model.obj.OBJLoader;

import net.minecraft.world.World;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.DamageSource;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.entity.MoverType;
import net.minecraft.nbt.NBTTagCompound;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.minecraft.client.renderer.entity.RenderManager;
import net.minecraft.client.renderer.entity.Render;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.model.ModelBase;
import net.minecraft.client.model.ModelRenderer;
import net.minecraft.client.model.ModelBox;

import java.util.List;

@ElementsNarutomodMod.ModElement.Tag
public class EntityDaikokutenCube extends ElementsNarutomodMod.ModElement {
    public static final int ENTITYID = 606;
    
    public EntityDaikokutenCube(ElementsNarutomodMod instance) {
        super(instance, 606);
    }
    
    @Override
    public void initElements() {
        elements.entities.add(() -> EntityEntryBuilder.create().entity(DaikokutenCube.class)
            .id(new ResourceLocation("narutomod", "daikokutencube"), ENTITYID)
            .name("daikokutencube").tracker(64, 1, true).build());
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void preInit(FMLPreInitializationEvent event) {
        RenderingRegistry.registerEntityRenderingHandler(DaikokutenCube.class, renderManager -> new RenderCustom(renderManager));
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void init(FMLInitializationEvent event) {
        OBJLoader.INSTANCE.addDomain("narutomod");
    }
    
    public static class DaikokutenCube extends Entity {
        private int ticksOnGround = 0;
        private EntityLivingBase owner;
        
        public DaikokutenCube(World world) {
            super(world);
            setSize(5.0F, 5.0F);
            this.isImmuneToFire = true;
        }
        
        public DaikokutenCube(World world, double x, double y, double z) {
            super(world);
            setPosition(x, y, z);
            this.isImmuneToFire = true;
            setSize(5.0F, 5.0F);
        }
        
        public void setOwner(EntityLivingBase owner) {
            this.owner = owner;
        }
        
        public EntityLivingBase getOwner() {
            return this.owner;
        }
        
        @Override
        protected void entityInit() {
            // 初始化数据观察器
        }
        
        @Override
        protected void readEntityFromNBT(NBTTagCompound compound) {
            this.ticksOnGround = compound.getInteger("TicksOnGround");
        }
        
        @Override
        protected void writeEntityToNBT(NBTTagCompound compound) {
            compound.setInteger("TicksOnGround", this.ticksOnGround);
        }
        
        @Override
        public void onUpdate() {
            super.onUpdate();
            
            // 如果着火则熄灭
            if (isBurning()) {
                extinguish();
            }
            
            if (onGround) {
                if (this.ticksOnGround == 0) {
                    // 第一次着地时对周围生物造成伤害
                    List<EntityLivingBase> entities = this.world.getEntitiesWithinAABB(EntityLivingBase.class, 
                        getEntityBoundingBox().grow(2.0D));
                    
                    for (EntityLivingBase entity : entities) {
                        if (entity != this.owner && entity != this) {
                            entity.attackEntityFrom(DamageSource.GENERIC, 10.0F);
                            entity.addPotionEffect(new PotionEffect(MobEffects.SLOWNESS, 100, 2, false, false));
                        }
                    }
                }
                this.ticksOnGround++;
                
                // 10秒后消失
                if (this.ticksOnGround > 200) {
                    setDead();
                }
            } else {
                // 下落
                move(MoverType.SELF, 0, -0.5, 0);
            }
        }
        
        @Override
        public boolean canBeCollidedWith() {
            return true;
        }
        
        @Override
        public boolean canBePushed() {
            return false;
        }
        
        @Override
        public boolean attackEntityFrom(DamageSource source, float amount) {
            // 只有主人可以破坏立方体
            if (source.getTrueSource() == this.owner) {
                setDead();
                return true;
            }
            return false;
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class RenderCustom extends Render<DaikokutenCube> {
        public RenderCustom(RenderManager renderManager) {
            super(renderManager);
        }
        
        @Override
        public void doRender(DaikokutenCube entity, double x, double y, double z, float entityYaw, float partialTicks) {
            GlStateManager.pushMatrix();
            GlStateManager.translate((float)x, (float)y, (float)z);
            GlStateManager.enableRescaleNormal();
            GlStateManager.scale(-1.0F, -1.0F, 1.0F);
            GlStateManager.enableAlpha();
            
            // 绑定纹理
            bindEntityTexture(entity);
            
            // 渲染立方体模型
            ModelCube model = new ModelCube();
            model.render(entity, 0.0F, 0.0F, -0.1F, 0.0F, 0.0F, 0.0625F);
            
            GlStateManager.popMatrix();
            super.doRender(entity, x, y, z, entityYaw, partialTicks);
        }
        
        @Override
        protected ResourceLocation getEntityTexture(DaikokutenCube entity) {
            return new ResourceLocation("narutomod:textures/entity/daikokuten_cube.png");
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class ModelCube extends ModelBase {
        private final ModelRenderer cube;
        
        public ModelCube() {
            textureWidth = 64;
            textureHeight = 64;
            
            cube = new ModelRenderer(this);
            cube.setRotationPoint(0.0F, 0.0F, 0.0F);
            cube.cubeList.add(new ModelBox(cube, 0, 0, -2.5F, -2.5F, -2.5F, 5, 5, 5, 0.0F, false));
        }
        
        @Override
        public void render(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
            cube.render(f5);
        }
        
        public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
            modelRenderer.rotateAngleX = x;
            modelRenderer.rotateAngleY = y;
            modelRenderer.rotateAngleZ = z;
        }
    }
}
