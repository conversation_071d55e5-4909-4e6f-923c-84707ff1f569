/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.HashMap;
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.item.ItemKokugan;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.item.ItemStack;
/*    */ import net.minecraft.world.World;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSpecialJutsu3OnKeyPressedAddon extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSpecialJutsu3OnKeyPressedAddon(ElementsBorutomodaddononeroMod instance) {
/* 16 */     super(instance, 106);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 20 */     if (dependencies.get("is_pressed") == null) {
/* 21 */       System.err.println("Failed to load dependency is_pressed for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 24 */     if (dependencies.get("entity") == null) {
/* 25 */       System.err.println("Failed to load dependency entity for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 28 */     if (dependencies.get("x") == null) {
/* 29 */       System.err.println("Failed to load dependency x for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 32 */     if (dependencies.get("y") == null) {
/* 33 */       System.err.println("Failed to load dependency y for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 36 */     if (dependencies.get("z") == null) {
/* 37 */       System.err.println("Failed to load dependency z for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 40 */     if (dependencies.get("world") == null) {
/* 41 */       System.err.println("Failed to load dependency world for procedure SpecialJutsu3OnKeyPressedAddon!");
/*    */       return;
/*    */     } 
/* 44 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 45 */     Entity entity = (Entity)dependencies.get("entity");
/* 46 */     int x = ((Integer)dependencies.get("x")).intValue();
/* 47 */     int y = ((Integer)dependencies.get("y")).intValue();
/* 48 */     int z = ((Integer)dependencies.get("z")).intValue();
/* 49 */     World world = (World)dependencies.get("world");
/* 50 */     ItemStack helmet = ItemStack.field_190927_a;
/* 51 */     if (world.field_72995_K || ((EntityPlayer)entity).func_175149_v()) {
/*    */       return;
/*    */     }
/* 54 */     helmet = (entity instanceof EntityPlayer) ? (ItemStack)((EntityPlayer)entity).field_71071_by.field_70460_b.get(3) : ItemStack.field_190927_a;
/* 55 */     if (helmet.func_77973_b() == (new ItemStack(ItemKokugan.helmet, 1)).func_77973_b())
/* 56 */       if (entity.func_70093_af()) {
/*    */         
/* 58 */         Map<String, Object> $_dependencies = new HashMap<>();
/* 59 */         $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 60 */         $_dependencies.put("entity", entity);
/* 61 */         $_dependencies.put("x", Integer.valueOf(x));
/* 62 */         $_dependencies.put("y", Integer.valueOf(y));
/* 63 */         $_dependencies.put("z", Integer.valueOf(z));
/* 64 */         $_dependencies.put("world", world);
/* 65 */         ProcedureSpear.executeProcedure($_dependencies);
/*    */       }
/*    */       else {
/*    */         
/* 69 */         Map<String, Object> $_dependencies = new HashMap<>();
/* 70 */         $_dependencies.put("is_pressed", Boolean.valueOf(is_pressed));
/* 71 */         $_dependencies.put("entity", entity);
/* 72 */         $_dependencies.put("world", world);
/* 73 */         ProcedureSamidareSpear.executeProcedure($_dependencies);
/*    */       }  
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSpecialJutsu3OnKeyPressedAddon.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */