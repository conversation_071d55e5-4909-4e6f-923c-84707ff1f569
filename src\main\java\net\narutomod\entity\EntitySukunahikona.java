package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.procedure.ProcedureUtils;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.event.FMLInitializationEvent;
import net.minecraftforge.fml.client.registry.RenderingRegistry;
import net.minecraftforge.common.MinecraftForge;
import net.minecraftforge.fml.relauncher.ReflectionHelper;

import net.minecraft.world.World;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.DamageSource;
import net.minecraft.util.math.MathHelper;
import net.minecraft.entity.player.EntityPlayer;
import net.minecraft.entity.ai.attributes.AttributeModifier;
import net.minecraft.entity.SharedMonsterAttributes;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.minecraft.client.renderer.entity.RenderManager;
import net.minecraft.client.renderer.entity.Render;
import net.minecraft.client.entity.AbstractClientPlayer;
import net.minecraft.client.Minecraft;

@ElementsNarutomodMod.ModElement.Tag
public class EntitySukunahikona extends ElementsNarutomodMod.ModElement {
    public static final int ENTITYID = 604;
    
    public EntitySukunahikona(ElementsNarutomodMod instance) {
        super(instance, 604);
    }
    
    @Override
    public void initElements() {
        elements.entities.add(() -> EntityEntryBuilder.create().entity(Sukunahikona.class)
            .id(new ResourceLocation("narutomod", "sukunahikonaentity"), ENTITYID)
            .name("sukunahikonaentity").tracker(64, 1, true).build());
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void preInit(FMLPreInitializationEvent event) {
        RenderingRegistry.registerEntityRenderingHandler(Sukunahikona.class, renderManager -> new RenderSmallerMe(renderManager));
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void init(FMLInitializationEvent event) {
        MinecraftForge.EVENT_BUS.register(new PlayerRenderHook());
    }
    
    public static class Sukunahikona extends EntityClone.Base {
        private final int growTime = 20;
        private float scale;
        private EntityLivingBase targetEntity;
        
        public Sukunahikona(World world) {
            super(world);
        }
        
        public Sukunahikona(EntityLivingBase user, float scaleIn) {
            super(user);
            this.scale = scaleIn;
            this.stepHeight = scaleIn * this.height / 3.0F;
            setCanPickUpLoot(true);
            double d = MathHelper.sqrt(4.0D * scaleIn * scaleIn + (this.height * this.height));
            getEntityAttribute(EntityPlayer.REACH_DISTANCE).applyModifier(new AttributeModifier("sukunahikona.reach", d, 0));
            getEntityAttribute(SharedMonsterAttributes.ATTACK_DAMAGE).applyModifier(new AttributeModifier("sukunahikona.damage", (scaleIn * scaleIn), 0));
            addPotionEffect(new PotionEffect(MobEffects.INVISIBILITY, 999999, (int)scaleIn, false, false));
            user.startRiding(this);
        }
        
        @Override
        protected void applyEntityAttributes() {
            super.applyEntityAttributes();
            getAttributeMap().registerAttribute(EntityPlayer.REACH_DISTANCE);
            getEntityAttribute(EntityPlayer.REACH_DISTANCE).setBaseValue(2.0D);
        }
        
        @Override
        public double getMountedYOffset() {
            return this.height - 1.3D;
        }
        
        @Override
        public boolean shouldRiderSit() {
            return false;
        }
        
        @Override
        public boolean canBeSteered() {
            return true;
        }
        
        @Override
        public Entity getControllingPassenger() {
            return getPassengers().isEmpty() ? null : getPassengers().get(0);
        }
        
        @Override
        public boolean attackEntityFrom(DamageSource source, float amount) {
            if (source.getTrueSource() != null && source.getTrueSource().equals(getSummoner())) {
                return false;
            }
            if (getControllingPassenger() != null) {
                return getControllingPassenger().attackEntityFrom(source, amount);
            }
            return super.attackEntityFrom(source, amount);
        }
        
        @Override
        public void travel(float strafe, float vertical, float forward) {
            if (isBeingRidden() && canBeSteered()) {
                Entity entity = getControllingPassenger();
                this.rotationYaw = entity.rotationYaw;
                this.prevRotationYaw = this.rotationYaw;
                this.rotationPitch = entity.rotationPitch;
                setRotation(this.rotationYaw, this.rotationPitch);
                this.renderYawOffset = getRotationYawHead() * 0.15F;
                this.rotationYawHead = entity.rotationYaw;
                this.prevRotationYawHead = entity.rotationYaw;
                this.stepHeight = this.height / 3.0F;
                
                if (entity instanceof EntityLivingBase) {
                    checkJump((EntityLivingBase)entity);
                    setAIMoveSpeed((float)ProcedureUtils.getModifiedSpeed(this) / 3.5F);
                    float forwardInput = ((EntityLivingBase)entity).moveForward;
                    float strafeInput = ((EntityLivingBase)entity).moveStrafing;
                    super.travel(strafeInput, 0.0F, forwardInput);
                }
            } else {
                this.renderYawOffset = 0.02F;
                super.travel(strafe, vertical, forward);
            }
        }
        
        private void checkJump(EntityLivingBase entity) {
            if (this.world.isRemote && this.onGround) {
                // 使用反射获取跳跃状态
                try {
                    Boolean isJumping = (Boolean)ReflectionHelper.getPrivateValue(EntityLivingBase.class, entity, 49);
                    if (isJumping != null && isJumping.booleanValue()) {
                        jump();
                    }
                } catch (Exception e) {
                    // 忽略反射错误
                }
            }
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class RenderSmallerMe extends Render<Sukunahikona> {
        public RenderSmallerMe(RenderManager renderManager) {
            super(renderManager);
        }
        
        @Override
        public void doRender(Sukunahikona entity, double x, double y, double z, float entityYaw, float partialTicks) {
            // 不渲染实体本身，因为玩家会被缩小渲染
        }
        
        @Override
        protected ResourceLocation getEntityTexture(Sukunahikona entity) {
            return null;
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class PlayerRenderHook {
        // 这里可以添加玩家渲染钩子来实现缩小效果
        // 由于复杂性，暂时保持简单实现
    }
}
