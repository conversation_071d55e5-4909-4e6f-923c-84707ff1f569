/*     */ package net.mcreator.borutomodaddononero;
/*     */ 
/*     */ import com.google.common.collect.Maps;
/*     */ import java.lang.reflect.Field;
/*     */ import java.util.Iterator;
/*     */ import java.util.Map;
/*     */ import javax.annotation.Nullable;
/*     */ import net.minecraft.client.Minecraft;
/*     */ import net.minecraft.client.entity.AbstractClientPlayer;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBiped;
/*     */ import net.minecraft.client.model.ModelPlayer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.block.model.ItemCameraTransforms;
/*     */ import net.minecraft.client.renderer.entity.RenderLivingBase;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.client.renderer.entity.RenderPlayer;
/*     */ import net.minecraft.client.renderer.entity.layers.LayerBipedArmor;
/*     */ import net.minecraft.client.renderer.entity.layers.LayerRenderer;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.player.EntityPlayer;
/*     */ import net.minecraft.inventory.EntityEquipmentSlot;
/*     */ import net.minecraft.item.ItemArmor;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.ModConfig;
/*     */ import net.narutomod.item.ItemOnBody;
/*     */ import net.narutomod.procedure.ProcedureSync;
/*     */ import net.narutomod.procedure.ProcedureUtils;
/*     */ 
/*     */ 
/*     */ 
/*     */ @Tag
/*     */ public class PlayerRender
/*     */   extends ElementsBorutomodaddononeroMod.ModElement
/*     */ {
/*     */   private static PlayerRender INSTANCE;
/*     */   private static final String CLONETARGETID = "SkinCloningTargetId";
/*     */   private static final String CLONETARGETLAYERS = "SkinCloningRenderTargetLayers";
/*     */   private static final String PLAYERTRANSPARENT = "PlayerRenderTransparent";
/*     */   private static final String COLORMULTIPLIER = "SkinColorMultiplier";
/*     */   private RenderPlayer playerRenderer;
/*     */   
/*     */   public PlayerRender(ElementsBorutomodaddononeroMod instance) {
/*  50 */     super(instance, 608);
/*  51 */     INSTANCE = this;
/*     */   }
/*     */   
/*     */   public static PlayerRender getInstance() {
/*  55 */     return INSTANCE;
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  61 */     RenderManager renderManager = Minecraft.func_71410_x().func_175598_ae();
/*  62 */     this.playerRenderer = new Renderer(renderManager);
/*     */     try {
/*  64 */       Field skinMap = ProcedureUtils.getFieldByIndex(renderManager.getClass(), RenderManager.class, 1);
/*  65 */       if (skinMap.getType() == Map.class) {
/*  66 */         Map<String, RenderPlayer> map = (Map<String, RenderPlayer>)skinMap.get(renderManager);
/*  67 */         map.put("default", this.playerRenderer);
/*  68 */         map.put("slim", new Renderer(renderManager, true));
/*     */       } 
/*  70 */       Field renderer = ProcedureUtils.getFieldByIndex(renderManager.getClass(), RenderManager.class, 2);
/*  71 */       if (renderer.getType() == RenderPlayer.class) {
/*  72 */         renderer.set(renderManager, this.playerRenderer);
/*     */       }
/*  74 */     } catch (Exception e) {
/*  75 */       throw new RuntimeException("RenderManager hook");
/*     */     } 
/*     */   }
/*     */   
/*     */   public static void setSkinCloneTarget(EntityPlayer entity, @Nullable EntityPlayer target) {
/*  80 */     setSkinCloneTarget(entity, target, true);
/*     */   }
/*     */   
/*     */   public static void setSkinCloneTarget(EntityPlayer entity, @Nullable EntityPlayer target, boolean renderLayers) {
/*  84 */     if (target != null) {
/*  85 */       ProcedureSync.EntityNBTTag.setAndSync((Entity)entity, "SkinCloningTargetId", target.func_145782_y());
/*  86 */       ProcedureSync.EntityNBTTag.setAndSync((Entity)entity, "SkinCloningRenderTargetLayers", renderLayers);
/*     */     } else {
/*  88 */       ProcedureSync.EntityNBTTag.removeAndSync((Entity)entity, "SkinCloningTargetId");
/*  89 */       ProcedureSync.EntityNBTTag.removeAndSync((Entity)entity, "SkinCloningRenderTargetLayers");
/*     */     } 
/*     */   }
/*     */   
/*     */   @Nullable
/*     */   public static boolean hasSkinCloneTarget(EntityPlayer entity) {
/*  95 */     return entity.getEntityData().func_74764_b("SkinCloningTargetId");
/*     */   }
/*     */   
/*     */   public static void setTransparent(EntityPlayer entity, boolean set) {
/*  99 */     if (set) {
/*     */       
/* 101 */       ProcedureSync.EntityNBTTag.setAndSync((Entity)entity, "PlayerRenderTransparent", true);
/*     */     } else {
/*     */       
/* 104 */       ProcedureSync.EntityNBTTag.removeAndSync((Entity)entity, "PlayerRenderTransparent");
/*     */     } 
/*     */   }
/*     */   
/*     */   public static boolean isTransparent(EntityPlayer entity) {
/* 109 */     return entity.getEntityData().func_74767_n("PlayerRenderTransparent");
/*     */   }
/*     */   
/*     */   public static void setColorMultiplier(EntityPlayer entity, int color) {
/* 113 */     if ((color >> 24 & 0xFF) == 0) {
/*     */       
/* 115 */       ProcedureSync.EntityNBTTag.removeAndSync((Entity)entity, "SkinColorMultiplier");
/*     */     } else {
/*     */       
/* 118 */       ProcedureSync.EntityNBTTag.setAndSync((Entity)entity, "SkinColorMultiplier", color);
/*     */     } 
/*     */   }
/*     */   
/*     */   public static int getColorMultiplier(EntityPlayer entity) {
/* 123 */     return entity.getEntityData().func_74764_b("SkinColorMultiplier") ? entity.getEntityData().func_74762_e("SkinColorMultiplier") : 0;
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class Renderer extends RenderPlayer {
/*     */     public Renderer(RenderManager renderManager) {
/* 129 */       this(renderManager, false);
/*     */     }
/*     */     
/*     */     public Renderer(RenderManager renderManager, boolean useSmallArms) {
/* 133 */       super(renderManager, useSmallArms);
/* 134 */       Iterator<LayerRenderer> iter = this.field_177097_h.iterator();
/* 135 */       while (iter.hasNext()) {
/* 136 */         LayerRenderer renderer = iter.next();
/* 137 */         if (renderer instanceof LayerBipedArmor) {
/* 138 */           iter.remove();
/*     */         }
/*     */       } 
/* 141 */       func_177094_a((LayerRenderer)new PlayerRender.LayerArmorCustom(this));
/* 142 */       func_177094_a(new PlayerRender.LayerInventoryItem(this));
/*     */     }
/*     */ 
/*     */     
/*     */     protected void renderModel(AbstractClientPlayer entityIn, float f0, float f1, float f2, float f3, float f4, float f5) {
/* 147 */       if (PlayerRender.isTransparent((EntityPlayer)entityIn)) {
/* 148 */         if (func_180548_c((Entity)entityIn)) {
/* 149 */           GlStateManager.func_187408_a(GlStateManager.Profile.TRANSPARENT_MODEL);
/* 150 */           this.field_77045_g.func_78088_a((Entity)entityIn, f0, f1, f2, f3, f4, f5);
/* 151 */           GlStateManager.func_187440_b(GlStateManager.Profile.TRANSPARENT_MODEL);
/*     */         } 
/*     */       } else {
/* 154 */         boolean flag = func_193115_c((EntityLivingBase)entityIn);
/* 155 */         boolean flag1 = (!flag && !entityIn.func_98034_c((EntityPlayer)(Minecraft.func_71410_x()).field_71439_g));
/* 156 */         if (flag || flag1) {
/* 157 */           if (flag1) {
/* 158 */             GlStateManager.func_187408_a(GlStateManager.Profile.TRANSPARENT_MODEL);
/*     */           }
/* 160 */           func_180548_c((Entity)entityIn);
/* 161 */           ModelPlayer model = func_177087_b();
/* 162 */           if (PlayerRender.shouldNarutoRun((Entity)entityIn) && model.field_78095_p == 0.0F && model.field_187076_m == ModelBiped.ArmPose.EMPTY && model.field_187075_l == ModelBiped.ArmPose.EMPTY) {
/*     */             
/* 164 */             renderNarutoRun((ModelBiped)model, (Entity)entityIn, f0, f1, f2, f3, f4, f5);
/*     */           } else {
/* 166 */             model.func_78088_a((Entity)entityIn, f0, f1, f2, f3, f4, f5);
/*     */           } 
/* 168 */           if (flag1) {
/* 169 */             GlStateManager.func_187440_b(GlStateManager.Profile.TRANSPARENT_MODEL);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     public void renderNarutoRun(ModelBiped model, Entity entityIn, float f0, float f1, float f2, float f3, float f4, float scale) {
/* 176 */       model.field_78117_n = true;
/* 177 */       model.func_78087_a(f0, f1, f2, f3, f4, scale, entityIn);
/* 178 */       model.field_178723_h.field_78795_f = 1.4835F;
/* 179 */       model.field_178723_h.field_78796_g = -0.3927F;
/* 180 */       model.field_178724_i.field_78795_f = 1.4835F;
/* 181 */       model.field_178724_i.field_78796_g = 0.3927F;
/* 182 */       if (model instanceof ModelPlayer) {
/* 183 */         ((ModelPlayer)model).field_178732_b.field_78795_f = model.field_178723_h.field_78795_f;
/* 184 */         ((ModelPlayer)model).field_178732_b.field_78796_g = model.field_178723_h.field_78796_g;
/* 185 */         ((ModelPlayer)model).field_178734_a.field_78795_f = model.field_178724_i.field_78795_f;
/* 186 */         ((ModelPlayer)model).field_178734_a.field_78796_g = model.field_178724_i.field_78796_g;
/*     */       } 
/* 188 */       GlStateManager.func_179094_E();
/* 189 */       if (model.field_78091_s) {
/* 190 */         float f = 2.0F;
/* 191 */         GlStateManager.func_179152_a(0.75F, 0.75F, 0.75F);
/* 192 */         GlStateManager.func_179109_b(0.0F, 16.0F * scale, 0.0F);
/* 193 */         model.field_78116_c.func_78785_a(scale);
/* 194 */         GlStateManager.func_179121_F();
/* 195 */         GlStateManager.func_179094_E();
/* 196 */         GlStateManager.func_179152_a(0.5F, 0.5F, 0.5F);
/* 197 */         GlStateManager.func_179109_b(0.0F, 24.0F * scale, 0.0F);
/* 198 */         model.field_78115_e.func_78785_a(scale);
/* 199 */         model.field_178723_h.func_78785_a(scale);
/* 200 */         model.field_178724_i.func_78785_a(scale);
/* 201 */         model.field_178721_j.func_78785_a(scale);
/* 202 */         model.field_178722_k.func_78785_a(scale);
/* 203 */         model.field_178720_f.func_78785_a(scale);
/* 204 */         if (model instanceof ModelPlayer) {
/* 205 */           ((ModelPlayer)model).field_178733_c.func_78785_a(scale);
/* 206 */           ((ModelPlayer)model).field_178731_d.func_78785_a(scale);
/* 207 */           ((ModelPlayer)model).field_178734_a.func_78785_a(scale);
/* 208 */           ((ModelPlayer)model).field_178732_b.func_78785_a(scale);
/* 209 */           ((ModelPlayer)model).field_178730_v.func_78785_a(scale);
/*     */         } 
/*     */       } else {
/* 212 */         if (entityIn.func_70093_af()) {
/* 213 */           GlStateManager.func_179109_b(0.0F, 0.2F, 0.0F);
/*     */         }
/* 215 */         model.field_78116_c.func_78785_a(scale);
/* 216 */         model.field_78115_e.func_78785_a(scale);
/* 217 */         model.field_178723_h.func_78785_a(scale);
/* 218 */         model.field_178724_i.func_78785_a(scale);
/* 219 */         model.field_178721_j.func_78785_a(scale);
/* 220 */         model.field_178722_k.func_78785_a(scale);
/* 221 */         model.field_178720_f.func_78785_a(scale);
/* 222 */         if (model instanceof ModelPlayer) {
/* 223 */           ((ModelPlayer)model).field_178733_c.func_78785_a(scale);
/* 224 */           ((ModelPlayer)model).field_178731_d.func_78785_a(scale);
/* 225 */           ((ModelPlayer)model).field_178734_a.func_78785_a(scale);
/* 226 */           ((ModelPlayer)model).field_178732_b.func_78785_a(scale);
/* 227 */           ((ModelPlayer)model).field_178730_v.func_78785_a(scale);
/*     */         } 
/*     */       } 
/* 230 */       GlStateManager.func_179121_F();
/*     */     }
/*     */ 
/*     */     
/*     */     protected void renderLayers(AbstractClientPlayer entity, float f0, float f1, float f2, float f3, float f4, float f5, float f6) {
/* 235 */       if (!entity.func_82150_aj() || !entity.func_98034_c((EntityPlayer)(Minecraft.func_71410_x()).field_71439_g)) {
/* 236 */         AbstractClientPlayer target = getSkinCloneTarget((Entity)entity);
/* 237 */         if (target != null && entity.getEntityData().func_74767_n("SkinCloningRenderTargetLayers")) {
/* 238 */           if (target.func_70093_af()) {
/* 239 */             GlStateManager.func_179109_b(0.0F, -0.2F, 0.0F);
/*     */           }
/* 241 */           if (entity.func_70093_af()) {
/* 242 */             GlStateManager.func_179109_b(0.0F, 0.2F, 0.0F);
/*     */           }
/*     */         } else {
/* 245 */           target = entity;
/*     */         } 
/* 247 */         super.func_177093_a((EntityLivingBase)target, f0, f1, f2, f3, f4, f5, f6);
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public ResourceLocation func_110775_a(AbstractClientPlayer entity) {
/* 253 */       AbstractClientPlayer target = getSkinCloneTarget((Entity)entity);
/* 254 */       return (target != null) ? target.func_110306_p() : super.func_110775_a(entity);
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_188296_a(AbstractClientPlayer entityIn, double x, double y, double z, String name, double distanceSq) {
/* 259 */       if (entityIn.func_174833_aM()) {
/* 260 */         AbstractClientPlayer target = getSkinCloneTarget((Entity)entityIn);
/* 261 */         if (target != null) {
/* 262 */           super.func_188296_a(entityIn, x, y, z, target.func_70005_c_(), distanceSq);
/*     */         } else {
/* 264 */           super.func_188296_a(entityIn, x, y, z, name, distanceSq);
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     protected int getColorMultiplier(AbstractClientPlayer entityIn, float lightBrightness, float partialTickTime) {
/* 271 */       int color = PlayerRender.getColorMultiplier((EntityPlayer)entityIn);
/* 272 */       if ((color >> 24 & 0xFF) > 0) {
/* 273 */         return color;
/*     */       }
/* 275 */       return super.func_77030_a((EntityLivingBase)entityIn, lightBrightness, partialTickTime);
/*     */     }
/*     */     
/*     */     @Nullable
/*     */     private AbstractClientPlayer getSkinCloneTarget(Entity entity) {
/* 280 */       if (entity.getEntityData().func_74764_b("SkinCloningTargetId")) {
/* 281 */         EntityPlayer entityPlayer; Entity target = null;
/* 282 */         for (EntityPlayer player : this.field_76990_c.field_78722_g.field_73010_i) {
/* 283 */           if (player.func_145782_y() == entity.getEntityData().func_74762_e("SkinCloningTargetId")) {
/* 284 */             entityPlayer = player;
/*     */             break;
/*     */           } 
/*     */         } 
/* 288 */         if (entityPlayer instanceof AbstractClientPlayer) {
/* 289 */           return (AbstractClientPlayer)entityPlayer;
/*     */         }
/* 291 */         entity.getEntityData().func_82580_o("SkinCloningTargetId");
/*     */       } 
/*     */       
/* 294 */       return null;
/*     */     }
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class LayerInventoryItem implements LayerRenderer<AbstractClientPlayer> {
/*     */     private final RenderPlayer playerRenderer;
/* 301 */     private static final Map<String, ResourceLocation> ARMOR_TEXTURE_RES_MAP = Maps.newHashMap();
/*     */     
/*     */     public LayerInventoryItem(RenderPlayer playerRendererIn) {
/* 304 */       this.playerRenderer = playerRendererIn;
/*     */     }
/*     */ 
/*     */     
/*     */     public void doRenderLayer(AbstractClientPlayer entityIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
/* 309 */       if (!entityIn.func_175149_v()) {
/* 310 */         for (int i = 0; i < entityIn.field_71071_by.func_70302_i_(); i++) {
/* 311 */           ItemStack stack = entityIn.field_71071_by.func_70301_a(i);
/*     */           
/* 313 */           if (stack.func_77973_b() instanceof ItemOnBody.Interface) {
/* 314 */             ItemOnBody.Interface item = (ItemOnBody.Interface)stack.func_77973_b();
/* 315 */             if (item.showSkinLayer()) {
/* 316 */               renderSkinLayer(stack, entityIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale);
/*     */             }
/* 318 */             if (item.showOnBody() != ItemOnBody.BodyPart.NONE && i != entityIn.field_71071_by.field_70461_c && i != 40) {
/* 319 */               renderItemOnBody(stack, entityIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale);
/*     */             }
/*     */           } 
/*     */         } 
/*     */       }
/*     */     }
/*     */     
/*     */     private void renderSkinLayer(ItemStack stack, AbstractClientPlayer entityIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
/* 327 */       ModelBiped model = stack.func_77973_b().getArmorModel((EntityLivingBase)entityIn, stack, EntityEquipmentSlot.HEAD, new ModelBiped(1.0F));
/* 328 */       if (model != null) {
/* 329 */         String s = stack.func_77973_b().getArmorTexture(stack, (Entity)entityIn, EntityEquipmentSlot.HEAD, null);
/* 330 */         if (s != null) {
/* 331 */           ResourceLocation resourcelocation = ARMOR_TEXTURE_RES_MAP.get(s);
/* 332 */           if (resourcelocation == null) {
/* 333 */             resourcelocation = new ResourceLocation(s);
/* 334 */             ARMOR_TEXTURE_RES_MAP.put(s, resourcelocation);
/*     */           } 
/* 336 */           model.field_78117_n = (this.playerRenderer.func_177087_b()).field_78117_n;
/* 337 */           model.func_78086_a((EntityLivingBase)entityIn, limbSwing, limbSwingAmount, partialTicks);
/* 338 */           this.playerRenderer.func_110776_a(resourcelocation);
/* 339 */           model.func_78088_a((Entity)entityIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     private void renderItemOnBody(ItemStack stack, AbstractClientPlayer entityIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
/* 345 */       Vec3d offset = ((ItemOnBody.Interface)stack.func_77973_b()).getOffset();
/* 346 */       ItemOnBody.BodyPart bodypart = ((ItemOnBody.Interface)stack.func_77973_b()).showOnBody();
/* 347 */       GlStateManager.func_179094_E();
/* 348 */       ModelPlayer modelPlayer = this.playerRenderer.func_177087_b();
/*     */       
/* 350 */       if (entityIn.func_70093_af()) {
/* 351 */         GlStateManager.func_179109_b(0.0F, 0.2F, 0.0F);
/*     */       }
/* 353 */       switch (bodypart) {
/*     */         case HEAD:
/* 355 */           ((ModelBiped)modelPlayer).field_78116_c.func_78794_c(0.0625F);
/*     */           break;
/*     */         case TORSO:
/* 358 */           ((ModelBiped)modelPlayer).field_78115_e.func_78794_c(0.0625F);
/*     */           break;
/*     */         case RIGHT_ARM:
/* 361 */           ((ModelBiped)modelPlayer).field_178723_h.func_78794_c(0.0625F);
/*     */           break;
/*     */         case LEFT_ARM:
/* 364 */           ((ModelBiped)modelPlayer).field_178724_i.func_78794_c(0.0625F);
/*     */           break;
/*     */         case RIGHT_LEG:
/* 367 */           ((ModelBiped)modelPlayer).field_178721_j.func_78794_c(0.0625F);
/*     */           break;
/*     */         case LEFT_LEG:
/* 370 */           ((ModelBiped)modelPlayer).field_178722_k.func_78794_c(0.0625F);
/*     */           break;
/*     */       } 
/* 373 */       GlStateManager.func_179131_c(1.0F, 1.0F, 1.0F, 1.0F);
/* 374 */       GlStateManager.func_179137_b(offset.field_72450_a, -0.25D + offset.field_72448_b, offset.field_72449_c);
/* 375 */       GlStateManager.func_179114_b(180.0F, 0.0F, 1.0F, 0.0F);
/* 376 */       GlStateManager.func_179152_a(0.625F, -0.625F, -0.625F);
/* 377 */       Minecraft.func_71410_x().func_175597_ag().func_178099_a((EntityLivingBase)entityIn, stack, ItemCameraTransforms.TransformType.HEAD);
/* 378 */       GlStateManager.func_179121_F();
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_177142_b() {
/* 383 */       return false;
/*     */     }
/*     */   }
/*     */   
/*     */   public static boolean shouldNarutoRun(Entity entity) {
/* 388 */     return (ModConfig.NARUTO_RUN && !entity.func_184218_aH() && (!(entity instanceof EntityPlayer) || !((EntityPlayer)entity).field_71075_bZ.field_75100_b) && entity
/*     */       
/* 390 */       .func_174791_d().func_178786_a(entity.field_70142_S, entity.field_70137_T, entity.field_70136_U).func_189985_c() >= 0.125D);
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class LayerArmorCustom extends LayerBipedArmor {
/*     */     private final PlayerRender.Renderer renderer;
/*     */     
/*     */     public LayerArmorCustom(PlayerRender.Renderer rendererIn) {
/* 398 */       super((RenderLivingBase)rendererIn);
/* 399 */       this.renderer = rendererIn;
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_177141_a(EntityLivingBase entitylivingbaseIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale) {
/* 404 */       renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.CHEST);
/* 405 */       renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.LEGS);
/* 406 */       renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.FEET);
/* 407 */       renderArmorLayer(entitylivingbaseIn, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale, EntityEquipmentSlot.HEAD);
/*     */     }
/*     */     
/*     */     private void renderArmorLayer(EntityLivingBase entityIn, float limbSwing, float limbSwingAmount, float partialTicks, float ageInTicks, float netHeadYaw, float headPitch, float scale, EntityEquipmentSlot slotIn) {
/* 411 */       ItemStack itemstack = entityIn.func_184582_a(slotIn);
/* 412 */       if (itemstack.func_77973_b() instanceof ItemArmor) {
/* 413 */         ItemArmor itemarmor = (ItemArmor)itemstack.func_77973_b();
/* 414 */         if (itemarmor.func_185083_B_() == slotIn) {
/* 415 */           ModelBiped t = (ModelBiped)func_188360_a(slotIn);
/* 416 */           t = getArmorModelHook(entityIn, itemstack, slotIn, t);
/* 417 */           ModelPlayer modelPlayer = this.renderer.func_177087_b();
/* 418 */           t.func_178686_a((ModelBase)modelPlayer);
/* 419 */           t.func_78086_a(entityIn, limbSwing, limbSwingAmount, partialTicks);
/* 420 */           func_188359_a(t, slotIn);
/* 421 */           this.renderer.func_110776_a(getArmorResource((Entity)entityIn, itemstack, slotIn, null));
/* 422 */           if (itemarmor.hasOverlay(itemstack)) {
/* 423 */             int i = itemarmor.func_82814_b(itemstack);
/* 424 */             float f = (i >> 16 & 0xFF) / 255.0F;
/* 425 */             float f1 = (i >> 8 & 0xFF) / 255.0F;
/* 426 */             float f2 = (i & 0xFF) / 255.0F;
/* 427 */             GlStateManager.func_179131_c(f, f1, f2, 1.0F);
/* 428 */             renderArmorModel(t, (Entity)entityIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);
/* 429 */             this.renderer.func_110776_a(getArmorResource((Entity)entityIn, itemstack, slotIn, "overlay"));
/*     */           } 
/*     */           
/* 432 */           GlStateManager.func_179131_c(1.0F, 1.0F, 1.0F, 1.0F);
/* 433 */           renderArmorModel(t, (Entity)entityIn, limbSwing, limbSwingAmount, ageInTicks, netHeadYaw, headPitch, scale);
/*     */           
/* 435 */           if (itemstack.func_77962_s()) {
/* 436 */             func_188364_a((RenderLivingBase)this.renderer, entityIn, (ModelBase)t, limbSwing, limbSwingAmount, partialTicks, ageInTicks, netHeadYaw, headPitch, scale);
/*     */           }
/*     */         } 
/*     */       } 
/*     */     }
/*     */     
/*     */     private void renderArmorModel(ModelBiped model, Entity entityIn, float f0, float f1, float f2, float f3, float f4, float f5) {
/* 443 */       if ((model.field_178723_h.field_78806_j || model.field_178724_i.field_78806_j) && 
/* 444 */         PlayerRender.shouldNarutoRun(entityIn) && model.field_78095_p == 0.0F && model.field_187076_m == ModelBiped.ArmPose.EMPTY && model.field_187075_l == ModelBiped.ArmPose.EMPTY) {
/*     */         
/* 446 */         if (model instanceof net.mcreator.borutomodaddononero.item.ItemBaryonCloak.ModelBaryonCloak) {
/*     */           
/* 448 */           model.func_78088_a(entityIn, f0, f1, f2, f3, f4, f5);
/*     */         } else {
/*     */           
/* 451 */           this.renderer.renderNarutoRun(model, entityIn, f0, f1, f2, f3, f4, f5);
/*     */         } 
/*     */       } else {
/* 454 */         model.func_78088_a(entityIn, f0, f1, f2, f3, f4, f5);
/*     */       } 
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\PlayerRender.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */