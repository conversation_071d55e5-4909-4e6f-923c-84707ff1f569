/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import java.util.List;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.model.ModelBase;
/*     */ import net.minecraft.client.model.ModelBox;
/*     */ import net.minecraft.client.model.ModelRenderer;
/*     */ import net.minecraft.client.renderer.GlStateManager;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.entity.MoverType;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.nbt.NBTTagCompound;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.DamageSource;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.client.model.obj.OBJLoader;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLInitializationEvent;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ 
/*     */ @Tag
/*     */ public class EntityDaikokutenCube
/*     */   extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 60;
/*     */   
/*     */   public EntityDaikokutenCube(ElementsBorutomodaddononeroMod instance) {
/*  36 */     super(instance, 263);
/*     */   }
/*     */   
/*     */   public void initElements() {
/*  40 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(DaikokutenCube.class).id(new ResourceLocation("borutomodaddononero", "daikokutencube"), 60).name("daikokutencube").tracker(64, 1, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  48 */     RenderingRegistry.registerEntityRenderingHandler(DaikokutenCube.class, renderManager -> new RenderCustom(renderManager));
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void init(FMLInitializationEvent event) {
/*  56 */     OBJLoader.INSTANCE.addDomain("borutomodaddononero");
/*     */   }
/*     */   
/*     */   public static class DaikokutenCube extends Entity {
/*  60 */     private int ticksOnGround = 0;
/*     */     private EntityLivingBase owner;
/*     */     
/*     */     public DaikokutenCube(World world) {
/*  64 */       super(world);
/*  65 */       func_70105_a(5.0F, 5.0F);
/*  66 */       this.field_70178_ae = true;
/*     */     }
/*     */     
/*     */     public DaikokutenCube(World world, double x, double y, double z) {
/*  70 */       super(world);
/*  71 */       func_70107_b(x, y, z);
/*  72 */       this.field_70178_ae = true;
/*     */     }
/*     */     
/*     */     public void setOwner(EntityLivingBase owner) {
/*  76 */       this.owner = owner;
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70088_a() {}
/*     */ 
/*     */     
/*     */     protected void func_70037_a(NBTTagCompound compound) {
/*  84 */       this.ticksOnGround = compound.func_74762_e("TicksOnGround");
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70014_b(NBTTagCompound compound) {
/*  89 */       compound.func_74768_a("TicksOnGround", this.ticksOnGround);
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/*  94 */       super.func_70071_h_();
/*  95 */       if (func_70027_ad()) {
/*  96 */         func_70066_B();
/*     */       }
/*  98 */       if (this.field_70122_E) {
/*  99 */         if (this.ticksOnGround == 0) {
/* 100 */           List<EntityLivingBase> entities = this.field_70170_p.func_72872_a(EntityLivingBase.class, func_174813_aQ().func_186662_g(2.0D));
/* 101 */           for (Entity entity : entities) {
/* 102 */             if (entity != this.owner && 
/* 103 */               entity instanceof EntityLivingBase) {
/* 104 */               entity.func_70097_a(DamageSource.field_76377_j, 100.0F);
/* 105 */               ((EntityLivingBase)entity).func_70690_d(new PotionEffect(MobEffects.field_76421_d, 200, 4, false, false));
/* 106 */               ((EntityLivingBase)entity).func_70690_d(new PotionEffect(MobEffects.field_76440_q, 200, 4, false, false));
/*     */             } 
/*     */           } 
/*     */         } 
/*     */         
/* 111 */         this.ticksOnGround++;
/* 112 */         if (this.ticksOnGround > 200) {
/* 113 */           func_70106_y();
/*     */         }
/*     */       } else {
/* 116 */         this.field_70181_x -= 0.02D;
/* 117 */         func_70091_d(MoverType.SELF, this.field_70159_w, this.field_70181_x, this.field_70179_y);
/*     */       } 
/* 119 */       List<Entity> collidingEntities = this.field_70170_p.func_72839_b(this, func_174813_aQ());
/* 120 */       for (Entity entity : collidingEntities) {
/* 121 */         if (!(entity instanceof DaikokutenCube)) {
/* 122 */           entity.field_70159_w = 0.0D;
/* 123 */           entity.field_70181_x = 0.0D;
/* 124 */           entity.field_70179_y = 0.0D;
/* 125 */           entity.func_70107_b(entity.field_70169_q, entity.field_70167_r, entity.field_70166_s);
/*     */         } 
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     public boolean func_70097_a(DamageSource source, float amount) {
/* 132 */       if (source.func_76347_k() || source == DamageSource.field_76371_c) {
/* 133 */         return false;
/*     */       }
/* 135 */       return super.func_70097_a(source, amount);
/*     */     }
/*     */     
/*     */     public void func_70015_d(int seconds) {}
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class RenderCustom
/*     */     extends Render<DaikokutenCube>
/*     */   {
/*     */     private final ModelBase model;
/*     */     
/*     */     public RenderCustom(RenderManager renderManager) {
/* 148 */       super(renderManager);
/* 149 */       this.model = new EntityDaikokutenCube.ModelDaikokutenCube();
/*     */     }
/*     */ 
/*     */     
/*     */     public void doRender(EntityDaikokutenCube.DaikokutenCube entity, double x, double y, double z, float entityYaw, float partialTicks) {
/* 154 */       GlStateManager.func_179094_E();
/* 155 */       GlStateManager.func_179137_b(x, y, z);
/* 156 */       func_180548_c(entity);
/* 157 */       this.model.func_78088_a(entity, 0.0F, 0.0F, 0.0F, 0.0F, 0.0F, 0.0625F);
/* 158 */       GlStateManager.func_179121_F();
/*     */     }
/*     */ 
/*     */     
/*     */     protected ResourceLocation getEntityTexture(EntityDaikokutenCube.DaikokutenCube entity) {
/* 163 */       return new ResourceLocation("borutomodaddononero:textures/entity/daikokutencube.png");
/*     */     }
/*     */   }
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public static class ModelDaikokutenCube
/*     */     extends ModelBase
/*     */   {
/*     */     private final ModelRenderer bb_main;
/*     */     
/*     */     public ModelDaikokutenCube() {
/* 175 */       this.field_78090_t = 64;
/* 176 */       this.field_78089_u = 64;
/*     */       
/* 178 */       this.bb_main = new ModelRenderer(this);
/* 179 */       this.bb_main.func_78793_a(0.0F, 24.0F, 0.0F);
/* 180 */       this.bb_main.field_78804_l.add(new ModelBox(this.bb_main, 0, 0, -8.0F, 8.0F, -8.0F, 16, 16, 16, 32.0F, false));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_78088_a(Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
/* 185 */       this.bb_main.func_78785_a(f5);
/*     */     }
/*     */     
/*     */     public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
/* 189 */       modelRenderer.field_78795_f = x;
/* 190 */       modelRenderer.field_78796_g = y;
/* 191 */       modelRenderer.field_78808_h = z;
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntityDaikokutenCube.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */