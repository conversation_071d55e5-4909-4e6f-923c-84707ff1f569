/*     */ package net.mcreator.borutomodaddononero.entity;
/*     */ 
/*     */ import javax.annotation.Nullable;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*     */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*     */ import net.minecraft.client.renderer.entity.Render;
/*     */ import net.minecraft.client.renderer.entity.RenderManager;
/*     */ import net.minecraft.entity.Entity;
/*     */ import net.minecraft.entity.EntityLivingBase;
/*     */ import net.minecraft.init.MobEffects;
/*     */ import net.minecraft.item.ItemStack;
/*     */ import net.minecraft.network.datasync.DataParameter;
/*     */ import net.minecraft.network.datasync.DataSerializers;
/*     */ import net.minecraft.network.datasync.EntityDataManager;
/*     */ import net.minecraft.potion.PotionEffect;
/*     */ import net.minecraft.util.ResourceLocation;
/*     */ import net.minecraft.util.SoundCategory;
/*     */ import net.minecraft.util.SoundEvent;
/*     */ import net.minecraft.util.math.MathHelper;
/*     */ import net.minecraft.util.math.RayTraceResult;
/*     */ import net.minecraft.util.math.Vec3d;
/*     */ import net.minecraft.world.World;
/*     */ import net.minecraftforge.fml.client.registry.RenderingRegistry;
/*     */ import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntry;
/*     */ import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
/*     */ import net.minecraftforge.fml.relauncher.Side;
/*     */ import net.minecraftforge.fml.relauncher.SideOnly;
/*     */ import net.narutomod.item.ItemJutsu;
/*     */ 
/*     */ @Tag
/*     */ public class EntitySamidareSpear extends ElementsBorutomodaddononeroMod.ModElement {
/*     */   public static final int ENTITYID = 39;
/*     */   
/*     */   public EntitySamidareSpear(ElementsBorutomodaddononeroMod instance) {
/*  36 */     super(instance, 293);
/*     */   }
/*     */   
/*     */   public void initElements() {
/*  40 */     this.elements.entities.add(() -> EntityEntryBuilder.create().entity(SamidareSpears.class).id(new ResourceLocation("borutomodaddononero", "samidare_spear"), 39).name("samidare_spear").tracker(64, 3, true).build());
/*     */   }
/*     */ 
/*     */ 
/*     */ 
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public void preInit(FMLPreInitializationEvent event) {
/*  48 */     RenderingRegistry.registerEntityRenderingHandler(SamidareSpears.class, renderManager -> new CustomRender(renderManager));
/*     */   }
/*     */   
/*     */   @SideOnly(Side.CLIENT)
/*     */   public class CustomRender extends EntitySpear.Renderer<SamidareSpears> {
/*  53 */     private final ResourceLocation TEXTURE = new ResourceLocation("borutomodaddononero:textures/samidare_spear.png");
/*     */     
/*     */     public CustomRender(RenderManager renderManagerIn) {
/*  56 */       super(renderManagerIn);
/*     */     }
/*     */ 
/*     */     
/*     */     protected ResourceLocation getEntityTexture(EntitySamidareSpear.SamidareSpears entity) {
/*  61 */       return this.TEXTURE;
/*     */     }
/*     */   }
/*     */   
/*     */   public static class SamidareSpears extends EntitySpear.Base {
/*  66 */     private static final DataParameter<Float> RAND_YAW = EntityDataManager.func_187226_a(SamidareSpears.class, DataSerializers.field_187193_c);
/*  67 */     private static final DataParameter<Float> RAND_PITCH = EntityDataManager.func_187226_a(SamidareSpears.class, DataSerializers.field_187193_c);
/*     */     
/*     */     public SamidareSpears(World world) {
/*  70 */       super(world);
/*  71 */       setColor(-1056964609);
/*  72 */       setRandYawPitch();
/*     */     }
/*     */     
/*     */     public SamidareSpears(EntityLivingBase userIn) {
/*  76 */       super(userIn, -1056964609);
/*  77 */       setRandYawPitch();
/*     */     }
/*     */     
/*     */     public SamidareSpears(World world, EntityLivingBase userIn) {
/*  81 */       super(userIn, -1056964609);
/*  82 */       setRandYawPitch();
/*     */     }
/*     */ 
/*     */     
/*     */     protected void func_70088_a() {
/*  87 */       super.func_70088_a();
/*  88 */       this.field_70180_af.func_187214_a(RAND_YAW, Float.valueOf(0.0F));
/*  89 */       this.field_70180_af.func_187214_a(RAND_PITCH, Float.valueOf(0.0F));
/*     */     }
/*     */     
/*     */     private float getRandYaw() {
/*  93 */       return ((Float)this.field_70180_af.func_187225_a(RAND_YAW)).floatValue();
/*     */     }
/*     */     
/*     */     private float getRandPitch() {
/*  97 */       return ((Float)this.field_70180_af.func_187225_a(RAND_PITCH)).floatValue();
/*     */     }
/*     */     
/*     */     private void setRandYawPitch() {
/* 101 */       this.field_70180_af.func_187227_b(RAND_YAW, Float.valueOf((this.field_70146_Z.nextFloat() - 0.5F) * 90.0F));
/* 102 */       this.field_70180_af.func_187227_b(RAND_PITCH, Float.valueOf((this.field_70146_Z.nextFloat() - 0.5F) * 60.0F));
/*     */     }
/*     */ 
/*     */     
/*     */     public void func_70071_h_() {
/* 107 */       super.func_70071_h_();
/* 108 */       if (!isLaunched() && !func_189652_ae() && !this.field_70122_E) {
/* 109 */         this.field_70177_z += getRandYaw();
/* 110 */         this.field_70125_A += getRandPitch();
/*     */       } 
/*     */     }
/*     */ 
/*     */     
/*     */     protected void onImpact(RayTraceResult result) {
/* 116 */       if (!this.field_70170_p.field_72995_K && result.field_72308_g instanceof EntityLivingBase && 
/* 117 */         !result.field_72308_g.equals(this.shootingEntity)) {
/* 118 */         ((EntityLivingBase)result.field_72308_g).func_70690_d(new PotionEffect(MobEffects.field_76421_d, 200, 1, false, false));
/* 119 */         result.field_72308_g.field_70172_ad = 0;
/* 120 */         result.field_72308_g.func_70097_a(ItemJutsu.causeJutsuDamage(this, this.shootingEntity).func_76349_b(), 10.0F);
/* 121 */         func_70106_y();
/*     */       } 
/*     */     }
/*     */     
/*     */     public void shoot(Entity shooter, float pitch, float yaw, float p_184538_4_, float velocity, float inaccuracy) {
/* 126 */       Vec3d vec3d = shooter.func_70040_Z();
/* 127 */       Vec3d startVec = shooter.func_174824_e(1.0F);
/* 128 */       shoot(startVec.field_72450_a, startVec.field_72448_b, startVec.field_72449_c, vec3d.field_72450_a, vec3d.field_72448_b, vec3d.field_72449_c, velocity, inaccuracy);
/*     */     }
/*     */     
/*     */     public void shoot(double startX, double startY, double startZ, double directionX, double directionY, double directionZ, float velocity, float inaccuracy) {
/* 132 */       float f = MathHelper.func_76133_a(directionX * directionX + directionY * directionY + directionZ * directionZ);
/* 133 */       directionX /= f;
/* 134 */       directionY /= f;
/* 135 */       directionZ /= f;
/* 136 */       directionX += this.field_70146_Z.nextGaussian() * 0.0075D * inaccuracy;
/* 137 */       directionY += this.field_70146_Z.nextGaussian() * 0.0075D * inaccuracy;
/* 138 */       directionZ += this.field_70146_Z.nextGaussian() * 0.0075D * inaccuracy;
/* 139 */       directionX *= velocity;
/* 140 */       directionY *= velocity;
/* 141 */       directionZ *= velocity;
/* 142 */       this.field_70159_w = directionX;
/* 143 */       this.field_70181_x = directionY;
/* 144 */       this.field_70179_y = directionZ;
/* 145 */       float f1 = MathHelper.func_76133_a(directionX * directionX + directionZ * directionZ);
/* 146 */       this.field_70177_z = (float)(MathHelper.func_181159_b(directionX, directionZ) * 57.29577951308232D);
/* 147 */       this.field_70125_A = (float)(MathHelper.func_181159_b(directionY, f1) * 57.29577951308232D);
/* 148 */       this.field_70126_B = this.field_70177_z;
/* 149 */       this.field_70127_C = this.field_70125_A;
/*     */     }
/*     */     
/*     */     public static class Jutsu
/*     */       implements ItemJutsu.IJutsuCallback {
/*     */       public boolean createJutsu(ItemStack stack, EntityLivingBase entity, float power) {
/* 155 */         Vec3d vec = entity.func_70040_Z();
/* 156 */         Vec3d vec1 = entity.func_174824_e(1.0F).func_178787_e(vec.func_186678_a(1.5D));
/* 157 */         for (int i = 0; i < (int)(power * 3.0F); i++) {
/* 158 */           Vec3d vec2 = vec1.func_72441_c(entity.func_70681_au().nextDouble() - 0.5D, entity.func_70681_au().nextDouble() - 0.5D, entity.func_70681_au().nextDouble() - 0.5D);
/* 159 */           Vec3d vec3 = vec2.func_178787_e(vec);
/* 160 */           createJutsu(entity.field_70170_p, entity, vec2.field_72450_a, vec2.field_72448_b, vec2.field_72449_c, vec3.field_72450_a, vec3.field_72448_b, vec3.field_72449_c, 0.95F, 0.05F);
/*     */         } 
/* 162 */         return true;
/*     */       }
/*     */       
/*     */       public void createJutsu(EntityLivingBase attacker, EntityLivingBase target, float power) {
/* 166 */         Vec3d vec1 = attacker.func_174824_e(1.0F).func_178787_e(attacker.func_70040_Z().func_186678_a(1.5D));
/* 167 */         for (int i = 0; i < (int)(power * 3.0F); i++) {
/* 168 */           Vec3d vec2 = vec1.func_72441_c(attacker.func_70681_au().nextDouble() - 0.5D, attacker.func_70681_au().nextDouble() - 0.5D, attacker.func_70681_au().nextDouble() - 0.5D);
/* 169 */           createJutsu(attacker.field_70170_p, attacker, vec2.field_72450_a, vec2.field_72448_b, vec2.field_72449_c, target.field_70165_t, target.field_70163_u + (target.field_70131_O / 2.0F), target.field_70161_v, 0.95F, 0.05F);
/*     */         } 
/*     */       }
/*     */       
/*     */       public void createJutsu(World world, int num, double fromX, double fromY, double fromZ, double toX, double toY, double toZ, float speed, float inaccuracy) {
/* 174 */         for (int i = 0; i < num; i++) {
/* 175 */           createJutsu(world, (EntityLivingBase)null, fromX, fromY, fromZ, toX, toY, toZ, speed, inaccuracy);
/*     */         }
/*     */       }
/*     */       
/*     */       public void createJutsu(World world, @Nullable EntityLivingBase shooter, double fromX, double fromY, double fromZ, double toX, double toY, double toZ, float speed, float inaccuracy) {
/* 180 */         world.func_184148_a(null, fromX, fromY, fromZ, (SoundEvent)SoundEvent.field_187505_a.func_82594_a(new ResourceLocation("borutomodaddononero:samidare_spear")), SoundCategory.NEUTRAL, 0.8F, world.field_73012_v
/* 181 */             .nextFloat() * 0.4F + 0.8F);
/* 182 */         EntitySamidareSpear.SamidareSpears entity1 = (shooter != null) ? new EntitySamidareSpear.SamidareSpears(shooter) : new EntitySamidareSpear.SamidareSpears(world);
/* 183 */         entity1.setEntityScale(0.5F);
/* 184 */         entity1.func_70107_b(fromX, fromY, fromZ);
/* 185 */         entity1.func_70186_c(toX - fromX, toY - fromY, toZ - fromZ, speed, inaccuracy);
/* 186 */         entity1.func_189654_d(true);
/* 187 */         world.func_72838_d(entity1);
/*     */       }
/*     */     }
/*     */     
/*     */     public static void spawnShatteredShard(World worldIn, double x, double y, double z, double mX, double mY, double mZ) {
/* 192 */       SamidareSpears entity = new SamidareSpears(worldIn);
/* 193 */       entity.setEntityScale(worldIn.field_73012_v.nextFloat() * 0.5F + 0.05F);
/* 194 */       entity.func_70080_a(x, y, z, entity.getRandYaw(), entity.getRandPitch());
/* 195 */       entity.field_70159_w = mX;
/* 196 */       entity.field_70181_x = mY;
/* 197 */       entity.field_70179_y = mZ;
/* 198 */       worldIn.func_72838_d(entity);
/*     */     }
/*     */   }
/*     */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\entity\EntitySamidareSpear.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */