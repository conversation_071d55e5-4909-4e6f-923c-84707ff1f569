package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.item.ItemJutsu;

import net.minecraftforge.fml.relauncher.SideOnly;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.client.registry.RenderingRegistry;

import net.minecraft.world.World;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.util.math.MathHelper;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.entity.Entity;
import net.minecraft.item.ItemStack;
import net.minecraft.network.datasync.DataParameter;
import net.minecraft.network.datasync.DataSerializers;
import net.minecraft.network.datasync.EntityDataManager;
import net.minecraft.init.MobEffects;
import net.minecraft.potion.PotionEffect;
import net.minecraft.client.renderer.entity.RenderManager;
import net.minecraft.client.renderer.entity.Render;
import net.minecraft.client.renderer.GlStateManager;
import net.minecraft.client.model.ModelBase;
import net.minecraft.client.model.ModelRenderer;
import net.minecraft.client.model.ModelBox;

import javax.annotation.Nullable;
import javax.annotation.Nonnull;

@ElementsNarutomodMod.ModElement.Tag
public class EntitySamidareSpear extends ElementsNarutomodMod.ModElement {
    public static final int ENTITYID = 608;
    
    public EntitySamidareSpear(ElementsNarutomodMod instance) {
        super(instance, 608);
    }
    
    @Override
    public void initElements() {
        elements.entities.add(() -> EntityEntryBuilder.create().entity(SamidareSpears.class)
            .id(new ResourceLocation("narutomod", "samidare_spear"), ENTITYID)
            .name("samidare_spear").tracker(64, 3, true).build());
    }
    
    @SideOnly(Side.CLIENT)
    @Override
    public void preInit(FMLPreInitializationEvent event) {
        RenderingRegistry.registerEntityRenderingHandler(SamidareSpears.class, renderManager -> new CustomRender(renderManager));
    }
    
    public static class SamidareSpears extends EntityScalableProjectile.Base implements ItemJutsu.IJutsu {
        private static final DataParameter<Float> RAND_YAW = EntityDataManager.<Float>createKey(SamidareSpears.class, DataSerializers.FLOAT);
        private static final DataParameter<Float> RAND_PITCH = EntityDataManager.<Float>createKey(SamidareSpears.class, DataSerializers.FLOAT);
        private static final DataParameter<Integer> COLOR = EntityDataManager.<Integer>createKey(SamidareSpears.class, DataSerializers.VARINT);
        
        public SamidareSpears(World world) {
            super(world);
            setColor(-1056964609); // 设置颜色
            setRandYawPitch();
            this.setOGSize(0.3F, 0.3F);
        }
        
        public SamidareSpears(EntityLivingBase userIn) {
            super(userIn);
            setColor(-1056964609);
            setRandYawPitch();
            this.setOGSize(0.3F, 0.3F);
        }
        
        @Override
        protected void entityInit() {
            super.entityInit();
            this.dataManager.register(RAND_YAW, Float.valueOf(0.0F));
            this.dataManager.register(RAND_PITCH, Float.valueOf(0.0F));
            this.dataManager.register(COLOR, Integer.valueOf(-1056964609));
        }
        
        public void setColor(int color) {
            this.dataManager.set(COLOR, Integer.valueOf(color));
        }
        
        public int getColor() {
            return this.dataManager.get(COLOR).intValue();
        }
        
        private float getRandYaw() {
            return this.dataManager.get(RAND_YAW).floatValue();
        }
        
        private float getRandPitch() {
            return this.dataManager.get(RAND_PITCH).floatValue();
        }
        
        private void setRandYawPitch() {
            this.dataManager.set(RAND_YAW, Float.valueOf((this.rand.nextFloat() - 0.5F) * 90.0F));
            this.dataManager.set(RAND_PITCH, Float.valueOf((this.rand.nextFloat() - 0.5F) * 60.0F));
        }
        
        @Override
        public void onUpdate() {
            super.onUpdate();
            if (!isLaunched() && this.ticksInGround == 0 && !this.onGround) {
                this.rotationYaw += getRandYaw();
                this.rotationPitch += getRandPitch();
            }
        }
        
        @Override
        protected void onImpact(RayTraceResult result) {
            if (!this.world.isRemote && result.entityHit instanceof EntityLivingBase && 
                !result.entityHit.equals(this.shootingEntity)) {
                
                EntityLivingBase target = (EntityLivingBase)result.entityHit;
                target.addPotionEffect(new PotionEffect(MobEffects.NAUSEA, 200, 1, false, false));
                result.entityHit.hurtResistantTime = 0;
                result.entityHit.attackEntityFrom(ItemJutsu.causeJutsuDamage(this, this.shootingEntity).setDamageBypassesArmor(), 10.0F);
                setDead();
            }
        }
        
        @Override
        public ItemJutsu.JutsuEnum.Type getJutsuType() {
            return ItemJutsu.JutsuEnum.Type.DOTON; // 使用现有的类型
        }
        
        public static class Jutsu implements ItemJutsu.IJutsuCallback {
            @Override
            public boolean createJutsu(ItemStack stack, EntityLivingBase entity, float power) {
                Vec3d vec = entity.getLookVec();
                Vec3d vec1 = entity.getPositionEyes(1.0F).add(vec.scale(1.5D));
                
                for (int i = 0; i < (int)(power * 3.0F); i++) {
                    Vec3d vec2 = vec1.addVector(
                        entity.getRNG().nextDouble() - 0.5D, 
                        entity.getRNG().nextDouble() - 0.5D, 
                        entity.getRNG().nextDouble() - 0.5D);
                    Vec3d vec3 = vec2.add(vec);
                    createJutsu(entity.world, entity, vec2.x, vec2.y, vec2.z, vec3.x, vec3.y, vec3.z, 0.95F, 0.05F);
                }
                return true;
            }
            
            public void createJutsu(EntityLivingBase attacker, EntityLivingBase target, float power) {
                Vec3d vec1 = attacker.getPositionEyes(1.0F).add(attacker.getLookVec().scale(1.5D));
                
                for (int i = 0; i < (int)(power * 3.0F); i++) {
                    Vec3d vec2 = vec1.addVector(
                        attacker.getRNG().nextDouble() - 0.5D, 
                        attacker.getRNG().nextDouble() - 0.5D, 
                        attacker.getRNG().nextDouble() - 0.5D);
                    createJutsu(attacker.world, attacker, vec2.x, vec2.y, vec2.z, 
                        target.posX, target.posY + (target.height / 2.0F), target.posZ, 0.95F, 0.05F);
                }
            }
            
            public void createJutsu(World world, int num, double fromX, double fromY, double fromZ, 
                                  double toX, double toY, double toZ, float speed, float inaccuracy) {
                for (int i = 0; i < num; i++) {
                    createJutsu(world, (EntityLivingBase)null, fromX, fromY, fromZ, toX, toY, toZ, speed, inaccuracy);
                }
            }
            
            public void createJutsu(World world, @Nullable EntityLivingBase shooter, double fromX, double fromY, double fromZ, 
                                  double toX, double toY, double toZ, float speed, float inaccuracy) {
                // 播放音效
                SoundEvent soundEvent = SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:samidare_spear_isshiki"));
                if (soundEvent != null) {
                    world.playSound(null, fromX, fromY, fromZ, soundEvent,
                        SoundCategory.NEUTRAL, 0.8F, world.rand.nextFloat() * 0.4F + 0.8F);
                }
                
                // 创建长矛实体
                EntitySamidareSpear.SamidareSpears entity1 = (shooter != null) ? 
                    new EntitySamidareSpear.SamidareSpears(shooter) : new EntitySamidareSpear.SamidareSpears(world);
                entity1.setEntityScale(0.5F);
                entity1.setPosition(fromX, fromY, fromZ);
                entity1.shoot(toX - fromX, toY - fromY, toZ - fromZ, speed, inaccuracy);
                world.spawnEntity(entity1);
            }
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class CustomRender extends Render<SamidareSpears> {
        private static final ResourceLocation TEXTURE = new ResourceLocation("narutomod:textures/entity/samidare_spear.png");
        protected final ModelSpear model;
        
        public CustomRender(RenderManager renderManagerIn) {
            super(renderManagerIn);
            this.model = new ModelSpear();
            this.shadowSize = 0.1F;
        }
        
        @Override
        public void doRender(@Nonnull SamidareSpears entity, double x, double y, double z, float entityYaw, float pt) {
            GlStateManager.pushMatrix();
            bindEntityTexture(entity);
            float scale = entity.getEntityScale();
            GlStateManager.translate(x, y, z);
            GlStateManager.rotate(-entity.prevRotationYaw - (entity.rotationYaw - entity.prevRotationYaw) * pt, 0.0F, 1.0F, 0.0F);
            GlStateManager.rotate(entity.prevRotationPitch + (entity.rotationPitch - entity.prevRotationPitch) * pt, 1.0F, 0.0F, 0.0F);
            GlStateManager.scale(scale, scale, scale);
            GlStateManager.enableRescaleNormal();
            this.model.render(entity, 0.0F, 0.0F, -0.1F, 0.0F, 0.0F, 0.0625F);
            GlStateManager.popMatrix();
            super.doRender(entity, x, y, z, entityYaw, pt);
        }
        
        @Override
        protected ResourceLocation getEntityTexture(@Nonnull SamidareSpears entity) {
            return TEXTURE;
        }
    }
    
    @SideOnly(Side.CLIENT)
    public static class ModelSpear extends ModelBase {
        private final ModelRenderer spear;
        
        public ModelSpear() {
            textureWidth = 32;
            textureHeight = 32;
            
            spear = new ModelRenderer(this);
            spear.setRotationPoint(0.0F, 0.0F, 0.0F);
            spear.cubeList.add(new ModelBox(spear, 0, 0, -0.5F, -8.0F, -0.5F, 1, 16, 1, 0.0F, false));
            spear.cubeList.add(new ModelBox(spear, 4, 0, -1.0F, -10.0F, -1.0F, 2, 2, 2, 0.0F, false));
        }
        
        @Override
        public void render(@Nonnull Entity entity, float f, float f1, float f2, float f3, float f4, float f5) {
            spear.render(f5);
        }
        
        public void setRotationAngle(ModelRenderer modelRenderer, float x, float y, float z) {
            modelRenderer.rotateAngleX = x;
            modelRenderer.rotateAngleY = y;
            modelRenderer.rotateAngleZ = z;
        }
    }
}
