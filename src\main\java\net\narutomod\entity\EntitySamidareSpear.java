package net.narutomod.entity;

import net.narutomod.ElementsNarutomodMod;
import net.narutomod.item.ItemJutsu;
import net.minecraft.entity.EntityLivingBase;
import net.minecraft.init.MobEffects;
import net.minecraft.item.ItemStack;
import net.minecraft.network.datasync.DataParameter;
import net.minecraft.network.datasync.DataSerializers;
import net.minecraft.network.datasync.EntityDataManager;
import net.minecraft.potion.PotionEffect;
import net.minecraft.util.ResourceLocation;
import net.minecraft.util.SoundCategory;
import net.minecraft.util.SoundEvent;
import net.minecraft.util.math.RayTraceResult;
import net.minecraft.util.math.Vec3d;
import net.minecraft.world.World;
import net.minecraftforge.fml.common.event.FMLPreInitializationEvent;
import net.minecraftforge.fml.common.registry.EntityEntryBuilder;
import net.minecraftforge.fml.relauncher.Side;
import net.minecraftforge.fml.relauncher.SideOnly;

import javax.annotation.Nullable;

@ElementsNarutomodMod.ModElement.Tag
public class EntitySamidareSpear extends ElementsNarutomodMod.ModElement {
	public static final int ENTITYID = 661;

	public EntitySamidareSpear(ElementsNarutomodMod instance) {
		super(instance, 661);
	}

	@Override
	public void initElements() {
		elements.entities.add(() -> EntityEntryBuilder.create().entity(EC.class)
			.id(new ResourceLocation("narutomod", "samidare_spear"), ENTITYID)
			.name("samidare_spear").tracker(64, 3, true).build());
	}

	@Override
	public void preInit(FMLPreInitializationEvent event) {
		net.minecraftforge.fml.client.registry.RenderingRegistry.registerEntityRenderingHandler(EC.class, CustomRender::new);
	}

	public static class EC extends EntitySpear.Base {
		private static final DataParameter<Float> RAND_YAW = EntityDataManager.<Float>createKey(EC.class, DataSerializers.FLOAT);
		private static final DataParameter<Float> RAND_PITCH = EntityDataManager.<Float>createKey(EC.class, DataSerializers.FLOAT);

		public EC(World world) {
			super(world);
			setColor(-1056964609);
			setRandYawPitch();
		}

		public EC(EntityLivingBase userIn) {
			super(userIn, -1056964609);
			setRandYawPitch();
		}

		public EC(World world, EntityLivingBase userIn) {
			super(userIn, -1056964609);
			setRandYawPitch();
		}

		@Override
		protected void entityInit() {
			super.entityInit();
			this.dataManager.register(RAND_YAW, 0.0F);
			this.dataManager.register(RAND_PITCH, 0.0F);
		}

		private float getRandYaw() {
			return this.dataManager.get(RAND_YAW);
		}

		private float getRandPitch() {
			return this.dataManager.get(RAND_PITCH);
		}

		private void setRandYawPitch() {
			this.dataManager.set(RAND_YAW, (this.rand.nextFloat() - 0.5F) * 90.0F);
			this.dataManager.set(RAND_PITCH, (this.rand.nextFloat() - 0.5F) * 60.0F);
		}

		@Override
		public void onUpdate() {
			super.onUpdate();
			if (!this.onGround) {
				this.rotationYaw += getRandYaw();
				this.rotationPitch += getRandPitch();
			}
		}

		@Override
		protected void onImpact(RayTraceResult result) {
			if (!this.world.isRemote && result.entityHit instanceof EntityLivingBase &&
				!result.entityHit.equals(this.shootingEntity)) {
				((EntityLivingBase)result.entityHit).addPotionEffect(new PotionEffect(MobEffects.NAUSEA, 200, 1, false, false));
				result.entityHit.hurtResistantTime = 0;
				result.entityHit.attackEntityFrom(ItemJutsu.causeJutsuDamage(this, this.shootingEntity).setDamageBypassesArmor(), 10.0F);
				this.setDead();
			}
		}

		public static class Jutsu implements ItemJutsu.IJutsuCallback {
			@Override
			public boolean createJutsu(ItemStack stack, EntityLivingBase entity, float power) {
				Vec3d vec = entity.getLookVec();
				Vec3d vec1 = entity.getPositionEyes(1.0F).add(vec.scale(1.5D));
				for (int i = 0; i < (int)(power * 3.0F); i++) {
					Vec3d vec2 = vec1.addVector(entity.getRNG().nextDouble() - 0.5D, entity.getRNG().nextDouble() - 0.5D, entity.getRNG().nextDouble() - 0.5D);
					Vec3d vec3 = vec2.add(vec);
					createJutsu(entity.world, entity, vec2.x, vec2.y, vec2.z, vec3.x, vec3.y, vec3.z, 0.95F, 0.05F);
				}
				return true;
			}

			public void createJutsu(EntityLivingBase attacker, EntityLivingBase target, float power) {
				Vec3d vec1 = attacker.getPositionEyes(1.0F).add(attacker.getLookVec().scale(1.5D));
				for (int i = 0; i < (int)(power * 3.0F); i++) {
					Vec3d vec2 = vec1.addVector(attacker.getRNG().nextDouble() - 0.5D, attacker.getRNG().nextDouble() - 0.5D, attacker.getRNG().nextDouble() - 0.5D);
					createJutsu(attacker.world, attacker, vec2.x, vec2.y, vec2.z, target.posX, target.posY + (target.height / 2.0F), target.posZ, 0.95F, 0.05F);
				}
			}

			public void createJutsu(World world, int num, double fromX, double fromY, double fromZ, double toX, double toY, double toZ, float speed, float inaccuracy) {
				for (int i = 0; i < num; i++) {
					createJutsu(world, null, fromX, fromY, fromZ, toX, toY, toZ, speed, inaccuracy);
				}
			}

			public void createJutsu(World world, @Nullable EntityLivingBase shooter, double fromX, double fromY, double fromZ, double toX, double toY, double toZ, float speed, float inaccuracy) {
				try {
					SoundEvent soundEvent = SoundEvent.REGISTRY.getObject(new ResourceLocation("narutomod:samidare_spear_sound"));
					if (soundEvent != null) {
						world.playSound(null, fromX, fromY, fromZ, soundEvent, SoundCategory.NEUTRAL, 0.8F, world.rand.nextFloat() * 0.4F + 0.8F);
					}
				} catch (Exception e) {
					// 音效播放失败时的安全处理
				}

				EC entity1 = (shooter != null) ? new EC(shooter) : new EC(world);
				entity1.setEntityScale(0.5F);
				entity1.setPosition(fromX, fromY, fromZ);
				entity1.shoot(toX - fromX, toY - fromY, toZ - fromZ, speed, inaccuracy);
				entity1.setNoGravity(true);
				world.spawnEntity(entity1);
			}
		}
	}

	@SideOnly(Side.CLIENT)
	public static class CustomRender extends EntitySpear.Renderer<EC> {
		private final ResourceLocation TEXTURE = new ResourceLocation("narutomod:textures/entity/samidare_spear.png");

		public CustomRender(net.minecraft.client.renderer.entity.RenderManager renderManagerIn) {
			super(renderManagerIn);
		}

		@Override
		protected ResourceLocation getEntityTexture(EC entity) {
			return this.TEXTURE;
		}
	}

}
