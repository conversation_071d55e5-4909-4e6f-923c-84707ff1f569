/*    */ package net.mcreator.borutomodaddononero.procedure;
/*    */ 
/*    */ import java.util.Map;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod;
/*    */ import net.mcreator.borutomodaddononero.ElementsBorutomodaddononeroMod.ModElement.Tag;
/*    */ import net.mcreator.borutomodaddononero.entity.EntitySamidareSpear;
/*    */ import net.minecraft.entity.Entity;
/*    */ import net.minecraft.entity.EntityLivingBase;
/*    */ import net.minecraft.entity.player.EntityPlayer;
/*    */ import net.minecraft.util.ResourceLocation;
/*    */ import net.minecraft.util.SoundCategory;
/*    */ import net.minecraft.util.SoundEvent;
/*    */ import net.minecraft.world.World;
/*    */ import net.narutomod.Chakra;
/*    */ 
/*    */ @Tag
/*    */ public class ProcedureSamidareSpear extends ElementsBorutomodaddononeroMod.ModElement {
/*    */   public ProcedureSamidareSpear(ElementsBorutomodaddononeroMod instance) {
/* 19 */     super(instance, 264);
/*    */   }
/*    */   
/*    */   public static void executeProcedure(Map<String, Object> dependencies) {
/* 23 */     if (dependencies.get("is_pressed") == null) {
/* 24 */       System.err.println("Failed to load dependency is_pressed for procedure SamidareSpear!");
/*    */       return;
/*    */     } 
/* 27 */     if (dependencies.get("entity") == null) {
/* 28 */       System.err.println("Failed to load dependency entity for procedure SamidareSpear!");
/*    */       return;
/*    */     } 
/* 31 */     if (dependencies.get("world") == null) {
/* 32 */       System.err.println("Failed to load dependency world for procedure SamidareSpear!");
/*    */       
/*    */       return;
/*    */     } 
/* 36 */     boolean is_pressed = ((Boolean)dependencies.get("is_pressed")).booleanValue();
/* 37 */     Entity entity = (Entity)dependencies.get("entity");
/* 38 */     World world = (World)dependencies.get("world");
/*    */     
/* 40 */     if (is_pressed && entity instanceof EntityPlayer) {
/* 41 */       EntityPlayer player = (EntityPlayer)entity;
/* 42 */       if (Chakra.pathway(player).getAmount() >= 150.0D) {
/* 43 */         Chakra.pathway(player).consume(150.0D);
/* 44 */         world.func_184148_a((EntityPlayer)null, entity.field_70165_t, entity.field_70163_u, entity.field_70161_v, (SoundEvent)SoundEvent.field_187505_a
/*    */             
/* 46 */             .func_82594_a(new ResourceLocation("borutomodaddononero:samidare_spear")), SoundCategory.NEUTRAL, 50.0F, 1.0F);
/*    */ 
/*    */         
/* 49 */         EntitySamidareSpear.SamidareSpears.Jutsu jutsu = new EntitySamidareSpear.SamidareSpears.Jutsu();
/* 50 */         jutsu.createJutsu(player.func_184614_ca(), (EntityLivingBase)player, 5.0F);
/*    */       } 
/*    */     } 
/*    */   }
/*    */ }


/* Location:              D:\IDEA\naruto_mod-0.3.2-beta\fushu\BorutoMod Addon byAnimeSupremeTeam v0.1.0.jar!\net\mcreator\borutomodaddononero\procedure\ProcedureSamidareSpear.class
 * Java compiler version: 8 (52.0)
 * JD-Core Version:       1.1.3
 */